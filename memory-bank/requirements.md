# Car Sharing Platform Requirements

## System Overview

The Car Sharing Platform is a comprehensive mobile and web application that enables users to share rides between locations. The platform connects drivers who have available seats with passengers who need transportation along similar routes, promoting resource sharing, reducing traffic congestion, and offering cost-effective transportation alternatives.

## Core Entities

### Users

- User registration and authentication
  - Email, phone, and password-based registration
  - OTP verification for email and phone
  - Social media login integration
- User profiles
  - Personal information (name, avatar, bio, birthday, gender)
  - Contact information (email, phone)
  - Identity verification (ID card verification)
  - User preferences for trip companions (chat, music, smoking, pets)
  - Vehicle ownership indicators (has_motor, has_car)
- Account management
  - Password change functionality
  - Account deactivation with feedback collection
  - Profile updates
  - Notification preferences management

### Vehicles

- Vehicle registration and management
  - Vehicle make and model information
  - Vehicle type categorization
  - Vehicle storage capabilities
- Vehicle verification process
- Multiple vehicles per user support

### Trips

- Trip creation and management
  - Route specification (departure and destination addresses with geo-coordinates)
  - Scheduling (departure date and time)
  - Seat availability configuration
  - Pricing setup
  - Additional options (auto-accept, helmet availability)
  - Trip notes
- Trip statuses
  - OPEN: Available for bookings
  - FINISHED: Completed trips
  - CANCELLED: Cancelled by driver
  - OUTDATED: Past departure time
  - PENALTY: Administrative penalty applied
- Trip operations
  - Trip creation by drivers
  - Trip modification with restrictions based on reservation status
  - Trip cancellation with reason capture
  - Trip completion marking
  - Trip search and filtering

### Reservations

- Booking process
  - Seat quantity selection
  - Payment method selection
- Reservation management
  - Acceptance/rejection by drivers
  - Cancellation by passengers with reason capture
- Reservation statuses
  - PENDING_CONFIRM: Awaiting driver confirmation
  - ACCEPTED: Confirmed by driver
  - PAID: Payment completed
  - FINISHED: Trip completed
  - CANCELLED: Cancelled by passenger or driver
  - REJECTED: Rejected by driver
  - OUTDATED: Expired reservations

### Payments

- Payment processing
  - Multiple payment gateway support
  - Transaction tracking
- Payment statuses
  - OPEN: Payment initiated
  - PAID: Payment completed
  - CANCELLED: Payment cancelled
  - REFUNDED: Payment refunded
- Bank integration for payment processing
- Checkout process with confirmation and cancellation flows

### Communication

- In-app messaging system
  - Chat rooms between users
  - Message history
  - Real-time messaging
- Notification system
  - Trip status notifications
  - Reservation notifications
  - Payment notifications
  - System notifications
  - FCM (Firebase Cloud Messaging) integration

### Reviews and Ratings

- User review system
  - Rating submissions after trip completion
  - Rating display on user profiles
  - Review aggregation and averaging

### Content Management

- Post/article system
  - Informational content
  - News updates
  - Platform announcements
  - Content status management (active/inactive)

## Admin Features

- User management
  - User verification process (avatar and ID verification)
  - User status control
  - User information viewing
- Trip oversight
  - Trip status monitoring
  - Trip cancellation capabilities
  - Trip detail viewing
- Reservation monitoring
  - Reservation status tracking
  - Reservation detail viewing
- Payment tracking
  - Transaction monitoring
  - Payment status tracking
- Content management
  - Post creation and editing
  - Post status management

## Integration Requirements

- Google Maps API integration
  - Geocoding for address lookup
  - Distance calculation
  - Route optimization
  - Place autocomplete
  - Direction services
- Payment gateway integration
- Push notification services

## Technical Requirements

- RESTful API architecture
- Authentication via OAuth 2.0 with Laravel Passport
- Secure data storage and transmission
- Soft delete support for data integrity
- Comprehensive logging system
- Automated processes via cronjobs
  - Outdated trip handling
  - Reservation expiration
- Real-time communication capabilities

## Non-Functional Requirements

- Performance
  - Fast response times for API endpoints
  - Efficient database queries
- Security
  - Data encryption
  - Secure authentication
  - Protection against common vulnerabilities
- Scalability
  - Support for growing user base
  - Efficient resource utilization
- Reliability
  - System availability
  - Data consistency
  - Backup mechanisms
- Usability
  - Intuitive API design
  - Comprehensive error messages
  - Consistent response formats

## Mobile Application Requirements

- Cross-platform compatibility
- User-friendly interface
- Push notification support
- Location services integration
- Offline capabilities where appropriate
- Real-time updates for trips and messages

## Frontend Requirements

### Technology Stack

- Next.js 15.2 with App Router
- React 19
- TypeScript
- Tailwind CSS for styling
- Radix UI for accessible component primitives
- Ant Design for complex UI components
- React Query for server state management
- Zustand for client-side state management
- Firebase for real-time communication
- Mapbox and Google Maps for mapping features

### Core Features

- Authentication & Authorization

  - Login with email/phone and password
  - Social login integration
  - OTP verification flows
  - Password reset and recovery
  - Secure session management
  - Protected routes based on authentication status

- User Interface

  - Responsive design for all device types
  - Dark/light mode support
  - Internationalization support for Vietnamese and English
  - Accessible components following WCAG guidelines
  - Toast notifications for system feedback
  - Loading states and error boundaries

- Trip Management

  - Trip search with advanced filtering options
  - Trip creation with multi-point routes
  - Interactive map-based route selection
  - Trip detail views with map visualization
  - Trip history for both drivers and passengers
  - Related trip recommendations

- Map Integration

  - Interactive maps for trip visualization
  - Location autocomplete for addresses
  - Route visualization with polylines
  - Distance and time calculations
  - Real-time location tracking
  - Map-based trip search

- Real-time Communication

  - Chat interface between drivers and passengers
  - Message history and persistence
  - Typing indicators
  - Real-time notifications
  - File and image sharing capabilities
  - Message read receipts

- Booking System

  - Intuitive booking flow with seat selection
  - Booking status tracking
  - Booking cancellation with reason capture
  - Driver approval interface for bookings
  - Passenger management for drivers

- Payments

  - Multiple payment method support
  - Secure payment processing
  - Payment history and receipts
  - Payment status tracking
  - Refund management

- User Profile
  - Profile customization and editing
  - Vehicle management interface
  - Identity verification flows
  - Rating and review display
  - Trip history visualization
  - Account settings management

### UI Components

- Custom Form Controls

  - Input fields with validation
  - Date and time pickers
  - Autocomplete inputs for locations
  - File uploads with preview
  - Number inputs with formatting
  - Currency inputs with localization

- Map Components

  - Map visualization with custom markers
  - Route polylines with styling
  - Location search and selection
  - Map controls for zoom and navigation
  - Mobile-optimized map interaction

- Layout Components
  - Responsive navigation system
  - Modal dialogs and overlays
  - Toast notifications
  - Tabbed interfaces
  - Collapsible sections
  - Card-based content presentation

### Frontend Architecture

- Component Structure

  - Atomic design principles
  - Feature-based organization
  - Shared UI component library
  - Consistent naming conventions
  - Proper component composition

- State Management

  - Server state with React Query
  - Local state with Zustand
  - Form state with React Hook Form
  - Persistent state with local storage
  - URL state with query parameters

- Data Fetching

  - Optimistic updates for immediate feedback
  - Request caching and invalidation
  - Pagination for large datasets
  - Polling for real-time updates
  - Error handling with retry mechanisms

- Performance Optimization

  - Code splitting and lazy loading
  - Image optimization with Next.js
  - Component memoization
  - Virtual lists for large datasets
  - Prefetching critical resources
  - Efficient re-rendering strategies

- Development Practices
  - Type safety with TypeScript
  - Form validation with Zod
  - Consistent error handling
  - Comprehensive testing
  - Responsive design testing
  - Accessibility compliance

### Integration Points

- Backend API integration

  - RESTful API consumption
  - Authentication token management
  - Request/response error handling
  - Data transformation and normalization
  - Optimistic updates

- Third-party Services
  - Google Maps API integration
  - Mapbox services
  - Payment gateway integration
  - Firebase for real-time features
  - Analytics integration
  - Monitoring and error reporting
