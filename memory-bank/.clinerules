# Cline Rules for carShare Project

## Coding Standards

### Backend (<PERSON><PERSON>)
- Follow PSR-12 coding standards
- Use repository pattern for data access
- Implement service layer for business logic
- Use resource classes for API responses
- Write PHPUnit tests for all new features
- Document API endpoints with OpenAPI annotations

### Frontend (Next.js)
- Use TypeScript for all new components
- Follow functional component pattern with hooks
- Use React Query for data fetching
- Implement proper error handling for all API calls
- Write Jest tests for critical components
- Use Tailwind CSS for styling

## Naming Conventions

### Backend
- Controllers: PascalCase, suffixed with "Controller" (e.g., VehicleController)
- Models: PascalCase, singular (e.g., Vehicle)
- Migrations: snake_case, descriptive (e.g., create_vehicles_table)
- Routes: kebab-case (e.g., /api/vehicle-types)
- Database tables: snake_case, plural (e.g., vehicles)
- Database columns: snake_case (e.g., registration_number)

### Frontend
- Components: PascalCase (e.g., VehicleCard)
- Hooks: camelCase, prefixed with "use" (e.g., useVehicleSearch)
- Utilities: camelCase (e.g., formatCurrency)
- Pages: kebab-case (e.g., vehicle-details)
- State variables: camelCase (e.g., isLoading)
- Constants: UPPER_SNAKE_CASE (e.g., MAX_UPLOAD_SIZE)

## Project Structure

### Backend
- Controllers handle request/response only
- Business logic goes in service classes
- Data access through repository classes
- Use events for side effects
- Use jobs for background processing
- Keep routes organized by resource

### Frontend
- Group components by feature when possible
- Shared components in /components directory
- Page-specific components co-located with pages
- Hooks in /hooks directory
- API calls in /services directory
- Types in /types directory

## Git Workflow
- Feature branches named as feature/feature-name
- Bug fix branches named as fix/bug-description
- Use conventional commits (feat:, fix:, docs:, etc.)
- Squash commits when merging to main
- Keep PRs focused on single features or fixes

## User Experience Patterns
- Provide loading states for all async operations
- Show clear error messages for failed operations
- Implement form validation with helpful messages
- Use optimistic UI updates when appropriate
- Ensure all actions have confirmation steps
- Maintain mobile-first responsive design

## Performance Guidelines
- Optimize images before upload
- Implement pagination for all list views
- Use lazy loading for images and components
- Minimize bundle size with code splitting
- Cache frequently accessed data
- Use debounce for search inputs

## Security Practices
- Validate all input on both client and server
- Implement proper authorization checks
- Use CSRF protection for all forms
- Sanitize user-generated content
- Implement rate limiting for sensitive endpoints
- Follow least privilege principle for user roles
