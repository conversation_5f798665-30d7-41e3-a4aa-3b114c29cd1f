# Technical Context: carShare

## Technology Stack

### Backend

- **Framework**: <PERSON><PERSON> 10 (PHP)
- **Database**: MySQL
- **API**: RESTful API with JSON responses
- **Authentication**: Laravel Passport (OAuth2)
- **File Storage**: Google Cloud Storage
- **Caching**: Redis
- **Queue System**: Laravel Queue with Redis driver
- **Notifications**: <PERSON>ail (SMTP), SMS (Twilio), Firebase (Push)

### Frontend

- **Framework**: Next.js 13+ (React)
- **State Management**: React Query for server state, React Context for UI state
- **Styling**: Tailwind CSS with custom components
- **Form Handling**: React Hook Form with Zod validation
- **API Client**: Axios
- **Authentication**: JWT with secure HTTP-only cookies
- **Maps Integration**: Google Maps API

### DevOps & Infrastructure

- **Version Control**: Git (GitHub)
- **CI/CD**: GitHub Actions
- **Hosting**: <PERSON><PERSON> (Backend), Vercel (Frontend)
- **Monitoring**: Sentry for error tracking
- **Analytics**: Google Analytics, custom event tracking

## Development Environment

### Requirements

- PHP 8.1+
- Node.js 16+
- Composer
- npm or yarn
- MySQL 8.0+
- Redis

### Local Setup

1. Clone the backend repository
2. Install PHP dependencies with Composer
3. Set up environment variables
4. Run database migrations and seeders
5. Start the Laravel development server
6. Clone the frontend repository
7. Install Node.js dependencies
8. Configure environment variables
9. Start the Next.js development server

## External Services & APIs

### Payment Processing

- Stripe for payment processing
- PayPal as an alternative payment method

### Geolocation & Maps

- Google Maps API for location display and search
- Geocoding API for address validation

### Communication

- Twilio for SMS notifications
- SendGrid for transactional emails
- Firebase for push notifications

### File Storage

- Google Cloud Storage for vehicle images and documents
- Image processing and optimization pipeline

### Authentication

- Social login options (Google, Facebook)
- Two-factor authentication for enhanced security

## Testing Strategy

### Backend Testing

- PHPUnit for unit and feature tests
- API tests for endpoint validation
- Database tests for data integrity

### Frontend Testing

- Jest for unit testing React components
- React Testing Library for component testing
- Playwright for end-to-end testing
- Storybook for component documentation and visual testing

### Test Framework Structure

- **Directory Organization**:

  - `tests/`: Main test directory
  - `tests/fixtures/`: Reusable test fixtures and authentication
  - `tests/trip-steps/`: Modular steps for trip-related tests
  - `tests/utils/`: Common test utilities

- **Key Test Files**:

  - `create-trip.spec.ts`: Tests for trip creation flow
  - `edit-trip-login.spec.ts`: Tests for trip editing with authentication
  - `edit-trip-stopovers.spec.ts`: Tests for stopover editing functionality

- **Utility Modules**:

  - `common.utils.ts`: Core utilities for all tests
    - `waitForLoadingToDisappear()`: Handles loading screens
    - `safeClickNext()`: Reliable next button clicking
    - `logTestStep()`: Standardized logging
    - `getTestResultsDir()`: Test results management

- **Navigation Module**:

  - `navigation.steps.ts`: Centralized navigation functions
    - `navigateToCreateTrip()`: Trip creation navigation
    - `navigateToEditTrip()`: Trip editing navigation
    - `navigateToTripList()`: Trip listing navigation
    - `navigateToHomePage()`: Home page navigation

- **Trip Creation Steps**:

  - `departure.steps.ts`: Departure location selection
  - `destination.steps.ts`: Destination selection
  - `vehicle.steps.ts`: Vehicle selection
  - `route.steps.ts`: Route selection
  - `stopover.steps.ts`: Stopover point selection
  - `trip-details.steps.ts`: Remaining trip details

- **Test Results Management**:
  - Organized by test case name
  - Screenshots captured at key steps
  - Detailed logs with timestamps
  - Separate directories for each test run

## Deployment Pipeline

1. Code pushed to feature branch
2. Automated tests run in CI environment
3. Code review process
4. Merge to development branch
5. Deploy to staging environment
6. QA testing
7. Merge to main branch
8. Deploy to production environment
9. Post-deployment verification

## Performance Considerations

- API response time optimization
- Database query optimization
- Frontend bundle size management
- Image optimization and lazy loading
- Caching strategies for frequently accessed data
- CDN for static assets

## Security Measures

- HTTPS enforcement
- CSRF protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Rate limiting
- Data encryption for sensitive information
- Regular security audits and updates

## Monitoring & Logging

- Application logs with structured logging
- Error tracking with Sentry
- Performance monitoring
- User activity logging for security and analytics
- Database query monitoring

## Trip API Documentation

### Base URL

All API requests should use the base URL from the environment variable.

### Authentication

Most endpoints require authentication using Bearer token:

```
Authorization: Bearer {token}
```

### Endpoints

#### Get Trips List

```
GET /api/v1/trips
```

Query parameters:

- `page`: Page number (pagination)
- `cond[max_seat]`: Number of available seats
- `cond[departure_date]`: Date in format YYYY-MM-DD
- `cond[departure_city]`: City name
- `cond[departure_latitude]`: Latitude of departure point
- `cond[departure_longitude]`: Longitude of departure point
- `cond[destination_city]`: City name
- `cond[destination_latitude]`: Latitude of destination
- `cond[destination_longitude]`: Longitude of destination
- `cond[sort_by]`: Sorting criteria (e.g., "earliest-departure")
- `cond[auto_accept]`: Filter by auto-accept setting (0 or 1)
- `cond[allow_smoking]`: Filter by smoking allowance (0 or 1)
- `cond[allow_pets]`: Filter by pet allowance (0 or 1)
- `cond[vehicle_type]`: Filter by vehicle type
- `cond[has_helmet_available]`: Filter by helmet availability (0 or 1)
- `cond[is_ID_card_verified]`: Filter by ID verification status
- `cond[departure_time]`: Filter by time ranges (before-6, 6-to-12, 12-to-18, after-18)

#### Get User's Own Trips

```
GET /api/v1/trips/own
```

Query parameters:

- `page`: Page number (pagination)

_Requires authentication_

#### Get Trip Details

```
GET /api/v1/trip/{id}
```

Path parameters:

- `id`: Trip ID

Query parameters:

- `departure_latitude`: User's departure latitude
- `departure_longitude`: User's departure longitude
- `destination_latitude`: User's destination latitude
- `destination_longitude`: User's destination longitude

_Requires authentication_

#### Get Similar Trips

```
GET /api/v1/trip/similar
```

Query parameters:

- `departure_city`: City name
- `destination_city`: City name
- `departure_date`: Date in format YYYY-MM-DD
- `vehicle_type`: Type of vehicle

#### Cancel Trip

```
POST /api/v1/trips/{trip_id}/cancel
```

Path parameters:

- `trip_id`: Trip ID

_Requires authentication_

#### Complete Trip

```
POST /api/v1/trips/{trip_id}/complete
```

Path parameters:

- `trip_id`: Trip ID

_Requires authentication_

#### Create Trip

```
POST /api/v1/trips
```

Form data:

- `vehicle_id`: ID of the vehicle to use (Get from the API get vehicle info)
- `max_seat`: Maximum number of seats available
- `notes`: Additional notes about the trip
- `auto_accept`: Whether to automatically accept reservations (0 or 1) - 1: Automatically accept, 0: Manually
- `has_helmet_available`: Whether helmets are available (0 or 1) - có mang sẵn mủ bảo hiểm
- `date`: Departure date (YYYY-MM-DD)
- `time`: Departure time (HH:MM)

**For direct trips (without stops):**

- `completion_times[0]`: Time to complete entire trip in minutes
- `distances[0]`: Distance for the entire trip in meters (đơn vị: m)
- `prices[0]`: Price for the entire trip
- `overview_polyline[0]`: Encoded polyline for the entire route
- `points[0][address_name]`: Address name for origin
- `points[0][city_name]`: City name for origin
- `points[0][latitude]`: Latitude for origin
- `points[0][longitude]`: Longitude for origin
- `points[1][address_name]`: Address name for destination
- `points[1][city_name]`: City name for destination
- `points[1][latitude]`: Latitude for destination
- `points[1][longitude]`: Longitude for destination

**For trips with intermediate stops:**

- `completion_times[0]`: Time to complete main trip in minutes (if there are stops, this field isn't used)
- `completion_times[1]`: Time to complete first segment in minutes
- `completion_times[2]`: Time to complete second segment in minutes (if applicable)
- `completion_times[n]`: Time to complete nth segment in minutes
- `prices[0]`: Price for main trip (if there are stops, this field isn't used)
- `prices[1]`: Price for first segment
- `prices[2]`: Price for second segment (if applicable)
- `prices[n]`: Price for nth segment
- `distances[0]`: Distance for main trip in meters (if there are stops, this field isn't used)
- `distances[1]`: Distance for first segment in meters
- `distances[2]`: Distance for second segment in meters (if applicable)
- `distances[n]`: Distance for nth segment in meters
- `overview_polyline[0]`: If there are intermediate stops, this should be empty but still included
- `overview_polyline[1]`: Encoded polyline for first segment
- `overview_polyline[2]`: Encoded polyline for second segment (if applicable)
- `overview_polyline[n]`: Encoded polyline for nth segment
- `points[0][address_name]`: Address name for origin
- `points[0][city_name]`: City name for origin
- `points[0][latitude]`: Latitude for origin
- `points[0][longitude]`: Longitude for origin
- `points[1][address_name]`: Address name for first stop
- `points[1][city_name]`: City name for first stop
- `points[1][latitude]`: Latitude for first stop
- `points[1][longitude]`: Longitude for first stop
- `points[n][address_name]`: Address name for nth location
- `points[n][city_name]`: City name for nth location
- `points[n][latitude]`: Latitude for nth location
- `points[n][longitude]`: Longitude for nth location

_Requires authentication_

#### Update Trip

```
POST /api/v1/trip/{trip_id}/update
```

Path parameters:

- `trip_id`: Trip ID

Form data:

- `vehicle_id`: ID of the vehicle to use (Get from the API get vehicle info)
- `max_seat`: Maximum number of seats available
- `notes`: Additional notes about the trip
- `auto_accept`: Whether to automatically accept reservations (0 or 1) - 1: Automatically accept, 0: Manually
- `has_helmet_available`: Whether helmets are available (0 or 1) - có mang sẵn mủ bảo hiểm
- `date`: Departure date (YYYY-MM-DD)
- `time`: Departure time (HH:MM)

Mapping fields
| Backend Field | Frontend Field |
| ------------------------- | -------------- |
| points (stops) | stepOvers |
| overview_polyline (route) | chooseRoute |
| max_seat | seats |
| auto_accept | approval |
| price | price |
| notes | notes |

Update type:

1. **UPDATE_TYPE_ALL (Type 1)**:
   - Frontend updatable fields: chooseRoute, stepOvers, seats, approval, price, notes
   - Backend updatable fields: points, price, auto_accept, max_seat, remaining_seats, notes, overview_polyline
2. **UPDATE_TYPE_ACCEPTED (Type 2)**:
   - Frontend updatable fields: seats, approval, price, notes
   - Backend updatable fields: price, auto_accept, max_seat, remaining_seats, notes
3. **UPDATE_TYPE_PENDING (Type 3)**:
   - Only allows updating price
4. **UPDATE_TYPE_UNDEFINED (Type 0)**:
   - No updates allowed
     **For direct trips (without stops):**

- `completion_times[0]`: Time to complete entire trip in minutes
- `distances[0]`: Distance for the entire trip in meters (đơn vị: m)
- `prices[0]`: Price for the entire trip
- `overview_polyline[0]`: Encoded polyline for the entire route
- `points[0][address_name]`: Address name for origin
- `points[0][city_name]`: City name for origin
- `points[0][latitude]`: Latitude for origin
- `points[0][longitude]`: Longitude for origin
- `points[1][address_name]`: Address name for destination
- `points[1][city_name]`: City name for destination
- `points[1][latitude]`: Latitude for destination
- `points[1][longitude]`: Longitude for destination

**For trips with intermediate stops:**

- `completion_times[0]`: Time to complete main trip in minutes (if there are stops, this field isn't used)
- `completion_times[1]`: Time to complete first segment in minutes
- `completion_times[2]`: Time to complete second segment in minutes (if applicable)
- `completion_times[n]`: Time to complete nth segment in minutes
- `prices[0]`: Price for main trip (if there are stops, this field isn't used)
- `prices[1]`: Price for first segment
- `prices[2]`: Price for second segment (if applicable)
- `prices[n]`: Price for nth segment
- `distances[0]`: Distance for main trip in meters (if there are stops, this field isn't used)
- `distances[1]`: Distance for first segment in meters
- `distances[2]`: Distance for second segment in meters (if applicable)
- `distances[n]`: Distance for nth segment in meters
- `overview_polyline[0]`: If there are intermediate stops, this should be empty but still included
- `overview_polyline[1]`: Encoded polyline for first segment
- `overview_polyline[2]`: Encoded polyline for second segment (if applicable)
- `overview_polyline[n]`: Encoded polyline for nth segment
- `points[0][address_name]`: Address name for origin
- `points[0][city_name]`: City name for origin
- `points[0][latitude]`: Latitude for origin
- `points[0][longitude]`: Longitude for origin
- `points[1][address_name]`: Address name for first stop
- `points[1][city_name]`: City name for first stop
- `points[1][latitude]`: Latitude for first stop
- `points[1][longitude]`: Longitude for first stop
- `points[n][address_name]`: Address name for nth location
- `points[n][city_name]`: City name for nth location
- `points[n][latitude]`: Latitude for nth location
- `points[n][longitude]`: Longitude for nth location

_Requires authentication_

### Trip Object Structure

A trip consists of multiple points (at least origin and destination), with optional intermediate stops. Each segment has associated data:

- Price
- Distance
- Completion time
- Route (polyline)

### Trip States

Trips can be in various states:

- Active/Upcoming
- Completed
- Cancelled

### Multi-segment Trips

The API supports two types of trips:

1. **Direct trips**: Simple trips from origin to destination with no intermediate stops

   - Single segment with one route
   - Two points (origin and destination)

2. **Multi-segment trips**: Trips with one or more intermediate stops
   - Multiple segments, each with its own route data
   - Multiple points (origin, intermediate stops, destination)
   - Each segment has its own price, distance, and estimated completion time

## Testing Setup

### Tools and Libraries

- **Jest**: Primary testing framework
- **SWC**: JavaScript/TypeScript compiler used for faster test execution
- **jsdom**: Browser-like environment for running tests

### Configuration

- Jest configuration is in `jest.config.js` at the project root
- Using SWC for transformations instead of Babel
- Some test files are excluded due to JSX parsing issues
- Network tests in `ratings.test.ts` show errors but are currently passing (may need proper mocking)

### Available Test Scripts

- `yarn test`: Run all tests (some will fail)
- `yarn test:safe` / `yarn test:working`: Run only tests known to work
- `yarn test:watch`: Run tests in watch mode
- `yarn test:coverage`: Generate test coverage report

### Current Issues

- JSX parsing issues in UI component tests
- Network-related tests may need better mocking
- Configuration may need updates to fully support Next.js 13+ components

### References

- Documentation available in `TESTING.md`
- Excluded test files and patterns are listed in `jest.config.js`

## Development Environment

- Next.js
- TypeScript
- React
- Ant Design
- Radix UI
- Tailwind CSS

## Key Dependencies

- For detailed dependencies, refer to `package.json`

## Technical Constraints

- JSX parsing in test files currently has some limitations
- Need to follow proper patterns for testing React components, especially with Next.js 13+ features
