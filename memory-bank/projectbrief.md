# Project Brief: carShare

## Project Overview

carShare is a peer-to-peer car sharing platform that connects car owners with potential renters. The platform enables car owners to monetize their vehicles when not in use and provides renters with convenient access to vehicles without the need for ownership. The service aims to create a more efficient use of existing vehicles while offering an affordable alternative to traditional car rental services.

## Vision Statement

To transform the way people access and utilize vehicles by creating a trusted community marketplace that makes car sharing simple, secure, and beneficial for both owners and renters.

## Core Requirements

### User Management

- User registration and authentication with email and social login options
- Comprehensive user profiles with identity verification
- Multi-level verification system (email, phone, ID, driver's license)
- User preferences and settings management
- Rating and review system for both car owners and renters
- User reputation system based on platform activity and reviews

### Vehicle Management

- Detailed vehicle registration with type, brand, model, license plate, and color
- License plate validation and vehicle categorization (car, motorcycle)
- Multiple image upload with gallery management
- Advanced availability calendar with customizable scheduling
- Flexible pricing options (base rates, dynamic pricing, time-based variations)
- Comprehensive vehicle condition tracking and reporting
- Maintenance and mileage tracking

### Booking System

- Advanced search with location, time, and multiple filtering options
- Map-based vehicle discovery
- Streamlined booking process with seat/passenger selection
- Secure payment processing through Alepay
- Comprehensive booking management (confirmation, modification, cancellation)
- Detailed booking history and status tracking
- Flexible cancellation and late return policies

### Communication

- Real-time messaging between owners and renters
- Comprehensive notification system (bookings, payments, reminders)
- Multi-channel notifications (in-app, email, SMS, push)
- Support ticketing system with tracking
- Help center with FAQ and user guides

### Maps and Location

- Google Maps integration for vehicle location display
- Area-based vehicle search with radius filtering
- Route calculation and travel time estimation
- Pickup and drop-off point management
- Distance calculation between user and vehicles

### Multilingual Support

- Full support for Vietnamese and English languages
- Localized date, time, and currency formats
- Language switching capability

## Technical Architecture

### Backend

- Laravel 10 (PHP) RESTful API
- MySQL database with optimized queries and indexing
- OAuth2 authentication with Laravel Passport
- Redis for caching and queue management
- Google Cloud Storage for file management
- Comprehensive notification services (Email, SMS, Push)

### Frontend

- Next.js 13+ (React) with TypeScript
- Server-side rendering for critical pages
- React Query for efficient data fetching
- Tailwind CSS for responsive design
- Progressive Web App capabilities
- Google Maps API integration

### DevOps & Infrastructure

- Git (GitHub) for version control
- GitHub Actions for CI/CD
- AWS for backend hosting
- Vercel for frontend hosting
- Sentry for error tracking
- Google Analytics for user behavior analysis

## Project Goals

1. Create a user-friendly platform that simplifies peer-to-peer car sharing
2. Implement secure, reliable payment and booking systems
3. Build a scalable architecture that can handle growing user base and traffic
4. Ensure comprehensive data security and privacy compliance
5. Provide a seamless experience across all devices (desktop, tablet, mobile)
6. Establish a trusted community with strong verification and review systems
7. Optimize the platform for both Vietnamese and international users

## Success Metrics

- User registration and retention rates (target: 20% monthly growth)
- Number of active vehicle listings (target: 1000+ in first year)
- Booking completion rate (target: >85%)
- User satisfaction (target: average rating >4.5/5)
- Platform uptime (target: 99.9%)
- Average response time (target: <200ms for API requests)
- Conversion rate from search to booking (target: >5%)
- Monthly active users (target: 10,000+ by end of year)

## Timeline

This is an ongoing project with continuous development and improvement cycles. The development follows an agile methodology with two-week sprints and regular releases.

### Current Phase

Focus on core booking functionality and user experience improvements:

- Refining the vehicle search and filtering system
- Enhancing the booking flow to reduce friction
- Implementing a more robust notification system
- Improving the messaging system between owners and renters
- Optimizing mobile responsiveness across the platform

### Next Phase

- Launch enhanced messaging system
- Complete payment processing integration
- Implement review and rating system
- Optimize database performance for scaling

## Stakeholders

- Development Team: Responsible for building and maintaining the platform
- Car Owners: Primary users who list their vehicles for rent
- Renters: Users who book and use the vehicles
- Support Team: Handles customer inquiries and issues
- Business Stakeholders: Oversee project direction and success
