import { TripCard } from "@/components/features/trip/SearchResultTripCard";
import { TTripDetail } from "@/features/_common/types/trip";
import { TCreateTrip } from "@/features/travels/create-trip/types/createTrip";
import Req from "@/helper/request";
import {
  BookingRequest,
  BookingResponse,
  CancelBookingRequest,
} from "@/types/booking";
import { BookedTripsResponse, TTripRelatedReq } from "@/types/trip";
import { toQueryString } from "@/utils";
import dayjs from "dayjs";
import queryString from "query-string";
import { TTripForm } from "@/features/_common/types/trip";

export const getListVehicle = async () => {
  try {
    const result = await Req.GET(`/vehicles`);

    if (result?.success && result?.data) {
      return {
        success: true,
        message: result?.message,
        data: result?.data,
      };
    }
    return {
      success: false,
      message: result?.message,
      data: null,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error?.response?.data?.message || "Lỗi hệ thống",
      data: null,
    };
  }
};

export const createVehicle = async (params: any) => {
  try {
    const form = new FormData();
    form.append("type", params.type);
    form.append("brand", params.brand);
    form.append("model", params.model);
    form.append("license_plate_letters", params.license_plate_letters);
    form.append("license_plate_numbers", params.license_plate_numbers);
    form.append("color", params.color);
    // form.append('images', params.images)
    form.append("number_of_seats", params.number_of_seats);
    for (let i = 0; i < params.images.length; i++) {
      form.append("images[]", params.images[i]);
    }

    const response = await Req.POST(`/vehicles`, form);
    if (response?.success && response?.data) {
      return {
        success: true,
        message: response?.message,
        data: response?.data,
      };
    }

    return {
      success: false,
      message: response?.message,
      data: null,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error?.response?.data?.message || "Lỗi hệ thống",
      data: null,
    };
  }
};

export const handleCreateTrip = async (payload: TCreateTrip) => {
  console.log(payload);

  try {
    const form = new FormData();
    const autoAccept: any = payload.autoAccept ? 1 : 0;
    const hasHelmetAvailable: any = payload.hasHelmetAvailable ? 1 : 0;

    form.append("vehicle_id", payload.vehicleId.toString());
    form.append("max_seat", payload.seatAmount.toString());
    form.append("auto_accept", autoAccept);
    form.append("has_helmet_available", hasHelmetAvailable);
    form.append("notes", payload.notes);
    form.append("date", dayjs(payload.departureDate).format("YYYY-MM-DD"));
    form.append("time", payload.departureTime);
    if (payload.routes?.length > 0) {
      payload.routes.map((route: any, j: number) => {
        form.append(`completion_times[${j}]`, route.completionTime);
        form.append(`distances[${j}]`, route.distance);
        form.append(`prices[${j}]`, route.price);
        form.append(`overview_polyline[${j}]`, route.overviewPolyline);

        form.append(
          `points[${j}][address_name]`,
          route[j === 0 ? "startPoint" : "endPoint"].address_name
        );
        form.append(
          `points[${j}][city_name]`,
          route[j === 0 ? "startPoint" : "endPoint"].city
        );
        form.append(
          `points[${j}][latitude]`,
          route[j === 0 ? "startPoint" : "endPoint"].latitude
        );
        form.append(
          `points[${j}][longitude]`,
          route[j === 0 ? "startPoint" : "endPoint"].longitude
        );
        if (payload.routes.length === 1) {
          form.append(`points[1][address_name]`, route.endPoint.address_name);
          form.append(`points[1][city_name]`, route.endPoint.city);
          form.append(`points[1][latitude]`, route.endPoint.latitude);
          form.append(`points[1][longitude]`, route.endPoint.longitude);
        }
      });
    }
    return await Req.POST(`/trips`, form);
  } catch (error: any) {
    console.error("Error in handleCreateTrip:", error);
    return {
      success: false,
      message: error?.response?.data?.message || "Lỗi hệ thống",
    };
  }
};

/**
 * Hàm chung xử lý cả tạo mới và chỉnh sửa chuyến đi
 * @param payload Dữ liệu chuyến đi
 * @returns Kết quả từ API
 */
export const saveTrip = async (payload: TTripForm) => {
  try {
    const form = new FormData();
    const autoAccept: any = payload.autoAccept ? 1 : 0;
    const hasHelmetAvailable: any = payload.hasHelmetAvailable ? 1 : 0;

    form.append("vehicle_id", payload.vehicleId.toString());
    form.append("max_seat", payload.seatAmount.toString());
    form.append("auto_accept", autoAccept);
    form.append("has_helmet_available", hasHelmetAvailable);
    form.append("notes", payload.notes);
    form.append("date", dayjs(payload.departureDate).format("YYYY-MM-DD"));
    form.append("time", payload.departureTime);

    // Xử lý routes - logic tương tự handleCreateTrip
    if (payload.routes?.length > 0) {
      payload.routes.map((route: any, j: number) => {
        form.append(`completion_times[${j}]`, route.completionTime);
        form.append(`distances[${j}]`, route.distance);
        form.append(`prices[${j}]`, route.price);
        form.append(`overview_polyline[${j}]`, route.overviewPolyline);

        form.append(
          `points[${j}][address_name]`,
          route[j === 0 ? "startPoint" : "endPoint"].address_name
        );
        form.append(
          `points[${j}][city_name]`,
          route[j === 0 ? "startPoint" : "endPoint"].city
        );
        form.append(
          `points[${j}][latitude]`,
          route[j === 0 ? "startPoint" : "endPoint"].latitude
        );
        form.append(
          `points[${j}][longitude]`,
          route[j === 0 ? "startPoint" : "endPoint"].longitude
        );
        if (payload.routes.length === 1) {
          form.append(`points[1][address_name]`, route.endPoint.address_name);
          form.append(`points[1][city_name]`, route.endPoint.city);
          form.append(`points[1][latitude]`, route.endPoint.latitude);
          form.append(`points[1][longitude]`, route.endPoint.longitude);
        }
      });
    }

    // Nếu là edit, gọi POST đến /trips/idTrip
    if (payload.isEdit && payload.id) {
      // Có thể thêm trường update_type nếu cần
      if (payload.update_type) {
        form.append("update_type", payload.update_type.toString());
      }
      return await Req.POST(`/trips/${payload.id}`, form);
    }

    // Nếu là tạo mới, gọi POST đến /trips
    return await Req.POST(`/trips`, form);
  } catch (error: any) {
    console.error("Error in saveTrip:", error);
    return {
      success: false,
      message: error?.response?.data?.message || "Lỗi hệ thống",
    };
  }
};

type ReqParams = {
  max_seat: string;
  departure_date: string;
  departure_city: string;
  departure_latitude: string;
  departure_longitude: string;
  destination_city: string;
  destination_latitude: string;
  destination_longitude: string;
  sort_by: string;
};

export const getListTrip = async (
  req: ReqParams,
  rangeTimes: string[] = [],
  conveniences: string[] = []
) => {
  try {
    let params = Object.entries(req).reduce(
      (prev, [key, value]) => (prev += `&cond[${key}]=${value}`),
      ""
    );

    if (Array.isArray(conveniences)) {
      params += conveniences.reduce((prev, v) => (prev += `&cond[${v}]=1`), "");
    }

    if (Array.isArray(rangeTimes)) {
      params += rangeTimes.reduce(
        (prev, v) => (prev += `&cond[departure_time][${v}]=${v}`),
        ""
      );
    }

    if (
      Array.isArray(conveniences) &&
      conveniences.includes("has_helmet_available")
    ) {
      params += `&cond[vehicle_type]=1`; // has helmet available
    }

    const result = await Req.GET("/trips?" + params);
    if (result?.success && result?.data) {
      return result?.data;
    }
    return [];
  } catch (error) {
    console.log("getListTrip error>>", error);
    return [];
  }
};

type Req = {
  destination: string | number;
  origin: string | number;
  type_vehicle: string | number;
  stop_points?: string[];
};

export const calculateSuggestPrice = async ({
  stop_points,
  ...payload
}: Req) => {
  try {
    const stop_points_params = stop_points?.reduce(
      (prev, it, index) => `${prev}&stop_points[${index}]=${it}`,
      ""
    );

    const queryParams = queryString.stringify(payload);
    const result = await Req.GET(
      `/maps/calculate-distance?${queryParams}${
        stop_points_params ? `&${stop_points_params}` : ""
      }`
    );
    return result;
  } catch (error) {
    console.log("calculateSuggestPrice error>>", error);

    return {
      success: false,
      message: "Lỗi hệ thống",
      data: null,
    };
  }
};

export const getTripDetail = async (
  id: string | number,
  params?: string
): Promise<TTripDetail> => {
  try {
    const result = await Req.GET(`/trips/${id}?${params ?? ""}`);

    if (result?.success && result?.data) {
      return result?.data;
    }
    return {} as TTripDetail;
  } catch (error) {
    console.log("getTripDetail error>>", error);
    return {} as TTripDetail;
  }
};

export const bookingTrip = async (
  id: string | number,
  params?: BookingRequest
): Promise<BookingResponse> => {
  try {
    const result = await Req.POST(`/reservations/${id}`, params);
    console.log("🚀 ~ file: trip.ts:226 ~ result:", result);

    if (result?.success && result?.data) {
      return result?.data;
    }

    return {} as unknown as BookingResponse;
  } catch (error) {
    console.log("bookingTrip error>>", error);
    return {} as unknown as BookingResponse;
  }
};

export const getRelatedTrips = async (
  params: TTripRelatedReq
): Promise<TripCard[]> => {
  const paramsRaw = toQueryString(params);
  try {
    const result = await Req.GET(`/trips/similar?${paramsRaw}`);
    console.log("🚀 ~ getRelatedTrips ~ result:", result);
    return result.data;
  } catch (error) {
    console.log("getRelatedTrips error>>", error);
    return [] as unknown as TripCard[];
  }
};

export const getCreatedTrips = async (page?: number) => {
  try {
    const pageParam = page ? `?page=${page}` : "";
    const result = await Req.GET(`/trips/own${pageParam}`);
    if (result.success && result.data) {
      return result.data;
    }
    return [];
  } catch (error: any) {
    return [];
  }
};

export const getBookedTrips = async (
  page?: number
): Promise<BookedTripsResponse> => {
  try {
    const pageParam = page ? `?page=${page}` : "";
    const result = await Req.GET(`/reservations${pageParam}`);
    return result.data;
  } catch (error: any) {
    return {
      current_page: 1,
      data: [],
      first_page_url: "",
      from: 0,
      last_page: 1,
      last_page_url: "",
      links: [],
      next_page_url: null,
      path: "",
      per_page: 20,
      prev_page_url: null,
      to: 0,
      total: 0,
    };
  }
};

export const cancelTrip = async (
  id: string | number,
  params?: CancelBookingRequest
): Promise<void> => {
  try {
    const result = await Req.POST(`/trips/${id}/cancel`, params);

    if (result?.success && result?.data) {
      return result?.data;
    }

    return Promise.resolve(result);
  } catch (error: any) {
    return Promise.reject(error?.response?.data?.message || "Lỗi hệ thống");
  }
};

export const cancelBooking = async (
  bookingId: string | number,
  params?: CancelBookingRequest
): Promise<void> => {
  try {
    const result = await Req.POST(`/reservations/${bookingId}/cancel`, params);

    if (result?.success && result?.data) {
      return result?.data;
    }

    return Promise.resolve(result);
  } catch (error: any) {
    return Promise.reject(error?.response?.data?.message || "Lỗi hệ thống");
  }
};

export const rejectCoPassenger = async (
  bookingId: string | number,
  params?: CancelBookingRequest
): Promise<void> => {
  try {
    const result = await Req.POST(
      `/reservations/${bookingId}/reject`,
      bookingId
    );
    if (result.code !== 200) {
      return Promise.reject(result?.message ?? "Lỗi hệ thống");
    }

    return Promise.resolve(result);
  } catch (error: any) {
    return Promise.reject(error?.response?.data?.message || "Lỗi hệ thống");
  }
};
