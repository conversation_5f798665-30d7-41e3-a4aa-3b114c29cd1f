import { TAddress } from "@/types/address";
import { TRoute } from "@/types/route";
import { getRoute } from "./getRoute";

/**
 * Lấy tuyến đường cho nhiều điểm trong một hành trình
 * @param points Danh sách các điểm trên hành trình (điểm đầu, điểm dừng, điểm cuối)
 * @param isMotoBike Loại phương tiện (xe máy hay không)
 * @returns Promise<TRoute[]> Danh sách các tuyến đường giữa các điểm
 */
export const getMultiPointsRoute = async (
  points: TAddress[],
  isMotoBike?: boolean
): Promise<TRoute[]> => {
  if (!points || points.length < 2) {
    // Cần ít nhất 2 điểm để tạo tuyến đường
    return Promise.reject("Cần ít nhất 2 điể<PERSON> để tạo tuyến đường");
  }

  try {
    // Tạo danh sách các tuyến đường giữa các điểm liên tiếp
    const routes: TRoute[] = [];
    
    for (let i = 0; i < points.length - 1; i++) {
      const startPoint = points[i];
      const endPoint = points[i + 1];
      
      // Lấy tuyến đường giữa điểm hiện tại và điểm tiếp theo
      const route = await getRoute(startPoint, endPoint, !!isMotoBike);
      routes.push(route);
    }
    
    return routes;
  } catch (error) {
    console.error("Lỗi khi lấy tuyến đường đa điểm:", error);
    return Promise.reject(error);
  }
}; 