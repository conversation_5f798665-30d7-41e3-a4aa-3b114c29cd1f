import { ensureApiStopoversSelected, syncSelectedStopovers, updateStopoversSelection, createReversedTrip, validateAndUpdateStep, findSelectedVehicle } from '@/features/travels/create-trip/utils/tripProviderUtils';
import { TExtendedAddress } from '@/features/travels/create-trip/utils/tripDataUtils';
import { TVehicle } from '@/types/vehicle';
import { TTripForm } from '@/features/_common/types/trip';

// Mock TExtendedAddress để sử dụng trong test
const createMockAddress = (place_id: string, address_name: string, isSelected: boolean): TExtendedAddress => ({
  place_id,
  address_name,
  formatted_address: `${address_name}, City`,
  latitude: 0,
  longitude: 0,
  city: 'City',
  address_detail: '',
  isSelected
});

// Mock Vehicle
const createMockVehicle = (id: number, name: string): TVehicle & { id: number; name: string } => ({
  id,
  name,
  type: 1,
  brand: 'Brand',
  model: 'Model',
  license_plate: 'ABC123',
  color: 'Black',
  number_of_seats: 2
});

describe('tripProviderUtils', () => {
  // Test for ensureApiStopoversSelected
  describe('ensureApiStopoversSelected', () => {
    it('should mark API stopovers as selected', () => {
      // Arrange
      const isEdit = true;
      const stopovers: TExtendedAddress[] = [
        createMockAddress('related_trip_1', 'Stop 1', false),
        createMockAddress('stop2', 'Stop 2', false)
      ];
      const tripStopovers: TExtendedAddress[] = [];
      const setStopovers = jest.fn();
      const updateTripStopovers = jest.fn();

      // Act
      ensureApiStopoversSelected(isEdit, stopovers, tripStopovers, setStopovers, updateTripStopovers);

      // Assert
      expect(setStopovers).toHaveBeenCalledWith([
        { ...stopovers[0], isSelected: true },
        { ...stopovers[1], isSelected: false }
      ]);
      expect(updateTripStopovers).toHaveBeenCalled();
    });

    it('should not process if not in edit mode', () => {
      // Arrange
      const isEdit = false;
      const stopovers: TExtendedAddress[] = [
        createMockAddress('related_trip_1', 'Stop 1', false)
      ];
      const setStopovers = jest.fn();
      const updateTripStopovers = jest.fn();

      // Act
      ensureApiStopoversSelected(isEdit, stopovers, [], setStopovers, updateTripStopovers);

      // Assert
      expect(setStopovers).not.toHaveBeenCalled();
      expect(updateTripStopovers).not.toHaveBeenCalled();
    });
  });

  // Test for syncSelectedStopovers
  describe('syncSelectedStopovers', () => {
    it('should update trip stopovers when there are differences', () => {
      // Arrange
      const stopovers: TExtendedAddress[] = [
        createMockAddress('stop1', 'Stop 1', true),
        createMockAddress('stop2', 'Stop 2', false)
      ];
      const tripStopovers: TExtendedAddress[] = [
        createMockAddress('stop3', 'Stop 3', true)
      ];
      const updateTripStopovers = jest.fn();

      // Act
      syncSelectedStopovers(stopovers, tripStopovers, updateTripStopovers);

      // Assert
      expect(updateTripStopovers).toHaveBeenCalledWith([
        stopovers[0] // Chỉ được chọn stop1
      ]);
    });

    it('should not update when there are no differences', () => {
      // Arrange
      const stopovers: TExtendedAddress[] = [
        createMockAddress('stop1', 'Stop 1', true),
        createMockAddress('stop2', 'Stop 2', false)
      ];
      const tripStopovers: TExtendedAddress[] = [
        createMockAddress('stop1', 'Stop 1', true)
      ];
      const updateTripStopovers = jest.fn();

      // Act
      syncSelectedStopovers(stopovers, tripStopovers, updateTripStopovers);

      // Assert
      expect(updateTripStopovers).not.toHaveBeenCalled();
    });
  });

  // Test for updateStopoversSelection
  describe('updateStopoversSelection', () => {
    it('should update isSelected flags based on selected stopovers', () => {
      // Arrange
      const stopovers: TExtendedAddress[] = [
        createMockAddress('stop1', 'Stop 1', false),
        createMockAddress('stop2', 'Stop 2', false),
        createMockAddress('stop3', 'Stop 3', true)
      ];
      const selectedStopovers: TExtendedAddress[] = [
        createMockAddress('stop1', 'Stop 1', true),
        createMockAddress('stop3', 'Stop 3', true)
      ];

      // Act
      const result = updateStopoversSelection(stopovers, selectedStopovers);

      // Assert
      expect(result[0].isSelected).toBe(true);
      expect(result[1].isSelected).toBe(false);
      expect(result[2].isSelected).toBe(true);
    });
  });

  // Test for createReversedTrip
  describe('createReversedTrip', () => {
    it('should swap pickup and dropOff locations', () => {
      // Arrange
      const originAddress = createMockAddress('origin', 'Origin', false);
      const destinationAddress = createMockAddress('destination', 'Destination', false);
      const mockVehicle = createMockVehicle(123, 'Car');
      
      const trip: Partial<TTripForm> = {
        pickup: originAddress,
        dropOff: destinationAddress,
        vehicleId: 123,
        vehicleSelected: mockVehicle,
        seatPrice: 10,
        seatAmount: 2,
        notes: 'Test notes'
      };

      // Act
      const result = createReversedTrip(trip);

      // Assert
      expect(result.pickup).toEqual(destinationAddress);
      expect(result.dropOff).toEqual(originAddress);
      expect(result.vehicleId).toBe(123);
    });
  });

  // Test for validateAndUpdateStep
  describe('validateAndUpdateStep', () => {
    it('should return valid step when it exists in available steps', () => {
      // Mock the getStepperConfig (imported in the implementation)
      jest.mock('./stepperUtils', () => ({
        getStepperConfig: () => [{ step: 1 }, { step: 2 }, { step: 3 }]
      }));

      // Act - assuming 2 is a valid step in the mock
      const result = validateAndUpdateStep(2, true, 0, 1);

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.validStep).toBe(2);
    });
  });

  // Test for findSelectedVehicle
  describe('findSelectedVehicle', () => {
    it('should find a vehicle by id', () => {
      // Arrange
      const vehicles = [
        { id: 123, name: 'Car 1' },
        { id: 456, name: 'Car 2' }
      ];

      // Act
      const result = findSelectedVehicle(vehicles, 123);

      // Assert
      expect(result).toEqual({ id: 123, name: 'Car 1' });
    });

    it('should return null when vehicle not found', () => {
      // Arrange
      const vehicles = [
        { id: 123, name: 'Car 1' },
        { id: 456, name: 'Car 2' }
      ];

      // Act
      const result = findSelectedVehicle(vehicles, 789);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when vehicleId is undefined', () => {
      // Arrange
      const vehicles = [
        { id: 123, name: 'Car 1' },
        { id: 456, name: 'Car 2' }
      ];

      // Act
      const result = findSelectedVehicle(vehicles, undefined);

      // Assert
      expect(result).toBeNull();
    });
  });
}); 