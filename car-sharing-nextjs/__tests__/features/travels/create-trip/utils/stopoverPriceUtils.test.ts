import {
  T<PERSON><PERSON>,
  TApiBasedRoute,
  filterSelectedStopovers,
  filterMainRoute,
  convertPricesToRoutes,
  orderStopPricesByStopoverOrder,
  parseCoordinates,
  prepareDefaultPrices,
  createFullRoute,
  formatDuration
} from '@/features/travels/create-trip/utils/stopoverPriceUtils';

describe('stopoverPriceUtils', () => {
  describe('filterSelectedStopovers', () => {
    it('should return empty array when input is empty or invalid', () => {
      expect(filterSelectedStopovers([])).toEqual([]);
      expect(filterSelectedStopovers(null as any)).toEqual([]);
      expect(filterSelectedStopovers(undefined as any)).toEqual([]);
    });

    it('should filter stopovers by isSelected flag', () => {
      const stopovers = [
        { place_id: '1', isSelected: true, address_name: 'Location 1' },
        { place_id: '2', isSelected: false, address_name: 'Location 2' },
        { place_id: '3', isSelected: true, address_name: 'Location 3' }
      ] as any[];

      const result = filterSelectedStopovers(stopovers);
      expect(result).toHaveLength(2);
      expect(result[0].place_id).toBe('1');
      expect(result[1].place_id).toBe('3');
    });
  });

  describe('orderStopPricesByStopoverOrder', () => {
    it('should return empty array when inputs are empty', () => {
      expect(orderStopPricesByStopoverOrder([], [])).toEqual([]);
      expect(orderStopPricesByStopoverOrder(null as any, [])).toEqual([]);
      expect(orderStopPricesByStopoverOrder([], null as any)).toEqual([]);
    });

    it('should order stopPrices according to stopover order', () => {
      const stopPrices: TPrice[] = [
        {
          start_location: '10.456,106.789',
          end_location: '11.789,107.123',
          start_address: 'Stop B',
          end_address: 'Stop C',
          price_max: 20000,
          price_min: 10000,
          price_avg: 15000,
          price_suggest_from: 12000,
          price_suggest_to: 18000
        },
        {
          start_location: '10.123,106.456',
          end_location: '10.456,106.789',
          start_address: 'Stop A',
          end_address: 'Stop B',
          price_max: 15000,
          price_min: 5000,
          price_avg: 10000,
          price_suggest_from: 8000,
          price_suggest_to: 12000
        }
      ];

      const orderedStopovers = [
        { latitude: 10.123, longitude: 106.456, address_name: 'Stop A' },
        { latitude: 10.456, longitude: 106.789, address_name: 'Stop B' },
        { latitude: 11.789, longitude: 107.123, address_name: 'Stop C' }
      ] as any[];

      const result = orderStopPricesByStopoverOrder(stopPrices, orderedStopovers);

      expect(result).toHaveLength(2);
      // First route should be A->B (originally second in stopPrices)
      expect(result[0].start_address).toBe('Stop A');
      expect(result[0].end_address).toBe('Stop B');
      // Second route should be B->C (originally first in stopPrices)
      expect(result[1].start_address).toBe('Stop B');
      expect(result[1].end_address).toBe('Stop C');
    });

    it('should handle missing matches gracefully', () => {
      const stopPrices: TPrice[] = [
        {
          start_location: '10.123,106.456',
          end_location: '10.456,106.789',
          price_max: 15000,
          price_min: 5000,
          price_avg: 10000,
          price_suggest_from: 8000,
          price_suggest_to: 12000
        }
      ];

      const orderedStopovers = [
        { latitude: 20.123, longitude: 106.456, address_name: 'Different Stop A' },
        { latitude: 20.456, longitude: 106.789, address_name: 'Different Stop B' }
      ] as any[];

      const result = orderStopPricesByStopoverOrder(stopPrices, orderedStopovers);
      expect(result).toHaveLength(0); // No matches found
    });
  });

  describe('filterMainRoute', () => {
    it('should return empty array when input is empty', () => {
      expect(filterMainRoute([])).toEqual([]);
      expect(filterMainRoute(null as any)).toEqual([]);
    });

    it('should return all prices when no stopovers provided', () => {
      const stopPrices: TPrice[] = [
        {
          start_location: '10.123,106.456',
          end_location: '11.789,107.123',
          price_max: 30000,
          price_min: 10000,
          price_avg: 20000,
          price_suggest_from: 15000,
          price_suggest_to: 25000
        }
      ];

      const result = filterMainRoute(stopPrices);
      expect(result).toEqual(stopPrices);
    });

    it('should filter out the main route and keep intermediate routes', () => {
      const stopPrices: TPrice[] = [
        {
          start_location: '10.123,106.456',
          end_location: '11.789,107.123',
          start_address: 'Start',
          end_address: 'End',
          price_max: 30000,
          price_min: 10000,
          price_avg: 20000,
          price_suggest_from: 15000,
          price_suggest_to: 25000
        },
        {
          start_location: '10.123,106.456',
          end_location: '10.456,106.789',
          start_address: 'Start',
          end_address: 'Middle',
          price_max: 15000,
          price_min: 5000,
          price_avg: 10000,
          price_suggest_from: 8000,
          price_suggest_to: 12000
        },
        {
          start_location: '10.456,106.789',
          end_location: '11.789,107.123',
          start_address: 'Middle',
          end_address: 'End',
          price_max: 18000,
          price_min: 8000,
          price_avg: 13000,
          price_suggest_from: 11000,
          price_suggest_to: 15000
        }
      ];

      const selectedStopovers = [
        { latitude: 10.123, longitude: 106.456, address_name: 'Start' },
        { latitude: 10.456, longitude: 106.789, address_name: 'Middle' },
        { latitude: 11.789, longitude: 107.123, address_name: 'End' }
      ] as any[];

      const result = filterMainRoute(stopPrices, selectedStopovers);

      // Should filter out the main route (first item: 10.123,106.456 -> 11.789,107.123)
      // and keep the intermediate routes
      expect(result).toHaveLength(2);
      expect(result[0].start_location).toBe('10.123,106.456');
      expect(result[0].end_location).toBe('10.456,106.789');
      expect(result[1].start_location).toBe('10.456,106.789');
      expect(result[1].end_location).toBe('11.789,107.123');
    });
  });

  describe('parseCoordinates', () => {
    it('should parse valid coordinates', () => {
      expect(parseCoordinates('10.123,106.456')).toEqual([10.123, 106.456]);
    });

    it('should handle coordinates with spaces', () => {
      expect(parseCoordinates('10.123, 106.456')).toEqual([10.123, 106.456]);
    });

    it('should throw error for missing coordinates', () => {
      expect(() => parseCoordinates('')).toThrow('Missing coordinates');
      expect(() => parseCoordinates(null as any)).toThrow('Missing coordinates');
      expect(() => parseCoordinates(undefined as any)).toThrow('Missing coordinates');
    });

    it('should throw error for invalid coordinate format', () => {
      expect(() => parseCoordinates('invalid')).toThrow('Invalid coordinates format');
      expect(() => parseCoordinates('10.123')).toThrow('Invalid coordinates format');
      expect(() => parseCoordinates('10.123,abc')).toThrow('Invalid coordinates format');
    });
  });

  describe('convertPricesToRoutes', () => {
    it('should return empty array when input is empty', () => {
      expect(convertPricesToRoutes([])).toEqual([]);
      expect(convertPricesToRoutes(null as any)).toEqual([]);
    });

    it('should convert prices to routes correctly', () => {
      const stopPrices: TPrice[] = [
        {
          start_location: '10.123,106.456',
          end_location: '10.456,106.789',
          start_address: 'Start Address 1',
          end_address: 'End Address 1',
          price_max: 15000,
          price_min: 5000,
          price_avg: 10000,
          price_suggest_from: 8000,
          price_suggest_to: 12000,
          distance: 10,
          duration: 3600
        }
      ];

      try {
        const result = convertPricesToRoutes(stopPrices);
        expect(result).toHaveLength(1);
        expect(result[0].startPoint.latitude).toBe(10.123);
        expect(result[0].startPoint.longitude).toBe(106.456);
        expect(result[0].startPoint.address_name).toBe('Start Address 1');
        expect(result[0].endPoint.latitude).toBe(10.456);
        expect(result[0].endPoint.longitude).toBe(106.789);
        expect(result[0].endPoint.address_name).toBe('End Address 1');
        expect(result[0].price).toBe(10000);
        expect(result[0].distance).toBe(10);
        expect(result[0].duration).toBe(3600);
      } catch (error) {
        fail('Should not throw an error');
      }
    });

    it('should use default address when address is missing', () => {
      const stopPrices: TPrice[] = [
        {
          start_location: '10.123,106.456',
          end_location: '10.456,106.789',
          price_max: 15000,
          price_min: 5000,
          price_avg: 10000,
          price_suggest_from: 8000,
          price_suggest_to: 12000
        }
      ];

      try {
        const result = convertPricesToRoutes(stopPrices);
        expect(result[0].startPoint.address_name).toBe('Địa chỉ không xác định');
        expect(result[0].endPoint.address_name).toBe('Địa chỉ không xác định');
      } catch (error) {
        fail('Should not throw an error');
      }
    });

    it('should throw error for missing location data', () => {
      const stopPrices: TPrice[] = [
        {
          start_location: '',
          end_location: '10.456,106.789',
          price_max: 15000,
          price_min: 5000,
          price_avg: 10000,
          price_suggest_from: 8000,
          price_suggest_to: 12000
        }
      ];

      expect(() => convertPricesToRoutes(stopPrices)).toThrow('Missing location data');
    });
  });

  describe('prepareDefaultPrices', () => {
    const apiBasedRoutes: TApiBasedRoute[] = [
      {
        startPoint: { latitude: 10.123, longitude: 106.456, address_name: 'Start 1' },
        endPoint: { latitude: 10.456, longitude: 106.789, address_name: 'End 1' },
        price: 10000,
        distance: 10,
        duration: 3600
      },
      {
        startPoint: { latitude: 10.456, longitude: 106.789, address_name: 'Start 2' },
        endPoint: { latitude: 11.789, longitude: 107.123, address_name: 'End 2' },
        price: 15000,
        distance: 15,
        duration: 5400
      }
    ];

    const stopPrices: TPrice[] = [
      {
        start_location: '10.123,106.456',
        end_location: '10.456,106.789',
        price_max: 15000,
        price_min: 5000,
        price_avg: 10000,
        price_suggest_from: 8000,
        price_suggest_to: 12000
      },
      {
        start_location: '10.456,106.789',
        end_location: '11.789,107.123',
        price_max: 20000,
        price_min: 10000,
        price_avg: 15000,
        price_suggest_from: 12000,
        price_suggest_to: 18000
      }
    ];

    it('should return empty array when no routes or prices', () => {
      expect(prepareDefaultPrices(undefined, [], [])).toEqual([]);
      expect(prepareDefaultPrices(undefined, null as any, apiBasedRoutes)).toEqual([]);
      expect(prepareDefaultPrices(undefined, stopPrices, [])).toEqual([]);
    });

    it('should use existing stopover prices when available', () => {
      const tripStopoverPrices = [12000, 18000];
      const result = prepareDefaultPrices(tripStopoverPrices, stopPrices, apiBasedRoutes);
      expect(result).toEqual([12000, 18000]);
    });

    it('should use API prices when no existing prices', () => {
      const result = prepareDefaultPrices(undefined, stopPrices, apiBasedRoutes);
      expect(result).toEqual([10000, 15000]);
    });

    it('should extend prices if there are not enough', () => {
      const tripStopoverPrices = [12000];
      const result = prepareDefaultPrices(tripStopoverPrices, stopPrices, apiBasedRoutes);
      expect(result).toEqual([12000, 15000]);
    });

    it('should truncate prices if there are too many', () => {
      const tripStopoverPrices = [12000, 18000, 20000];
      const result = prepareDefaultPrices(tripStopoverPrices, stopPrices, apiBasedRoutes);
      expect(result).toEqual([12000, 18000]);
    });
  });

  describe('createFullRoute', () => {
    it('should create a full route with all required fields', () => {
      const routeData = {
        startPoint: { latitude: 10.123, longitude: 106.456, address_name: 'Start' },
        endPoint: { latitude: 10.456, longitude: 106.789, address_name: 'End' },
        distance: 10,
        duration: 3600
      };

      const result = createFullRoute(routeData, 10000);
      expect(result).toEqual({
        startPoint: routeData.startPoint,
        endPoint: routeData.endPoint,
        price: 10000,
        overviewPolyline: '',
        distance: 10,
        completionTime: 0,
        duration: 3600
      });
    });

    it('should use route price when price parameter is 0', () => {
      const routeData = {
        startPoint: { latitude: 10.123, longitude: 106.456 },
        endPoint: { latitude: 10.456, longitude: 106.789 },
        price: 5000
      };

      const result = createFullRoute(routeData, 0);
      expect(result.price).toBe(5000);
    });

    it('should use 0 when both prices are missing', () => {
      const routeData = {
        startPoint: { latitude: 10.123, longitude: 106.456 },
        endPoint: { latitude: 10.456, longitude: 106.789 }
      };

      const result = createFullRoute(routeData, 0);
      expect(result.price).toBe(0);
    });
  });

  describe('formatDuration', () => {
    it('should return empty string for undefined or 0 seconds', () => {
      expect(formatDuration(undefined)).toBe('');
      expect(formatDuration(0)).toBe('');
    });

    it('should format hours only when no minutes', () => {
      expect(formatDuration(3600)).toBe('1h');
      expect(formatDuration(7200)).toBe('2h');
    });

    it('should format hours and minutes', () => {
      expect(formatDuration(3660)).toBe('1h 1p');
      expect(formatDuration(5430)).toBe('1h 30p');
    });
  });
});