import { TAddress } from "@/types/address";
import {
  addStopoverToList,
  areStopoverListsEqual,
  filterSelectedStopovers,
  isDuplicateStopover,
  isStopoverExistInList,
  updateStopoverSelection,
} from "@/features/travels/create-trip/utils/stopoverUtils";
import { TExtendedAddress } from "@/features/travels/create-trip/utils/tripDataUtils";

describe("stopoverUtils", () => {
  // Mock data
  const mockStopover1: Partial<TExtendedAddress> = {
    place_id: "place1",
    city: "Hà Nội",
    latitude: 21.0278,
    longitude: 105.8342,
    formatted_address: "Hà Nội, Việt Nam",
    address_name: "Hà Nội",
    isSelected: true,
  };

  const mockStopover2: Partial<TExtendedAddress> = {
    place_id: "place2",
    city: "Hồ Chí Minh",
    latitude: 10.8231,
    longitude: 106.6297,
    formatted_address: "<PERSON><PERSON>, V<PERSON><PERSON><PERSON> Nam",
    address_name: "<PERSON><PERSON>",
    isSelected: false,
  };

  const mockStopovers = [mockStopover1, mockStopover2] as TExtendedAddress[];

  describe("isStopoverExistInList", () => {
    it("should return true if stopover exists by place_id", () => {
      const newStopover: Partial<TAddress> = {
        place_id: "place1",
        city: "Test City",
        latitude: 0,
        longitude: 0,
        formatted_address: "Test Address",
        address_name: "Test",
      };
      expect(isStopoverExistInList(newStopover as TAddress, mockStopovers)).toBe(true);
    });

    it("should return true if stopover exists by coordinates", () => {
      const newStopover: Partial<TAddress> = {
        place_id: "new_place",
        city: "Hà Nội",
        latitude: 21.0278,
        longitude: 105.8342,
        formatted_address: "Hà Nội, Việt Nam",
        address_name: "Hà Nội",
      };
      expect(isStopoverExistInList(newStopover as TAddress, mockStopovers)).toBe(true);
    });

    it("should return false if stopover does not exist", () => {
      const newStopover: Partial<TAddress> = {
        place_id: "new_place",
        city: "Đà Nẵng",
        latitude: 16.0544,
        longitude: 108.2022,
        formatted_address: "Đà Nẵng, Việt Nam",
        address_name: "Đà Nẵng",
      };
      expect(isStopoverExistInList(newStopover as TAddress, mockStopovers)).toBe(false);
    });
  });

  describe("filterSelectedStopovers", () => {
    it("should filter stopovers with isSelected=true", () => {
      const result = filterSelectedStopovers(mockStopovers);
      expect(result).toHaveLength(1);
      expect(result[0].place_id).toBe("place1");
    });

    it("should return empty array when no stopovers are selected", () => {
      const unselectedStopovers = [
        { ...mockStopover1, isSelected: false },
        { ...mockStopover2, isSelected: false },
      ] as TExtendedAddress[];
      const result = filterSelectedStopovers(unselectedStopovers);
      expect(result).toHaveLength(0);
    });
  });

  describe("updateStopoverSelection", () => {
    it("should toggle isSelected for matching stopover", () => {
      const result = updateStopoverSelection(mockStopovers, mockStopover1 as TAddress);
      expect(result[0].isSelected).toBe(false);
      expect(result[1].isSelected).toBe(false);
    });

    it("should not change other stopovers", () => {
      const result = updateStopoverSelection(mockStopovers, mockStopover2 as TAddress);
      expect(result[0].isSelected).toBe(true);
      expect(result[1].isSelected).toBe(true);
    });
  });

  describe("addStopoverToList", () => {
    it("should add stopover to list with isSelected=true", () => {
      const newStopover: Partial<TAddress> = {
        place_id: "place3",
        city: "Đà Nẵng",
        latitude: 16.0544,
        longitude: 108.2022,
        formatted_address: "Đà Nẵng, Việt Nam",
        address_name: "Đà Nẵng",
      };
      
      const result = addStopoverToList(newStopover as TAddress, mockStopovers);
      expect(result).toHaveLength(3);
      expect(result[2].place_id).toBe("place3");
      expect(result[2].isSelected).toBe(true);
    });
  });

  describe("isDuplicateStopover", () => {
    const duplicateStopover: Partial<TAddress> = {
      ...mockStopover1,
      city: "Duplicate",
    };
    
    const duplicateStopovers = [
      mockStopover1,
      mockStopover2,
      duplicateStopover
    ] as TAddress[];

    it("should return true for duplicate stopover", () => {
      expect(isDuplicateStopover(duplicateStopovers[2], 2, duplicateStopovers)).toBe(true);
    });

    it("should return false for unique stopover", () => {
      expect(isDuplicateStopover(mockStopover1 as TAddress, 0, mockStopovers)).toBe(false);
    });
  });

  describe("areStopoverListsEqual", () => {
    it("should return true for identical lists", () => {
      expect(areStopoverListsEqual(mockStopovers, [...mockStopovers])).toBe(true);
    });

    it("should return false for different lists", () => {
      const newStopover: Partial<TExtendedAddress> = {
        place_id: "place3",
        city: "Đà Nẵng",
        latitude: 16.0544,
        longitude: 108.2022,
        formatted_address: "Đà Nẵng, Việt Nam",
        address_name: "Đà Nẵng",
        isSelected: true,
      };
      
      const differentList = [...mockStopovers, newStopover as TExtendedAddress];
      expect(areStopoverListsEqual(mockStopovers, differentList)).toBe(false);
    });
  });
}); 