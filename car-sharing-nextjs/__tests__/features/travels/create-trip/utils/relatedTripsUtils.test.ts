import { 
  isSameLocation, 
  isStopoverExistInList, 
  relatedTripToAddress, 
  extractStopoversFromRelatedTrips,
  updateStopoversWithRelatedTrips
} from '@/features/travels/create-trip/utils/relatedTripsUtils';
import { TExtendedAddress } from '@/features/travels/create-trip/utils/tripDataUtils';
import { TAddress } from '@/types/address';

describe('relatedTripsUtils', () => {
  describe('isSameLocation', () => {
    it('should return true for same coordinates', () => {
      const loc1 = { latitude: 10.7374393, longitude: 106.6776839 };
      const loc2 = { latitude: 10.7374393, longitude: 106.6776839 };
      expect(isSameLocation(loc1, loc2)).toBe(true);
    });

    it('should return true for very close coordinates', () => {
      const loc1 = { latitude: 10.7374393, longitude: 106.6776839 };
      const loc2 = { latitude: 10.7374392, longitude: 106.6776838 };
      expect(isSameLocation(loc1, loc2)).toBe(true);
    });

    it('should return false for different coordinates', () => {
      const loc1 = { latitude: 10.7374393, longitude: 106.6776839 };
      const loc2 = { latitude: 10.8529606, longitude: 107.1073189 };
      expect(isSameLocation(loc1, loc2)).toBe(false);
    });

    it('should handle string coordinates', () => {
      const loc1 = { latitude: '10.7374393', longitude: '106.6776839' };
      const loc2 = { latitude: 10.7374393, longitude: 106.6776839 };
      expect(isSameLocation(loc1, loc2)).toBe(true);
    });
  });

  describe('isStopoverExistInList', () => {
    const stopovers: TAddress[] = [
      {
        place_id: '1',
        address_name: 'HCM City',
        formatted_address: 'HCM City',
        city: 'HCM',
        latitude: 10.7374393,
        longitude: 106.6776839
      },
      {
        place_id: '2',
        address_name: 'Dong Nai',
        formatted_address: 'Dong Nai',
        city: 'Dong Nai',
        latitude: 10.8529606,
        longitude: 107.1073189
      }
    ];

    it('should find existing stopover by coordinates', () => {
      const stopover: TAddress = {
        place_id: '3', // Different ID
        address_name: 'Different Name',
        formatted_address: 'Different Name',
        city: 'HCM',
        latitude: 10.7374393, // Same coordinates as the first one
        longitude: 106.6776839
      };
      expect(isStopoverExistInList(stopover, stopovers)).toBe(true);
    });

    it('should not find non-existing stopover', () => {
      const stopover: TAddress = {
        place_id: '3',
        address_name: 'Phu Yen',
        formatted_address: 'Phu Yen',
        city: 'Phu Yen',
        latitude: 13.0361358,
        longitude: 109.31356
      };
      expect(isStopoverExistInList(stopover, stopovers)).toBe(false);
    });
  });

  describe('relatedTripToAddress', () => {
    it('should convert RelatedTrip to TExtendedAddress', () => {
      const relatedTrip = {
        id: 593,
        is_parent_route: 0,
        price: '581000.00',
        overview_polyline: 'some-polyline',
        completion_time: 3845,
        distance: 55317,
        departure_address: 'HCM',
        departure_city: 'Ho Chi Minh',
        departure_latitude: '10.7374393',
        departure_longitude: '106.6776839',
        destination_address: 'Dong Nai',
        destination_city: 'Dong Nai',
        destination_latitude: '10.8529606',
        destination_longitude: '107.1073189'
      };

      const result = relatedTripToAddress(relatedTrip);
      
      expect(result).toEqual({
        place_id: 'related_trip_593',
        address_name: 'Dong Nai',
        formatted_address: 'Dong Nai',
        city: 'Dong Nai',
        latitude: 10.8529606,
        longitude: 107.1073189,
        address_detail: JSON.stringify({ city: 'Dong Nai' }),
        isSelected: true
      });
    });
  });

  describe('extractStopoversFromRelatedTrips', () => {
    const originPoint: TAddress = {
      place_id: 'origin',
      address_name: 'HCM City',
      formatted_address: 'HCM City',
      city: 'HCM',
      latitude: 10.7374393,
      longitude: 106.6776839
    };

    const destinationPoint: TAddress = {
      place_id: 'destination',
      address_name: 'Ha Noi',
      formatted_address: 'Ha Noi',
      city: 'Ha Noi',
      latitude: 21.0321918,
      longitude: 105.8456782
    };

    const relatedTrips = [
      {
        id: 592,
        is_parent_route: 1, // Parent route should be filtered out
        price: '815000.00',
        overview_polyline: 'polyline1',
        completion_time: 99494,
        distance: 1673300,
        departure_address: 'HCM',
        departure_city: 'Ho Chi Minh',
        departure_latitude: '10.7374393',
        departure_longitude: '106.6776839',
        destination_address: 'Ha Noi',
        destination_city: 'Ha Noi',
        destination_latitude: '21.0321918',
        destination_longitude: '105.8456782'
      },
      {
        id: 593,
        is_parent_route: 0,
        price: '581000.00',
        overview_polyline: 'polyline2',
        completion_time: 3845,
        distance: 55317,
        departure_address: 'HCM',
        departure_city: 'Ho Chi Minh',
        departure_latitude: '10.7374393',
        departure_longitude: '106.6776839',
        destination_address: 'Dong Nai',
        destination_city: 'Dong Nai',
        destination_latitude: '10.8529606',
        destination_longitude: '107.1073189'
      },
      {
        id: 594,
        is_parent_route: 0,
        price: '212000.00',
        overview_polyline: 'polyline3',
        completion_time: 492307,
        distance: 440835,
        departure_address: 'Dong Nai',
        departure_city: 'Dong Nai',
        departure_latitude: '10.8529606',
        departure_longitude: '107.1073189',
        destination_address: 'Phu Yen',
        destination_city: 'Phu Yen',
        destination_latitude: '13.0361358',
        destination_longitude: '109.31356'
      }
    ];

    it('should extract valid stopovers from related trips', () => {
      const result = extractStopoversFromRelatedTrips(relatedTrips, originPoint, destinationPoint);
      
      // Should extract 2 stopovers (excluding parent route and duplicates)
      expect(result.length).toBe(2);
      
      // Check that Dong Nai is included
      expect(result.some(stop => stop.city === 'Dong Nai')).toBe(true);
      
      // Check that Phu Yen is included
      expect(result.some(stop => stop.city === 'Phu Yen')).toBe(true);
      
      // All stopovers should be marked as selected
      expect(result.every(stop => stop.isSelected)).toBe(true);
    });

    it('should handle empty related trips', () => {
      const result = extractStopoversFromRelatedTrips([], originPoint, destinationPoint);
      expect(result).toEqual([]);
    });
  });

  describe('updateStopoversWithRelatedTrips', () => {
    const originPoint: TAddress = {
      place_id: 'origin',
      address_name: 'HCM City',
      formatted_address: 'HCM City',
      city: 'HCM',
      latitude: 10.7374393,
      longitude: 106.6776839
    };

    const destinationPoint: TAddress = {
      place_id: 'destination',
      address_name: 'Ha Noi',
      formatted_address: 'Ha Noi',
      city: 'Ha Noi',
      latitude: 21.0321918,
      longitude: 105.8456782
    };

    const currentStopovers: TAddress[] = [
      {
        place_id: 'existing1',
        address_name: 'Some Existing Place',
        formatted_address: 'Some Existing Place',
        city: 'Some City',
        latitude: 15.123456,
        longitude: 108.654321
      }
    ];

    const relatedTrips = [
      {
        id: 592,
        is_parent_route: 1, // Parent route should be filtered out
        price: '815000.00',
        overview_polyline: 'polyline1',
        completion_time: 99494,
        distance: 1673300,
        departure_address: 'HCM',
        departure_city: 'Ho Chi Minh',
        departure_latitude: '10.7374393',
        departure_longitude: '106.6776839',
        destination_address: 'Ha Noi',
        destination_city: 'Ha Noi',
        destination_latitude: '21.0321918',
        destination_longitude: '105.8456782'
      },
      {
        id: 593,
        is_parent_route: 0,
        price: '581000.00',
        overview_polyline: 'polyline2',
        completion_time: 3845,
        distance: 55317,
        departure_address: 'HCM',
        departure_city: 'Ho Chi Minh',
        departure_latitude: '10.7374393',
        departure_longitude: '106.6776839',
        destination_address: 'Dong Nai',
        destination_city: 'Dong Nai',
        destination_latitude: '10.8529606',
        destination_longitude: '107.1073189'
      }
    ];

    it('should merge existing stopovers with related trips stopovers', () => {
      const result = updateStopoversWithRelatedTrips(
        currentStopovers,
        relatedTrips,
        originPoint,
        destinationPoint
      );
      
      // Should have 2 stopovers (1 existing + 1 from related trips)
      expect(result.length).toBe(2);
      
      // Check that existing stopover is preserved
      expect(result.some(stop => stop.place_id === 'existing1')).toBe(true);
      
      // Check that Dong Nai from related trips is added
      expect(result.some(stop => stop.city === 'Dong Nai')).toBe(true);
    });

    it('should mark existing stopover as selected if it matches a related trip', () => {
      // Create a stopover with same coordinates as Dong Nai but not marked as selected
      const stopoversWithMatch: TExtendedAddress[] = [
        {
          place_id: 'existing1',
          address_name: 'Dong Nai with different name',
          formatted_address: 'Dong Nai with different name',
          city: 'Dong Nai',
          latitude: 10.8529606, // Same as Dong Nai from related trip
          longitude: 107.1073189,
          isSelected: false
        }
      ];
      
      const result = updateStopoversWithRelatedTrips(
        stopoversWithMatch,
        relatedTrips,
        originPoint,
        destinationPoint
      );
      
      // Should still have just 1 stopover (existing one got updated)
      expect(result.length).toBe(1);
      
      // The existing stopover should now be marked as selected
      const dongNaiStop = result.find(s => 
        isSameLocation(s, { latitude: 10.8529606, longitude: 107.1073189 })
      );
      expect(dongNaiStop?.isSelected).toBe(true);
    });
  });
}); 