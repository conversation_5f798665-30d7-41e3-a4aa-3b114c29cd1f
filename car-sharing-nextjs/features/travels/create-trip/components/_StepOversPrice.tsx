import { NumberField } from "@/components/form/NumberField";
import { Button } from "@/components/ui/button";
import { Container } from "@/components/ui/Container";
import { number2Currency } from "@/helper/currency";
import { useVisible } from "@/hooks/useVisible";
import { cn } from "@/utils";
import {
  comparePriceWithSuggestion,
  getStatusColor,
  PriceStatus,
} from "@/utils/priceCompare";
import { Drawer, Timeline } from "antd";
import { FC, useEffect, useMemo } from "react";
import { useForm, useWatch } from "react-hook-form";
import { useCreateTrip } from "@/features/travels/create-trip/providers/CreateTripProvider";

type TPrice = {
  price_max: number;
  price_avg: number;
  price_suggest_from: number;
  price_suggest_to: number;
  price_min: number;
  start_location: string;
  end_location: string;
};

type Props = {
  stopPrices: TPrice[];
};

// Tương tự StopoversPrice nhưng được sử dụng cho chế độ Edit
export const StepOversPrice: FC<Props> = ({ stopPrices }) => {
  const { trip, routes, setRoutes, setTripData } = useCreateTrip();
  const { visible, open, close } = useVisible();

  // Add debug logging
  useEffect(() => {
    console.log("StepOversPrice - Routes:", routes);
    console.log("StepOversPrice - Stop Prices:", stopPrices);
    console.log("StepOversPrice - Trip stopovers:", trip.stopovers);
  }, [routes, stopPrices, trip.stopovers]);

  // Check if there are any selected stopovers
  const hasSelectedStopovers = useMemo(() => {
    if (
      !trip.stopovers ||
      !Array.isArray(trip.stopovers) ||
      trip.stopovers.length === 0
    ) {
      console.log("StepOversPrice - No stopovers found in trip data");
      return false;
    }

    // Look for stopovers with isSelected property set to true
    const selectedStopovers = trip.stopovers.filter(
      (stopover) => stopover.isSelected === true
    );
    console.log(
      `StepOversPrice - Found ${selectedStopovers.length} selected stopovers`
    );

    return selectedStopovers.length > 0;
  }, [trip.stopovers]);

  // Identify unique routes to avoid duplicates
  const uniqueRoutes = useMemo(() => {
    if (!routes || routes.length <= 1) return [];

    // Skip the first route (usually main route)
    const intermediateRoutes = routes.slice(1);

    // Deduplicate routes based on endpoint coordinates
    const uniqueEndpoints = new Map();
    intermediateRoutes.forEach((route) => {
      const key = `${route.endPoint.latitude},${route.endPoint.longitude}`;
      if (!uniqueEndpoints.has(key)) {
        uniqueEndpoints.set(key, route);
      }
    });

    // Convert map back to array
    return Array.from(uniqueEndpoints.values());
  }, [routes]);

  // Deduplicate stopPrices to match unique routes
  const uniqueStopPrices = useMemo(() => {
    if (!Array.isArray(stopPrices) || stopPrices.length === 0) return [];

    // If fewer prices than routes, duplicate last price
    if (stopPrices.length < uniqueRoutes.length) {
      const result = [...stopPrices];
      const lastPrice = stopPrices[stopPrices.length - 1];
      while (result.length < uniqueRoutes.length) {
        result.push(lastPrice);
      }
      return result;
    }

    // If more prices than routes, truncate
    if (stopPrices.length > uniqueRoutes.length) {
      return stopPrices.slice(0, uniqueRoutes.length);
    }

    return stopPrices;
  }, [stopPrices, uniqueRoutes]);

  console.log("Deduplicated stopPrices:", uniqueStopPrices);

  const { control, getValues } = useForm({
    defaultValues: {
      prices:
        trip.stopoverPrices ??
        uniqueStopPrices.map(({ price_avg }) => price_avg),
    },
  });

  const prices = useWatch({ control, name: "prices" });

  const onOk = () => {
    // Save the prices from the form
    setTripData("stopoverPrices", getValues().prices);

    // Update the routes with the corresponding prices
    setRoutes((prev) => {
      return prev.map((route, index) => {
        // Keep first route unchanged
        if (index === 0) return { ...route };

        // Find if this route matches any unique route by coordinates
        const uniqueRouteIndex = uniqueRoutes.findIndex(
          (uniqueRoute) =>
            uniqueRoute.endPoint.latitude === route.endPoint.latitude &&
            uniqueRoute.endPoint.longitude === route.endPoint.longitude
        );

        // If it matches a unique route, use the corresponding price
        if (uniqueRouteIndex >= 0 && uniqueRouteIndex < prices.length) {
          return { ...route, price: prices[uniqueRouteIndex] };
        }

        // If not matched, keep the existing price
        return { ...route };
      });
    });

    close();
  };

  const getStatus = (priceData: number, price: TPrice) => {
    if (!price) return;
    return comparePriceWithSuggestion(priceData, {
      price_suggest_from: price.price_suggest_from,
      price_suggest_to: price.price_suggest_to,
    });
  };

  // Hide component if no selected stopovers
  if (!hasSelectedStopovers) {
    console.log("StepOversPrice - No selected stopovers, hiding component");
    return null;
  }

  // Skip early if we don't have valid routes data
  if (!routes) return null;

  return (
    <>
      <p
        onClick={open}
        className="cursor-pointer text-primary-500 w-full py-5 px-2 hover:bg-slate-200 rounded-lg text-lg"
      >
        Đặt giá cho các hành trình nhỏ
      </p>
      <Drawer placement="right" width="100vw" onClose={close} open={visible}>
        <Container>
          <div className="flex flex-col gap-4 py-11 items-center">
            <h1 className="text-5xl text-[#58aed8] font-bold text-center mb-4">
              Đặt giá cho các hành trình nhỏ
            </h1>
            <div className="flex flex-col w-1/2">
              {uniqueRoutes.map((route, index) => {
                // Find the matching stopPrice index
                const stopPriceIndex = index;

                // If no matching price found, skip
                if (
                  stopPriceIndex < 0 ||
                  stopPriceIndex >= uniqueStopPrices.length
                )
                  return null;

                const status = getStatus(
                  prices[stopPriceIndex],
                  uniqueStopPrices[stopPriceIndex]
                );

                // Find the original index of this route in the full routes array
                const originalIndex = routes.findIndex(
                  (r) =>
                    r !== routes[0] && // Skip first route
                    r.endPoint.latitude === route.endPoint.latitude &&
                    r.endPoint.longitude === route.endPoint.longitude
                );

                return (
                  <div
                    key={`${route.endPoint.latitude},${route.endPoint.longitude}`}
                    className="flex justify-between w-full"
                  >
                    <Timeline
                      items={[
                        {
                          children:
                            originalIndex === 1
                              ? trip.pickup?.formatted_address
                              : route.startPoint.address_name,
                        },
                        {
                          children:
                            originalIndex === routes.length - 1
                              ? trip.dropOff?.formatted_address
                              : route.endPoint.address_name,
                        },
                      ]}
                    />
                    <div className="w-[100px] mt-[20px]">
                      <NumberField
                        control={control}
                        name={`prices.${stopPriceIndex}`}
                        size="small"
                        renderValue={(value) => number2Currency(value ?? 0)}
                        step={1000}
                        min={1000}
                      />
                      <div
                        className={cn(
                          "text-white p-1 rounded-md whitespace-nowrap w-fit cursor-none select-none",
                          getStatusColor(status as PriceStatus)
                        )}
                      >
                        Giá đề xuất:{" "}
                        {number2Currency(
                          uniqueStopPrices[stopPriceIndex].price_suggest_from
                        )}{" "}
                        -{" "}
                        {number2Currency(
                          uniqueStopPrices[stopPriceIndex].price_suggest_to
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="flex justify-between gap-2 w-1/2">
              <Button
                variant="default"
                onClick={close}
                size="lg"
                className="flex-1"
              >
                Huỷ
              </Button>
              <Button
                onClick={onOk}
                size="lg"
                variant="primary"
                className="bg-primary-500 text-white flex-1"
              >
                Xác nhận
              </Button>
            </div>
          </div>
        </Container>
      </Drawer>
    </>
  );
}; 