import { useEffect } from 'react';
import { CreateTripStepper } from '../createTrip';
import { useCreateTrip } from '../providers/CreateTripProvider';
import { getStepperConfigWithComponents } from '../utils/stepperUtils';

// Import các stepper components theo cách đúng
import Approval from '../stepper/Approval';
import ChooseRoute from '../stepper/ChooseRoute';
import { Complete } from '../stepper/Complete';
import DepartureDate from '../stepper/DepartureDate';
import DepartureTime from '../stepper/DepartureTime';
import DropOff from '../stepper/DropOff';
import DropOffExact from '../stepper/DropOffExact';
import HelmetAvailable from '../stepper/HelmetAvailable';
import Notes from '../stepper/Notes';
import Pickup from '../stepper/Pickup';
import PickupExact from '../stepper/PickupExact';
import Price from '../stepper/Price';
import Seats from '../stepper/Seats';
import Stopovers from '../stepper/Stopovers';
import StopoversConfirm from '../stepper/StopoversConfirm';
import Vehicle from '../stepper/Vehicle';
import { Verify } from '../stepper/Verify';

interface TripStepperProps {
  isEdit?: boolean;
  updateType?: number;
}

const TripStepper: React.FC<TripStepperProps> = ({ 
  isEdit = false, 
  updateType = 0 
}) => {
  const { step, prevStep, setStep } = useCreateTrip();
  
  // Sử dụng getStepperConfigWithComponents từ utils
  const steps = getStepperConfigWithComponents(isEdit, updateType);
  
  // Đảm bảo step hiện tại có trong cấu hình
  useEffect(() => {
    if (steps.length > 0) {
      const stepValues = steps.map(s => s.step);
      if (!stepValues.includes(step)) {
        // Nếu step hiện tại không có trong cấu hình, chuyển sang step đầu tiên
        setStep(steps[0].step);
      }
    }
  }, [isEdit, updateType, step, steps, setStep]);
  
  // Tìm component cho step hiện tại
  const currentStepConfig = steps.find(s => s.step === step);
  
  // Nếu không tìm thấy component, có thể bước không tồn tại trong cấu hình hiện tại
  if (!currentStepConfig) {
    // Nếu có bước khả dụng, tự động chuyển sang
    if (steps.length > 0) {
      return (
        <div className="flex flex-col items-start justify-center h-full min-h-[50vh]">
          <h2 className="text-xl font-semibold mb-2">Đang chuyển bước...</h2>
          <p className="text-gray-500">Vui lòng đợi trong giây lát.</p>
        </div>
      );
    }
    
    // Nếu không có bước nào khả dụng
    return (
      <div className="flex flex-col items-start justify-center h-full min-h-[50vh]">
        <h2 className="text-xl font-semibold mb-2">Không tìm thấy bước</h2>
        <p className="text-gray-500">Bước này không tồn tại trong luồng hiện tại.</p>
        <div className="mt-4 flex gap-4">
          <button 
            onClick={prevStep}
            className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300"
          >
            Quay lại
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="stepper-container flex flex-col items-start justify-center w-full">
      {currentStepConfig.component}
      
      {/* Các nút điều hướng có thể được thêm vào đây hoặc trong các component con */}
    </div>
  );
};

export default TripStepper; 