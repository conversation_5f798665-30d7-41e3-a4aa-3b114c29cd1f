import { NumberField } from "@/components/form/NumberField";
import Timeline from "@/components/timeline/Timeline";
import { But<PERSON> } from "@/components/ui/button";
import { Container } from "@/components/ui/Container";
import { useCreateTrip } from "@/features/travels/create-trip/providers/CreateTripProvider";
import {
  convertPricesToRoutes,
  createFullRoute,
  filterMainRoute,
  filterSelectedStopovers,
  formatDuration,
  orderStopPricesByStopoverOrder,
  prepareDefaultPrices,
  TPrice
} from "@/features/travels/create-trip/utils/stopoverPriceUtils";
import { number2Currency } from "@/helper/currency";
import { useVisible } from "@/hooks/useVisible";
import { cn } from "@/utils";
import {
  comparePriceWithSuggestion,
  getStatusColor,
  PriceStatus,
} from "@/utils/priceCompare";
import { Drawer } from "antd";
import { FC, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";

// --------------------------
// TYPES
// --------------------------
type Props = {
  stopPrices: TPrice[];
};

export const StopoversPrice: FC<Props> = ({ stopPrices = [] }) => {
  // --------------------------
  // HOOKS
  // --------------------------
  const { trip, routes, setRoutes, setTripData } = useCreateTrip();
  const { visible, open, close } = useVisible();
  const [apiError, setApiError] = useState<string | null>(null);
  const [prices, setPrices] = useState<number[]>([]);
  const { control, getValues } = useForm({
    defaultValues: {
      prices:
        trip.stopoverPrices ?? stopPrices.map(({ price_avg }) => price_avg),
    },
  });

  // --------------------------
  // MEMOIZED VALUES
  // --------------------------
  const hasSelectedStopovers = useMemo(() => {
    if (
      !trip.stopovers ||
      !Array.isArray(trip.stopovers) ||
      trip.stopovers.length === 0
    ) {
      return false;
    }

    const selectedStopovers = filterSelectedStopovers(trip.stopovers);
    return selectedStopovers.length > 0;
  }, [trip.stopovers]);

  const uniqueStopPrices = useMemo(() => {
    if (!stopPrices || stopPrices.length === 0) {
      setApiError("Không có dữ liệu chuyến đi từ API");
      return [];
    }

    try {
      // Lấy danh sách stopovers đã chọn trước để sử dụng trong filterMainRoute
      const selectedStopovers = filterSelectedStopovers(trip.stopovers || []);
      console.log("🚀 ~ StopoversPrice ~ Selected stopovers:", selectedStopovers.length);
      console.log("🚀 ~ StopoversPrice ~ Original stopPrices:", stopPrices.length);

      // Lọc bỏ tuyến đường chính, chỉ giữ lại các tuyến đường trung gian
      const filteredPrices = filterMainRoute(stopPrices, selectedStopovers);
      console.log("🚀 ~ StopoversPrice ~ Filtered prices after removing main route:", filteredPrices.length);

      if (filteredPrices.length === 0) {
        setApiError("Không có chuyến con nào được tìm thấy");
      } else {
        setApiError(null);
      }

      // Sắp xếp stopPrices theo thứ tự của stopovers trong provider state
      // Điều này đảm bảo thứ tự hiển thị trong component khớp với thứ tự trong provider
      if (selectedStopovers.length > 0) {
        console.log("🚀 ~ StopoversPrice ~ Ordering prices based on stopover order");
        const orderedPrices = orderStopPricesByStopoverOrder(filteredPrices, selectedStopovers);
        console.log("🚀 ~ StopoversPrice ~ Final ordered prices:", orderedPrices.length);
        return orderedPrices;
      }

      return filteredPrices;
    } catch (error) {
      setApiError(`Lỗi xử lý dữ liệu tuyến đường: ${(error as Error).message}`);
      return [];
    }
  }, [stopPrices, trip.stopovers]);

  const apiBasedRoutes = useMemo(() => {
    if (!uniqueStopPrices || uniqueStopPrices.length === 0) {
      return [];
    }

    try {
      return convertPricesToRoutes(uniqueStopPrices);
    } catch (error) {
      setApiError(`Lỗi xử lý dữ liệu tuyến đường: ${(error as Error).message}`);
      return [];
    }
  }, [uniqueStopPrices]);

  const defaultPrices = useMemo(() => {
    return prepareDefaultPrices(
      trip.stopoverPrices,
      uniqueStopPrices,
      apiBasedRoutes
    );
  }, [trip.stopoverPrices, uniqueStopPrices, apiBasedRoutes]);

  // --------------------------
  // EFFECTS
  // --------------------------
  useEffect(() => {
    if (defaultPrices && defaultPrices.length > 0) {
      setPrices(defaultPrices);
    }
  }, [defaultPrices]);

  // --------------------------
  // HANDLERS
  // --------------------------
  const onOk = () => {
    // Lấy giá trị hiện tại từ form
    const formPrices = getValues("prices");
    console.log("🚀 ~ onOk ~ formPrices:", formPrices)

    // Cập nhật state prices trước
    setPrices(formPrices);

    // Lưu giá trị vào trip data
    setTripData("stopoverPrices", formPrices);

    if (apiBasedRoutes.length === 0) {
      close();
      return;
    }

    if (!routes || routes.length <= 1) {
      const mainRoute = createFullRoute(
        {
          startPoint: trip.pickup,
          endPoint: trip.dropOff,
          distance: 0,
          completionTime: 0,
          overviewPolyline: "",
        },
        trip.seatPrice || 0
      );

      const newRoutes = [
        mainRoute,
        ...apiBasedRoutes.map((route, index) =>
          createFullRoute(route, formPrices[index] || route.price)
        ),
      ];

      setRoutes(newRoutes);
      close();
      return;
    }
    setRoutes((prev) => {
      if (!prev || prev.length === 0) {
        return apiBasedRoutes.map((route, index) =>
          createFullRoute(route, formPrices[index] || route.price)
        );
      }

      return prev.map((route, index) => {
        if (index === 0) return { ...route };

        const apiRouteIndex = apiBasedRoutes.findIndex((apiRoute) => {
          const sameEndpoint =
            apiRoute.endPoint.latitude === route.endPoint.latitude &&
            apiRoute.endPoint.longitude === route.endPoint.longitude;

          const sameStartpoint =
            apiRoute.startPoint.latitude === route.startPoint.latitude &&
            apiRoute.startPoint.longitude === route.startPoint.longitude;

          return sameEndpoint && sameStartpoint;
        });

        if (apiRouteIndex >= 0 && apiRouteIndex < formPrices.length) {
          return {
            ...route,
            price: formPrices[apiRouteIndex],
            distance: apiBasedRoutes[apiRouteIndex].distance || route.distance,
          };
        }

        if (index - 1 < formPrices.length) {
          return { ...route, price: formPrices[index - 1] };
        }

        return { ...route };
      });
    });

    close();
  };

  const getStatus = (priceData: number, price: TPrice) => {
    if (!price) return;
    return comparePriceWithSuggestion(priceData, {
      price_suggest_from: price.price_suggest_from,
      price_suggest_to: price.price_suggest_to,
    });
  };

  // --------------------------
  // RENDER
  // --------------------------
  if (!hasSelectedStopovers) return null;

  return (
    <>
      <p
        onClick={open}
        className="cursor-pointer text-primary-500 w-full py-5 px-2 hover:bg-slate-200 rounded-lg text-lg"
      >
        Đặt giá cho các hành trình nhỏ
      </p>
      <Drawer placement="right" width="100vw" onClose={close} open={visible}>
        <Container>
          <div className="flex flex-col gap-4 py-11 items-center">
            <h1 className="text-5xl text-[#58aed8] font-bold text-center mb-4">
              Đặt giá cho các hành trình nhỏ
            </h1>

            {apiError ? (
              <div className="text-center p-6 text-red-500 bg-red-50 rounded-md w-full max-w-2xl">
                <div className="font-medium mb-2">Đã xảy ra lỗi:</div>
                <div>{apiError}</div>
                <div className="mt-4 text-sm text-gray-500">
                  Vui lòng thử lại sau hoặc liên hệ người quản trị hệ thống nếu
                  lỗi vẫn tiếp tục xảy ra.
                </div>
              </div>
            ) : apiBasedRoutes.length === 0 ? (
              <div className="text-center p-6 text-gray-500 bg-gray-50 rounded-md w-full max-w-2xl">
                <div className="font-medium mb-2">
                  Không có tuyến đường con nào được tìm thấy
                </div>
                <div>
                  Vui lòng thêm các điểm dừng vào hành trình của bạn hoặc đảm
                  bảo API đã trả về dữ liệu chính xác.
                </div>
              </div>
            ) : (
              <div className="flex flex-col w-full max-w-2xl">
                {apiBasedRoutes.map((route, index) => {
                  // Lấy thông tin giá cho route này
                  const stopPrice = uniqueStopPrices[index];
                  if (!stopPrice) {
                    return (
                      <div
                        key={`route-error-${index}`}
                        className="mb-4 p-4 bg-red-50 rounded-md text-red-500"
                      >
                        Lỗi: Không tìm thấy dữ liệu giá cho tuyến đường này
                      </div>
                    );
                  }

                  const status = getStatus(
                    prices[index] || stopPrice.price_avg,
                    stopPrice
                  );

                  return (
                    <div
                      key={`route-${index}`}
                      className="flex flex-col w-full mb-6 p-4 border border-gray-200 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow"
                    >
                      {/* Card Header */}
                      <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-100">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500 font-medium">
                            {stopPrice.distance
                              ? `${Math.round(stopPrice.distance * 10) / 10} km`
                              : "? km"}
                          </span>
                          <span className="text-gray-300">•</span>
                          <span className="text-sm text-gray-500">
                            {formatDuration(stopPrice.duration)}
                          </span>
                        </div>
                        <div
                          className={cn(
                            "text-white px-2 py-1 rounded-md text-xs",
                            getStatusColor(status as PriceStatus)
                          )}
                        >
                          Giá đề xuất:{" "}
                          {number2Currency(stopPrice.price_suggest_from)} -{" "}
                          {number2Currency(stopPrice.price_suggest_to)}
                        </div>
                      </div>

                      {/* Card Body */}
                      <div className="flex items-stretch min-h-[100px]">
                        <div className="flex-1">
                          <Timeline
                            variant="vertical"
                            textColor="text-gray-600"
                            isShowDuration={false}
                            tripDetail={
                              {
                                departure_address:
                                  route.startPoint.address_name || "",
                                departure_city: "",
                                departure_time: null,
                                destination_address:
                                  route.endPoint.address_name || "",
                                destination_city: "",
                                completion_time: null,
                              } as any
                            }
                          />
                        </div>
                        <div className="flex-initial ml-4 flex items-center">
                          <div className="flex flex-col items-end">
                            <NumberField
                              control={control}
                              name={`prices.${index}`}
                              size="small"
                              renderValue={(value) =>
                                number2Currency(value ?? 0)
                              }
                              step={1000}
                              min={1000}
                            />
                            {/* <span className="text-xs text-gray-400 mt-1">VNĐ</span> */}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            <Button
              className="px-8 my-4"
              variant="primary"
              onClick={onOk}
              disabled={!!apiError || apiBasedRoutes.length === 0}
            >
              Xác nhận
            </Button>
          </div>
        </Container>
      </Drawer>
    </>
  );
};
