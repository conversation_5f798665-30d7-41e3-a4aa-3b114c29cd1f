"use client";
import { TRelatedTrip, TTripForm } from "@/features/_common/types/trip";
import { useTripDetail } from "@/hooks/react-query/useTrip";
import { LoadingOutlined } from "@ant-design/icons";
import { Spin } from "antd";
import { FC, useEffect, useState } from "react";
import { CreateTripStepper, TCreateTripStepper } from "./createTrip";
import { CreateTripProvider, useCreateTrip } from "./providers/CreateTripProvider";
import Approval from "./stepper/Approval";
import ChooseRoute from "./stepper/ChooseRoute";
import { Complete } from "./stepper/Complete";
import DepartureDate from "./stepper/DepartureDate";
import DepartureTime from "./stepper/DepartureTime";
import DropOff from "./stepper/DropOff";
import DropOffExact from "./stepper/DropOffExact";
import HelmetAvailable from "./stepper/HelmetAvailable";
import Notes from "./stepper/Notes";
import Pickup from "./stepper/Pickup";
import PickupExact from "./stepper/PickupExact";
import Price from "./stepper/Price";
import Seats from "./stepper/Seats";
import Stopovers from "./stepper/Stopovers";
import StopoversConfirm from "./stepper/StopoversConfirm";
import Vehicle from "./stepper/Vehicle";
import { Verify } from "./stepper/Verify";
import { updateStopoversWithRelatedTrips } from "./utils/relatedTripsUtils";
import { TExtendedAddress } from "./utils/tripDataUtils";

// Mở rộng interface RelatedTrip để phù hợp với dữ liệu API
interface ExtendedRelatedTrip extends TRelatedTrip {
  departure_city: string;
  destination_city: string;
}

// Component LoadingScreen thay thế
const LoadingScreen: FC = () => {
  return (
    <div className="flex justify-center items-center min-h-screen">
      <Spin indicator={<LoadingOutlined spin />} />
    </div>
  );
};

// Component để hiển thị các bước chỉnh sửa chuyến đi
const StepComponent: FC = () => {
  const { step } = useCreateTrip();

  // Chuyển đổi bước hiện tại thành component tương ứng
  const getStepComp = (step: TCreateTripStepper) => {
    // Use unique keys for components that need to remount properly
    switch (step) {
      case CreateTripStepper.pickUp:
        return <Pickup key="pickup" />;
      case CreateTripStepper.pickUpExact:
        return <PickupExact key="pickupExact" />;
      case CreateTripStepper.dropOff:
        return <DropOff key="dropOff" />;
      case CreateTripStepper.dropOffExact:
        return <DropOffExact key="dropOffExact" />;
      case CreateTripStepper.vehicle:
        return <Vehicle key="vehicle" />;
      case CreateTripStepper.chooseRoute:
        return <ChooseRoute key="chooseRoute" />;
      case CreateTripStepper.stopovers:
        // Always remount the stopovers component with a unique key
        return <Stopovers key={`stopovers-${Date.now()}`} isEdit={true} />;
      case CreateTripStepper.stopoversConfirm:
        // Always remount the stopovers confirm component with a unique key
        return <StopoversConfirm key={`stopoversConfirm-${Date.now()}`} isEdit={true} />;
      case CreateTripStepper.departureDate:
        return <DepartureDate key="departureDate" />;
      case CreateTripStepper.departureTime:
        return <DepartureTime key="departureTime" />;
      case CreateTripStepper.seats:
        return <Seats key="seats" />;
      case CreateTripStepper.approval:
        return <Approval key="approval" />;
      case CreateTripStepper.helmetAvailable:
        return <HelmetAvailable key="helmetAvailable" />;
      case CreateTripStepper.price:
        return <Price key="price" />;
      case CreateTripStepper.notes:
        return <Notes key="notes" isLastStep isEdit={true} />;
      case CreateTripStepper.verify:
        return <Verify key="verify" />;
      case CreateTripStepper.complete:
        return <Complete key="complete" />;
      default:
        return null;
    }
  };

  // Sử dụng key để đảm bảo component remount khi step thay đổi
  return getStepComp(step);
};

interface EditTripProps {
  tripId: string;
}

const EditTrip: FC<EditTripProps> = ({ tripId }) => {
  const { data: tripData, isLoading } = useTripDetail(tripId);
  const [defaultValues, setDefaultValues] = useState<Partial<TTripForm>>({});
  const [isDataReady, setIsDataReady] = useState(false);

  useEffect(() => {
    if (tripData) {
      // Chuyển đổi dữ liệu từ API thành định dạng phù hợp cho form
      const formattedTrip: Partial<TTripForm> = {
        id: tripData.id,
        pickup: tripData.departure_address ? {
          place_id: tripData.id?.toString() || '',
          address_name: tripData.departure_address,
          formatted_address: tripData.departure_address,
          latitude: parseFloat(tripData.departure_latitude),
          longitude: parseFloat(tripData.departure_longitude),
          city: tripData.departure_city,
        } : undefined,
        dropOff: tripData.destination_address ? {
          place_id: tripData.id?.toString() || '',
          address_name: tripData.destination_address,
          formatted_address: tripData.destination_address,
          latitude: parseFloat(tripData.destination_latitude),
          longitude: parseFloat(tripData.destination_longitude),
          city: tripData.destination_city,
        } : undefined,
        departureDate: tripData.departure_date || '',
        departureTime: tripData.departure_time || '',
        vehicleId: tripData.vehicle_id,
        seatAmount: tripData.max_seat,
        seatPrice: tripData.price,
        autoAccept: tripData.auto_accept,
        hasHelmetAvailable: (tripData.has_helmet_available || 0) as 0 | 1,
        notes: tripData.notes,
        // Sử dụng mảng rỗng nếu không có dữ liệu routes
        routes: [],
        // Sử dụng mảng rỗng nếu không có dữ liệu stopovers
        stopovers: [],
        update_type: tripData.update_type,
      };

      // Xử lý related trips nếu có
      let processedStopovers: TExtendedAddress[] = [];
      
      // Xử lý relatedTrips với kiểu dữ liệu đã được mở rộng
      if (tripData.relatedTrips && formattedTrip.pickup && formattedTrip.dropOff) {
        processedStopovers = updateStopoversWithRelatedTrips(
          processedStopovers,
          tripData.relatedTrips as ExtendedRelatedTrip[],
          formattedTrip.pickup,
          formattedTrip.dropOff
        );
      }

      // Cập nhật state với dữ liệu đã xử lý
      setDefaultValues({
        ...formattedTrip,
        stopovers: processedStopovers
      });
      setIsDataReady(true);
    }
  }, [tripData]);

  if (isLoading || !isDataReady) {
    return <LoadingScreen />;
  }

  return (
    <CreateTripProvider 
      defaultValues={defaultValues}
      isEdit={true}
      updateType={tripData?.update_type}
      hasCoPassenger={!!tripData?.co_passenger?.length}
    >
      <StepComponent />
    </CreateTripProvider>
  );
};

export default EditTrip; 