import { TAddress } from "@/types/address";
import { TExtendedAddress } from "../utils/tripDataUtils";

interface RelatedTrip {
  id: number;
  is_parent_route: number; 
  price?: string;
  overview_polyline: string;
  completion_time?: number;
  distance?: number;
  departure_address: string;
  departure_city: string;
  departure_latitude: string;
  departure_longitude: string;
  destination_address: string;
  destination_city: string;
  destination_latitude: string;
  destination_longitude: string;
}

/**
 * Kiểm tra hai điểm có cùng tọa độ hay không (với độ chính xác nhất định)
 */
export const isSameLocation = (
  loc1: { latitude: number | string; longitude: number | string },
  loc2: { latitude: number | string; longitude: number | string },
  precision: number = 0.0001
): boolean => {
  const lat1 = typeof loc1.latitude === 'string' ? parseFloat(loc1.latitude) : loc1.latitude;
  const lng1 = typeof loc1.longitude === 'string' ? parseFloat(loc1.longitude) : loc1.longitude;
  const lat2 = typeof loc2.latitude === 'string' ? parseFloat(loc2.latitude) : loc2.latitude;
  const lng2 = typeof loc2.longitude === 'string' ? parseFloat(loc2.longitude) : loc2.longitude;

  return Math.abs(lat1 - lat2) < precision && Math.abs(lng1 - lng2) < precision;
};

/**
 * Kiểm tra xem một điểm dừng có tồn tại trong danh sách stopovers không
 */
export const isStopoverExistInList = (
  stopover: TAddress,
  stopovers: TAddress[]
): boolean => {
  return stopovers.some(existingStopover => 
    isSameLocation(stopover, existingStopover)
  );
};

/**
 * Chuyển đổi RelatedTrip thành định dạng TAddress
 */
export const relatedTripToAddress = (trip: RelatedTrip): TExtendedAddress => {
  console.log("🚀 ~ relatedTripToAddress ~ trip:", trip)
  return {
    place_id: `related_trip_${trip.id}`,
    address_name: trip.destination_address,
    formatted_address: trip.destination_address,
    city: trip.destination_city,
    latitude: parseFloat(trip.destination_latitude),
    longitude: parseFloat(trip.destination_longitude),
    address_detail: JSON.stringify({
      city: trip.destination_city,
    }),
    isSelected: true, // Mặc định chọn điểm dừng từ RelatedTrips
  };
};

/**
 * Lọc và chuyển đổi RelatedTrips thành danh sách điểm dừng
 */
export const extractStopoversFromRelatedTrips = (
  relatedTrips: RelatedTrip[] | undefined,
  originPoint: TAddress | null,
  destinationPoint: TAddress | null
): TExtendedAddress[] => {
  if (!relatedTrips || !originPoint || !destinationPoint) {
    return [];
  }

  // Lọc bỏ is_parent_route = 1 và các điểm trùng với điểm xuất phát và điểm đến
  const filteredTrips = relatedTrips.filter(trip => {
    // Bỏ qua các tuyến chính
    if (trip.is_parent_route === 1) {
      return false;
    }

    // Bỏ qua nếu điểm đến trùng với điểm xuất phát của chuyến đi
    if (isSameLocation(
      { latitude: trip.destination_latitude, longitude: trip.destination_longitude },
      originPoint
    )) {
      return false;
    }

    // Bỏ qua nếu điểm đến trùng với điểm đích của chuyến đi
    if (isSameLocation(
      { latitude: trip.destination_latitude, longitude: trip.destination_longitude },
      destinationPoint
    )) {
      return false;
    }

    return true;
  });

  // Chuyển đổi thành TAddress và đánh dấu đã được chọn
  return filteredTrips.map(trip => relatedTripToAddress(trip));
};

/**
 * Cập nhật danh sách stopovers với các điểm dừng từ RelatedTrips
 * Trả về danh sách stopovers đã được cập nhật
 */
export const updateStopoversWithRelatedTrips = (
  currentStopovers: TAddress[],
  relatedTrips: RelatedTrip[] | undefined,
  originPoint: TAddress | null,
  destinationPoint: TAddress | null
): TExtendedAddress[] => {
  console.log("🚀 ~ relatedTrips:", relatedTrips)
  // Lấy các điểm dừng từ RelatedTrips
  const stopsFromRelatedTrips = extractStopoversFromRelatedTrips(
    relatedTrips,
    originPoint,
    destinationPoint
  );
  
  // Hợp nhất danh sách hiện tại với các điểm dừng mới
  const updatedStopovers: TExtendedAddress[] = [...currentStopovers];
  
  // Thêm các điểm dừng mới từ RelatedTrips nếu chưa tồn tại
  stopsFromRelatedTrips.forEach(newStop => {
    if (!isStopoverExistInList(newStop, updatedStopovers)) {
      updatedStopovers.push(newStop);
    } else {
      // Nếu đã tồn tại, đánh dấu là đã được chọn
      const existingIndex = updatedStopovers.findIndex(stop => 
        isSameLocation(stop, newStop)
      );
      if (existingIndex !== -1) {
        updatedStopovers[existingIndex] = {
          ...updatedStopovers[existingIndex],
          isSelected: true
        };
      }
    }
  });
  
  return updatedStopovers;
}; 