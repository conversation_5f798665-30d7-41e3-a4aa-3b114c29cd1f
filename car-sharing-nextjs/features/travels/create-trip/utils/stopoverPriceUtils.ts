import { TAddress } from "@/types/address";

/**
 * <PERSON><PERSON>nh nghĩa type cho dữ liệu gi<PERSON> từ API
 */
export type TPrice = {
  price_max: number;
  price_avg: number;
  price_suggest_from: number;
  price_suggest_to: number;
  price_min: number;
  start_location: string;
  end_location: string;
  start_address?: string;
  end_address?: string;
  distance?: number;
  duration?: number;
};

/**
 * Định nghĩa type cho route dựa trên API
 */
export type TApiBasedRoute = {
  startPoint: {
    latitude: number;
    longitude: number;
    address_name: string;
  };
  endPoint: {
    latitude: number;
    longitude: number;
    address_name: string;
  };
  price: number;
  distance: number;
  duration: number;
};

/**
 * L<PERSON><PERSON> danh sách các điểm dừng đã được chọn
 */
export const filterSelectedStopovers = (stopovers: TAddress[]): TAddress[] => {
  if (!stopovers || !Array.isArray(stopovers) || stopovers.length === 0) {
    return [];
  }
  return stopovers.filter((stopover) => stopover.isSelected === true);
};

/**
 * Sắp xếp stopPrices theo thứ tự của stopovers trong provider state
 * Đảm bảo thứ tự hiển thị trong StopoversPrice component khớp với thứ tự trong provider
 */
export const orderStopPricesByStopoverOrder = (
  stopPrices: TPrice[],
  orderedStopovers: TAddress[]
): TPrice[] => {
  if (!stopPrices || stopPrices.length === 0 || !orderedStopovers || orderedStopovers.length === 0) {
    return stopPrices || [];
  }

  console.log("🔍 === DEBUGGING orderStopPricesByStopoverOrder ===");
  console.log("🔍 Input stopPrices:", stopPrices.length);
  console.log("🔍 Input stopovers:", orderedStopovers.length);
  console.log("🔍 StopPrices details:", stopPrices.map(p => ({
    start: p.start_location,
    end: p.end_location,
    startAddr: p.start_address,
    endAddr: p.end_address
  })));
  console.log("🔍 Stopovers details:", orderedStopovers.map(s => ({
    lat: s.latitude,
    lng: s.longitude,
    addr: s.address_name
  })));

  // Chuẩn hóa coordinates để so sánh (loại bỏ khoảng trắng)
  const normalizeCoord = (coord: string) => coord.replace(/\s+/g, '');

  // Tạo map từ coordinates đến stopPrice để tra cứu nhanh
  const stopPriceMap = new Map<string, TPrice>();

  stopPrices.forEach(price => {
    if (price.start_location && price.end_location) {
      // Chuẩn hóa coordinates trước khi tạo key
      const normalizedStart = normalizeCoord(price.start_location);
      const normalizedEnd = normalizeCoord(price.end_location);
      const key = `${normalizedStart}-${normalizedEnd}`;
      stopPriceMap.set(key, price);
      console.log("🚀 ~ orderStopPricesByStopoverOrder ~ Added to map:", key);
    }
  });

  // Sắp xếp stopPrices theo thứ tự của orderedStopovers
  const orderedStopPrices: TPrice[] = [];

  for (let i = 0; i < orderedStopovers.length - 1; i++) {
    const currentStopover = orderedStopovers[i];
    const nextStopover = orderedStopovers[i + 1];

    // Tạo key từ coordinates của stopovers (chuẩn hóa)
    const startCoord = normalizeCoord(`${currentStopover.latitude},${currentStopover.longitude}`);
    const endCoord = normalizeCoord(`${nextStopover.latitude},${nextStopover.longitude}`);
    const key = `${startCoord}-${endCoord}`;

    console.log("🚀 ~ orderStopPricesByStopoverOrder ~ Looking for route:", key);

    // Tìm stopPrice tương ứng
    const matchingPrice = stopPriceMap.get(key);
    if (matchingPrice) {
      console.log("🚀 ~ orderStopPricesByStopoverOrder ~ Found exact match for:", key);
      orderedStopPrices.push(matchingPrice);
    } else {
      console.log("🚀 ~ orderStopPricesByStopoverOrder ~ No exact match, trying fallback for:", key);

      // Nếu không tìm thấy exact match, tìm bằng cách so sánh coordinates với tolerance
      const fallbackPrice = stopPrices.find(price => {
        if (!price.start_location || !price.end_location) return false;

        try {
          const priceStartCoords = parseCoordinates(price.start_location);
          const priceEndCoords = parseCoordinates(price.end_location);

          // So sánh với độ chính xác 0.001 (khoảng 100m)
          const startMatch = Math.abs(priceStartCoords[0] - currentStopover.latitude) < 0.001 &&
                            Math.abs(priceStartCoords[1] - currentStopover.longitude) < 0.001;
          const endMatch = Math.abs(priceEndCoords[0] - nextStopover.latitude) < 0.001 &&
                          Math.abs(priceEndCoords[1] - nextStopover.longitude) < 0.001;

          return startMatch && endMatch;
        } catch {
          return false;
        }
      });

      if (fallbackPrice) {
        console.log("🚀 ~ orderStopPricesByStopoverOrder ~ Found fallback match for:", key);
        orderedStopPrices.push(fallbackPrice);
      } else {
        console.log("🚀 ~ orderStopPricesByStopoverOrder ~ No match found for:", key);
      }
    }
  }

  console.log("🚀 ~ orderStopPricesByStopoverOrder ~ Final ordered prices:", orderedStopPrices.length);
  return orderedStopPrices;
};

/**
 * Lọc để chỉ loại bỏ chuyến đi chính, giữ lại tất cả các chuyến trung gian
 * Cải thiện logic để xác định chính xác tuyến đường chính dựa trên stopovers
 */
export const filterMainRoute = (stopPrices: TPrice[], selectedStopovers?: TAddress[]): TPrice[] => {
  console.log("🔍 === DEBUGGING filterMainRoute ===");
  console.log("🔍 Input stopPrices:", stopPrices?.length || 0);
  console.log("🔍 Input selectedStopovers:", selectedStopovers?.length || 0);

  if (!stopPrices || stopPrices.length === 0) {
    console.log("🔍 No stopPrices, returning empty array");
    return [];
  }

  // Nếu không có stopovers được chọn, sử dụng logic cũ để tương thích ngược
  if (!selectedStopovers || selectedStopovers.length === 0) {
    // Logic cũ: loại bỏ tuyến đường từ start_location đầu tiên đến end_location cuối cùng
    if (stopPrices.length <= 1) {
      return stopPrices;
    }

    const mainRouteStart = stopPrices[0].start_location;
    const mainRouteEnd = stopPrices[stopPrices.length - 1].end_location;

    return stopPrices.filter(price => {
      const isMainRoute = price.start_location === mainRouteStart && price.end_location === mainRouteEnd;
      if (isMainRoute) {
        console.log("🚀 ~ filterMainRoute ~ Filtering out main route (legacy):", price.start_address, "→", price.end_address);
      }
      return !isMainRoute;
    });
  }

  // Logic mới: sử dụng stopovers để xác định chính xác tuyến đường chính
  const firstStopover = selectedStopovers[0];
  const lastStopover = selectedStopovers[selectedStopovers.length - 1];

  if (!firstStopover || !lastStopover) {
    return stopPrices;
  }

  // Chuẩn hóa coordinates để so sánh (loại bỏ khoảng trắng)
  const normalizeCoord = (coord: string) => coord.replace(/\s+/g, '');
  const startCoord = normalizeCoord(`${firstStopover.latitude},${firstStopover.longitude}`);
  const endCoord = normalizeCoord(`${lastStopover.latitude},${lastStopover.longitude}`);

  console.log("🚀 ~ filterMainRoute ~ Looking for main route from:", startCoord, "to:", endCoord);

  // Lọc để loại bỏ tuyến đường chính (từ điểm đầu đến điểm cuối, bỏ qua các stopovers)
  return stopPrices.filter(price => {
    const priceStartCoord = normalizeCoord(price.start_location || '');
    const priceEndCoord = normalizeCoord(price.end_location || '');

    // Kiểm tra xem đây có phải là tuyến đường chính không
    const isMainRoute = priceStartCoord === startCoord && priceEndCoord === endCoord;

    // Nếu đây là tuyến đường chính, loại bỏ nó
    if (isMainRoute) {
      console.log("🚀 ~ filterMainRoute ~ Filtering out main route:", price.start_address, "→", price.end_address);
      return false;
    }

    return true;
  });
};

/**
 * Chuyển đổi các dữ liệu giá từ API thành định dạng route sử dụng trong ứng dụng
 */
export const convertPricesToRoutes = (uniqueStopPrices: TPrice[]): TApiBasedRoute[] => {
  if (!uniqueStopPrices || uniqueStopPrices.length === 0) {
    return [];
  }

  return uniqueStopPrices.map((price, index) => {
    try {
      if (!price.start_location || !price.end_location) {
        throw new Error("Missing location data");
      }

      const startCoords = parseCoordinates(price.start_location);
      const endCoords = parseCoordinates(price.end_location);

      // Kiểm tra địa chỉ
      const startAddress = price.start_address || "Địa chỉ không xác định";
      const endAddress = price.end_address || "Địa chỉ không xác định";

      return {
        startPoint: {
          latitude: startCoords[0],
          longitude: startCoords[1],
          address_name: startAddress,
        },
        endPoint: {
          latitude: endCoords[0],
          longitude: endCoords[1],
          address_name: endAddress,
        },
        price: price.price_avg || 0,
        distance: price.distance || 0,
        duration: price.duration || 0,
      };
    } catch (error) {
      console.error("Error processing route data:", error, price);
      throw error;
    }
  });
};

/**
 * Parse tọa độ từ chuỗi "lat, lng"
 */
export const parseCoordinates = (locationStr: string): [number, number] => {
  if (!locationStr) {
    throw new Error("Missing coordinates");
  }

  const coords = locationStr.split(',').map(coord => parseFloat(coord.trim()));
  if (coords.length !== 2 || isNaN(coords[0]) || isNaN(coords[1])) {
    throw new Error("Invalid coordinates format");
  }

  return [coords[0], coords[1]];
};

/**
 * Chuẩn bị giá trị mặc định cho form dựa trên dữ liệu trip và API
 */
export const prepareDefaultPrices = (
  tripStopoverPrices: number[] | undefined,
  uniqueStopPrices: TPrice[],
  apiBasedRoutes: TApiBasedRoute[]
): number[] => {
  // Nếu không có routes hoặc giá, trả về mảng rỗng
  if (!uniqueStopPrices || !apiBasedRoutes || apiBasedRoutes.length === 0) {
    return [];
  }

  // Sử dụng giá từ trip.stopoverPrices nếu có
  if (tripStopoverPrices && Array.isArray(tripStopoverPrices) && tripStopoverPrices.length > 0) {
    // Nếu số lượng giá khác với số lượng routes, điều chỉnh lại
    if (tripStopoverPrices.length !== apiBasedRoutes.length) {
      if (tripStopoverPrices.length < apiBasedRoutes.length) {
        // Nếu thiếu giá, bổ sung bằng price_avg
        return [
          ...tripStopoverPrices,
          ...uniqueStopPrices.slice(tripStopoverPrices.length).map(price => price.price_avg)
        ];
      } else {
        // Nếu thừa giá, cắt bớt
        return tripStopoverPrices.slice(0, apiBasedRoutes.length);
      }
    }

    return tripStopoverPrices;
  }

  // Sử dụng giá trung bình từ API
  return uniqueStopPrices.map(price => price.price_avg);
};

/**
 * Tạo route đầy đủ với các trường bắt buộc
 */
export const createFullRoute = (routeData: any, price: number) => {
  console.log("🚀 ~ createFullRoute ~ price:", price)
  console.log("🚀 ~ createFullRoute ~ routeData:", routeData)
  return {
    startPoint: routeData.startPoint,
    endPoint: routeData.endPoint,
    price: price || routeData.price || 0,
    // Thêm các trường bắt buộc của TRoute
    distance: routeData.distance || 0,
    completionTime: routeData.duration || 0,
    // Các trường tùy chọn
    duration: routeData.duration,
  };
};

/**
 * Format thời gian di chuyển sang giờ:phút
 */
export const formatDuration = (seconds?: number) => {
  if (!seconds) return "";
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h${minutes > 0 ? ` ${minutes}p` : ''}`;
};