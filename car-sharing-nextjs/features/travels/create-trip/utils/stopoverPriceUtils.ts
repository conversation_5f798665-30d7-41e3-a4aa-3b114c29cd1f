import { TAddress } from "@/types/address";

/**
 * <PERSON><PERSON><PERSON> nghĩa type cho dữ liệu giá từ API
 */
export type TPrice = {
  price_max: number;
  price_avg: number;
  price_suggest_from: number;
  price_suggest_to: number;
  price_min: number;
  start_location: string;
  end_location: string;
  start_address?: string;
  end_address?: string;
  distance?: number;
  duration?: number;
};

/**
 * Định nghĩa type cho route dựa trên API
 */
export type TApiBasedRoute = {
  startPoint: {
    latitude: number;
    longitude: number;
    address_name: string;
  };
  endPoint: {
    latitude: number;
    longitude: number;
    address_name: string;
  };
  price: number;
  distance: number;
  duration: number;
};

/**
 * Lọc danh sách các điểm dừng đã được chọn
 */
export const filterSelectedStopovers = (stopovers: TAddress[]): TAddress[] => {
  if (!stopovers || !Array.isArray(stopovers) || stopovers.length === 0) {
    return [];
  }
  return stopovers.filter((stopover) => stopover.isSelected === true);
};

/**
 * <PERSON><PERSON><PERSON> để chỉ loại bỏ chuyến đi chính, giữ lại tất cả các chuyến trung gian
 */
export const filterMainRoute = (stopPrices: TPrice[]): TPrice[] => {
  if (!stopPrices || stopPrices.length === 0) {
    return [];
  }
  
  // Xác định chuyến đi chính (thường là tuyến đường từ điểm đầu đến điểm cuối của hành trình)
  const mainRouteStart = stopPrices.length > 0 ? stopPrices[0].start_location : "";
  const mainRouteEnd = stopPrices.length > 0 ? stopPrices[stopPrices.length - 1].end_location : "";
  
  // Lọc để chỉ loại bỏ chuyến đi chính, giữ lại tất cả các chuyến trung gian
  return stopPrices.filter(price => {
    // Kiểm tra xem đây có phải là chuyến đi chính không
    const isMainRoute = price.start_location === mainRouteStart && price.end_location === mainRouteEnd;
    return !isMainRoute;
  });
};

/**
 * Chuyển đổi các dữ liệu giá từ API thành định dạng route sử dụng trong ứng dụng
 */
export const convertPricesToRoutes = (uniqueStopPrices: TPrice[]): TApiBasedRoute[] => {
  if (!uniqueStopPrices || uniqueStopPrices.length === 0) {
    return [];
  }
  
  return uniqueStopPrices.map((price, index) => {
    try {
      if (!price.start_location || !price.end_location) {
        throw new Error("Missing location data");
      }
      
      const startCoords = parseCoordinates(price.start_location);
      const endCoords = parseCoordinates(price.end_location);
      
      // Kiểm tra địa chỉ
      const startAddress = price.start_address || "Địa chỉ không xác định";
      const endAddress = price.end_address || "Địa chỉ không xác định";
      
      return {
        startPoint: {
          latitude: startCoords[0],
          longitude: startCoords[1],
          address_name: startAddress,
        },
        endPoint: {
          latitude: endCoords[0],
          longitude: endCoords[1],
          address_name: endAddress,
        },
        price: price.price_avg || 0,
        distance: price.distance || 0,
        duration: price.duration || 0,
      };
    } catch (error) {
      console.error("Error processing route data:", error, price);
      throw error;
    }
  });
};

/**
 * Parse tọa độ từ chuỗi "lat, lng"
 */
export const parseCoordinates = (locationStr: string): [number, number] => {
  if (!locationStr) {
    throw new Error("Missing coordinates");
  }
  
  const coords = locationStr.split(',').map(coord => parseFloat(coord.trim()));
  if (coords.length !== 2 || isNaN(coords[0]) || isNaN(coords[1])) {
    throw new Error("Invalid coordinates format");
  }
  
  return [coords[0], coords[1]];
};

/**
 * Chuẩn bị giá trị mặc định cho form dựa trên dữ liệu trip và API
 */
export const prepareDefaultPrices = (
  tripStopoverPrices: number[] | undefined,
  uniqueStopPrices: TPrice[],
  apiBasedRoutes: TApiBasedRoute[]
): number[] => {
  // Nếu không có routes hoặc giá, trả về mảng rỗng
  if (!uniqueStopPrices || !apiBasedRoutes || apiBasedRoutes.length === 0) {
    return [];
  }
  
  // Sử dụng giá từ trip.stopoverPrices nếu có
  if (tripStopoverPrices && Array.isArray(tripStopoverPrices) && tripStopoverPrices.length > 0) {
    // Nếu số lượng giá khác với số lượng routes, điều chỉnh lại
    if (tripStopoverPrices.length !== apiBasedRoutes.length) {
      if (tripStopoverPrices.length < apiBasedRoutes.length) {
        // Nếu thiếu giá, bổ sung bằng price_avg
        return [
          ...tripStopoverPrices,
          ...uniqueStopPrices.slice(tripStopoverPrices.length).map(price => price.price_avg)
        ];
      } else {
        // Nếu thừa giá, cắt bớt
        return tripStopoverPrices.slice(0, apiBasedRoutes.length);
      }
    }
    
    return tripStopoverPrices;
  }
  
  // Sử dụng giá trung bình từ API
  return uniqueStopPrices.map(price => price.price_avg);
};

/**
 * Tạo route đầy đủ với các trường bắt buộc
 */
export const createFullRoute = (routeData: any, price: number) => {
  console.log("🚀 ~ createFullRoute ~ price:", price)
  console.log("🚀 ~ createFullRoute ~ routeData:", routeData)
  return {
    startPoint: routeData.startPoint,
    endPoint: routeData.endPoint,
    price: price || routeData.price || 0,
    // Thêm các trường bắt buộc của TRoute
    distance: routeData.distance || 0,
    completionTime: routeData.duration || 0,
    // Các trường tùy chọn
    duration: routeData.duration,
  };
};

/**
 * Format thời gian di chuyển sang giờ:phút
 */
export const formatDuration = (seconds?: number) => {
  if (!seconds) return "";
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h${minutes > 0 ? ` ${minutes}p` : ''}`;
}; 