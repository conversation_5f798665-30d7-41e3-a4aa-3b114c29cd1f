import { TTripForm } from "@/features/_common/types/trip";
import { TCreateTripStepper } from "../createTrip";
import { getStepperConfig } from "./stepperUtils";
import { findMissingApiStopovers, isStopoverFromApi } from "./stopoverUtils";
import { TExtendedAddress } from "./tripDataUtils";

/**
 * <PERSON><PERSON>m tra sự khác biệt giữa hai danh sách stopovers
 */
export const areStopoversDifferent = (
  stopovers1: TExtendedAddress[],
  stopovers2: TExtendedAddress[]
): boolean => {
  if (stopovers1.length !== stopovers2.length) return true;

  // So sánh từng cặp địa điểm dựa trên place_id và trạng thái isSelected
  return stopovers1.some((stop1, index) => {
    const stop2 = stopovers2[index];
    return (
      stop1.place_id !== stop2.place_id || stop1.isSelected !== stop2.isSelected
    );
  });
};

/**
 * <PERSON><PERSON><PERSON> bảo c<PERSON> stopovers từ API luôn được chọn
 */
export const ensureApiStopoversSelected = (
  isEdit: boolean,
  stopovers: TExtendedAddress[],
  tripStopovers: TExtendedAddress[] | undefined,
  setStopovers: (stopovers: TExtendedAddress[]) => void,
  updateTripStopovers: (stopovers: TExtendedAddress[]) => void
): void => {
  if (!isEdit || stopovers.length === 0) return;

  const apiStopovers = stopovers.filter(isStopoverFromApi);
  if (apiStopovers.length === 0) return;

  // Danh sách ID các điểm dừng từ API
  const apiIds = new Set(apiStopovers.map((stop) => stop.place_id));

  // 1. Tạo bản cập nhật mới cho stopovers
  const updatedStopovers = stopovers.map((stop) => {
    return {
      ...stop,
      isSelected: apiIds.has(stop.place_id) ? true : stop.isSelected,
    }
  })

  // Chỉ cập nhật nếu có sự thay đổi
  const hasStopoverChanges = areStopoversDifferent(stopovers, updatedStopovers);
  if (hasStopoverChanges) {
    setStopovers(updatedStopovers);
  }

  // 2. Thêm các điểm dừng API bị thiếu vào trip.stopovers
  const missing = findMissingApiStopovers(apiStopovers, tripStopovers || []);
  if (missing.length > 0) {
    const updatedTripStopovers = [
      ...(tripStopovers || []),
      ...missing.map((stop) => ({ ...stop, isSelected: true })),
    ];
    updateTripStopovers(updatedTripStopovers);
  }
};

/**
 * Cập nhật trip.stopovers từ các stopovers đã chọn
 */
export const syncSelectedStopovers = (
  stopovers: TExtendedAddress[],
  tripStopovers: TExtendedAddress[] | undefined,
  updateTripStopovers: (stopovers: TExtendedAddress[]) => void
): void => {
  const selected = stopovers.filter((stop) => stop.isSelected);

  // Nếu không có stopovers trong trip, chỉ cần cập nhật
  if (!tripStopovers || tripStopovers.length === 0) {
    if (selected.length > 0) {
      updateTripStopovers(selected);
    }
    return;
  }

  // Kiểm tra xem có sự khác biệt
  const currentIds = new Set(tripStopovers.map((stop) => stop.place_id));
  const selectedIds = new Set(selected.map((stop) => stop.place_id));

  // Nhanh chóng kiểm tra số lượng
  let isDifferent = selected.length !== tripStopovers.length;

  // Kiểm tra chi tiết nếu số lượng giống nhau
  if (!isDifferent) {
    isDifferent =
      Array.from(selectedIds).some((id) => !currentIds.has(id)) ||
      Array.from(currentIds).some((id) => !selectedIds.has(id));
  }

  // Chỉ cập nhật khi cần thiết
  if (isDifferent) {
    updateTripStopovers(selected);
  }
};

/**
 * Cập nhật state stopovers khi thay đổi trip.stopovers
 */
export const updateStopoversSelection = (
  stopovers: TExtendedAddress[],
  selectedStopovers: TExtendedAddress[]
): TExtendedAddress[] => {
  // Create maps to quickly lookup stopovers by ID
  const existingStopoverMap = new Map(
    stopovers.filter(stop => !!stop.place_id).map(stop => [stop.place_id as string, stop])
  );
  
  const resultMap = new Map<string, TExtendedAddress>();
  
  // First, add all existing stopovers to the result with updated selection status
  stopovers.forEach(stop => {
    if (!stop.place_id) return;
    
    const isSelected = selectedStopovers.some(selected => 
      selected.place_id && selected.place_id === stop.place_id
    );
    
    resultMap.set(stop.place_id as string, {
      ...stop,
      isSelected
    });
  });
  
  // Then, add any new stopovers from selectedStopovers that aren't in the existing list
  selectedStopovers.forEach(selected => {
    if (!selected.place_id) return;
    
    if (!existingStopoverMap.has(selected.place_id as string)) {
      resultMap.set(selected.place_id as string, {
        ...selected,
        isSelected: true
      });
    }
  });
  
  return Array.from(resultMap.values());
};

/**
 * Tạo dữ liệu hành trình đảo ngược
 */
export const createReversedTrip = (
  currentTrip: Partial<TTripForm>
): Partial<TTripForm> => {
  return {
    pickup: currentTrip.dropOff,
    dropOff: currentTrip.pickup,
    vehicleSelected: currentTrip.vehicleSelected,
    vehicleId: currentTrip.vehicleId,
    seatPrice: currentTrip.seatPrice,
    seatAmount: currentTrip.seatAmount,
    notes: currentTrip.notes,
  };
};

/**
 * Kiểm tra và cập nhật bước tiếp theo nếu cần thiết
 */
export const validateAndUpdateStep = (
  nextStep: TCreateTripStepper,
  isEdit: boolean,
  updateType: number,
  currentStep: TCreateTripStepper
): { isValid: boolean; validStep: TCreateTripStepper | null } => {
  const availableSteps = getStepperConfig(isEdit, updateType).map(
    (config) => config.step
  );

  if (availableSteps.includes(nextStep)) {
    return { isValid: true, validStep: nextStep };
  }

  if (availableSteps.length > 0) {
    return { isValid: false, validStep: availableSteps[0] };
  }

  return { isValid: false, validStep: null };
};

/**
 * Cập nhật trip khi thay đổi một trường cụ thể
 */
export const createTripFieldUpdater = (
  keyName: keyof TTripForm,
  value: unknown,
  currentTrip: Partial<TTripForm>
): Partial<TTripForm> => {
  return { ...currentTrip, [keyName]: value };
};

/**
 * Tìm xe được chọn từ danh sách xe
 */
export const findSelectedVehicle = (
  vehicles: Array<{ id: number | string }>,
  vehicleId: number | string | undefined
) => {
  if (!vehicleId) return null;
  return vehicles.find((v) => v.id === vehicleId) || null;
};
