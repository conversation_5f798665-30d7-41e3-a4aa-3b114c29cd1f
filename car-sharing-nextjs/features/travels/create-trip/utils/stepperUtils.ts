import { CreateTripStepper } from "../createTrip";
import Pickup from '../stepper/Pickup';
import PickupExact from '../stepper/PickupExact';
import DropOff from '../stepper/DropOff';
import DropOffExact from '../stepper/DropOffExact';
import Vehicle from '../stepper/Vehicle';
import ChooseRoute from '../stepper/ChooseRoute';
import Stopovers from '../stepper/Stopovers';
import StopoversConfirm from '../stepper/StopoversConfirm';
import DepartureDate from '../stepper/DepartureDate';
import DepartureTime from '../stepper/DepartureTime';
import Seats from '../stepper/Seats';
import Approval from '../stepper/Approval';
import HelmetAvailable from '../stepper/HelmetAvailable';
import Notes from '../stepper/Notes';
import Price from '../stepper/Price';
import { Verify } from '../stepper/Verify';
import { Complete } from '../stepper/Complete';
import React from 'react';

// Mapping step với component tương ứng
export const STEP_COMPONENT_MAP = {
  [CreateTripStepper.pickUp]: Pickup,
  [CreateTripStepper.pickUpExact]: PickupExact,
  [CreateTripStepper.dropOff]: DropOff,
  [CreateTripStepper.dropOffExact]: DropOffExact,
  [CreateTripStepper.vehicle]: Vehicle,
  [CreateTripStepper.chooseRoute]: ChooseRoute,
  [CreateTripStepper.stopovers]: Stopovers,
  [CreateTripStepper.stopoversConfirm]: StopoversConfirm,
  [CreateTripStepper.departureDate]: DepartureDate,
  [CreateTripStepper.departureTime]: DepartureTime,
  [CreateTripStepper.seats]: Seats,
  [CreateTripStepper.approval]: Approval,
  [CreateTripStepper.helmetAvailable]: HelmetAvailable,
  [CreateTripStepper.price]: Price,
  [CreateTripStepper.notes]: Notes,
  [CreateTripStepper.verify]: Verify,
  [CreateTripStepper.complete]: Complete,
};

// Định nghĩa các cấu hình step mặc định theo loại cập nhật
const STEPPER_CONFIGS = {
  // Cấu hình mặc định cho tạo mới
  create: [
    { step: CreateTripStepper.pickUp },
    { step: CreateTripStepper.pickUpExact },
    { step: CreateTripStepper.dropOff },
    { step: CreateTripStepper.dropOffExact },
    { step: CreateTripStepper.vehicle },
    { step: CreateTripStepper.chooseRoute },
    { step: CreateTripStepper.stopovers },
    { step: CreateTripStepper.stopoversConfirm },
    { step: CreateTripStepper.departureDate },
    { step: CreateTripStepper.departureTime },
    { step: CreateTripStepper.seats },
    { step: CreateTripStepper.approval },
    { step: CreateTripStepper.helmetAvailable },
    { step: CreateTripStepper.price },
    { step: CreateTripStepper.notes },
    { step: CreateTripStepper.verify },
    { step: CreateTripStepper.complete }
  ],
  
  // UPDATE_TYPE_ALL - Cho phép cập nhật: điểm dừng (points), giá, auto_accept, max_seat, remaining_seats, notes, overview_polyline
  updateTypeAll: [
    { step: CreateTripStepper.chooseRoute },
    { step: CreateTripStepper.stopovers },
    { step: CreateTripStepper.stopoversConfirm },
    { step: CreateTripStepper.departureDate },
    { step: CreateTripStepper.departureTime },
    { step: CreateTripStepper.seats },
    { step: CreateTripStepper.approval },
    { step: CreateTripStepper.helmetAvailable },
    { step: CreateTripStepper.price },
    { step: CreateTripStepper.notes },
    { step: CreateTripStepper.complete }
  ],
  
  // UPDATE_TYPE_ACCEPTED - Cho phép cập nhật: giá, auto_accept, max_seat, remaining_seats, notes
  updateTypeAccepted: [
    { step: CreateTripStepper.seats },
    { step: CreateTripStepper.approval },
    { step: CreateTripStepper.helmetAvailable },
    { step: CreateTripStepper.price },
    { step: CreateTripStepper.notes },
    { step: CreateTripStepper.complete }
  ],
  
  // UPDATE_TYPE_PENDING - Chỉ cho phép cập nhật giá
  updateTypePending: [
    { step: CreateTripStepper.price },
    { step: CreateTripStepper.complete }
  ],
  
  // UPDATE_TYPE_UNDEFINED - Không cho phép cập nhật
  updateTypeUndefined: []
};

/**
 * Trả về cấu hình các bước dựa vào isEdit và updateType
 * @param isEdit Có đang ở chế độ chỉnh sửa không
 * @param updateType Loại cập nhật (0, 1, 2, 3)
 * @returns Mảng các bước
 */
export const getStepperConfig = (isEdit: boolean, updateType: number) => {
  if (!isEdit) {
    return STEPPER_CONFIGS.create;
  } else {
    // Cấu hình stepper cho chỉnh sửa dựa trên updateType
    switch (updateType) {
      case 1: // UPDATE_TYPE_ALL
        return STEPPER_CONFIGS.updateTypeAll;
      case 2: // UPDATE_TYPE_ACCEPTED
        return STEPPER_CONFIGS.updateTypeAccepted;
      case 3: // UPDATE_TYPE_PENDING
        return STEPPER_CONFIGS.updateTypePending;
      default: // UPDATE_TYPE_UNDEFINED
        return STEPPER_CONFIGS.updateTypeUndefined;
    }
  }
};

/**
 * Lấy bước đầu tiên dựa vào cấu hình stepper
 * @param isEdit Có đang ở chế độ chỉnh sửa không
 * @param updateType Loại cập nhật (0, 1, 2, 3) 
 * @returns Bước đầu tiên
 */
export const getInitialStep = (isEdit: boolean, updateType: number) => {
  const steps = getStepperConfig(isEdit, updateType);
  
  // Nếu có cấu hình, lấy bước đầu tiên từ cấu hình đó
  if (steps.length > 0) {
    return steps[0].step;
  }
  
  // Fallback nếu không tìm thấy bước nào
  return CreateTripStepper.pickUp;
};

/**
 * Kiểm tra xem bước hiện tại có hợp lệ không trong cấu hình
 * @param step Bước cần kiểm tra
 * @param isEdit Có đang ở chế độ chỉnh sửa không
 * @param updateType Loại cập nhật
 * @returns true nếu bước hợp lệ, false nếu không
 */
export const isValidStep = (step: number, isEdit: boolean, updateType: number) => {
  const steps = getStepperConfig(isEdit, updateType);
  return steps.some(config => config.step === step);
};

/**
 * Lấy bước hợp lệ gần nhất cho bước hiện tại
 * @param currentStep Bước hiện tại
 * @param isEdit Có đang ở chế độ chỉnh sửa không
 * @param updateType Loại cập nhật
 * @returns Bước hợp lệ gần nhất
 */
export const getNearestValidStep = (currentStep: number, isEdit: boolean, updateType: number) => {
  const steps = getStepperConfig(isEdit, updateType);
  
  if (steps.length === 0) {
    return null; // Không có bước hợp lệ
  }
  
  // Tìm bước gần nhất
  const stepValues = steps.map(s => s.step);
  
  // Ưu tiên bước đầu tiên nếu không tìm thấy bước gần
  return stepValues[0];
};

/**
 * Lấy cấu hình stepper đầy đủ với components
 * @param isEdit Có đang ở chế độ chỉnh sửa không
 * @param updateType Loại cập nhật (0, 1, 2, 3)
 * @returns Mảng cấu hình đầy đủ với components
 */
export const getStepperConfigWithComponents = (isEdit: boolean, updateType: number) => {
  const steps = getStepperConfig(isEdit, updateType);
  
  // Map cấu hình cơ bản sang cấu hình có component
  return steps.map(stepConfig => {
    const { step } = stepConfig;
    const StepComponent = STEP_COMPONENT_MAP[step as keyof typeof STEP_COMPONENT_MAP];
    
    return {
      step,
      component: StepComponent ? React.createElement(StepComponent) : null
    };
  });
}; 