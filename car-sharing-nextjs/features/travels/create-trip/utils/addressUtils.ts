import { minStopDistance } from "@/constants/minStopDistance";
import { TYPES_PLACE } from "@/constants/typesPlace";
import { TAddress } from "@/types/address";
import { getGeocode } from "use-places-autocomplete";

interface GeocodeCache {
  [cacheKey: string]: TAddress;
}

/**
 * Lấy địa chỉ từ một điểm kết thúc trên bản đồ Google Maps
 * 
 * @param endPoint Điểm kết thúc từ Google Maps
 * @param distance Khoảng cách, có thể là số hoặc object Distance của Google Maps
 * @param geocodeCache Cache để lưu kết quả geocode để tránh gọi API nhiều lần
 * @returns TAddress object hoặc object trống nếu không tìm thấy địa chỉ
 */
export const getAddressFromEndPoint = async (
  endPoint: google.maps.LatLng,
  distance: number | google.maps.Distance | undefined,
  geocodeCache: GeocodeCache = {}
): Promise<TAddress> => {
  // Xử lý distance có thể là số hoặc object
  const distanceValue = typeof distance === 'number' 
    ? distance 
    : distance?.value;

  // Kiểm tra khoảng cách tối thiểu
  if (!distanceValue || distanceValue <= minStopDistance) {
    return {} as TAddress;
  }

  // Tạo cache key từ lat/lng
  const cacheKey = `${endPoint.lat()},${endPoint.lng()}`;

  // Kiểm tra xem có cached result chưa
  if (geocodeCache[cacheKey]) {
    return geocodeCache[cacheKey];
  }

  try {
    const address = await getGeocode({
      location: {
        lat: endPoint.lat(),
        lng: endPoint.lng(),
      },
      language: "vi",
      region: "vn",
    });

    if (!address[0]) return {} as TAddress;

    const result = {
      ...address[0],
      latitude: endPoint.lat(),
      longitude: endPoint.lng(),
      city:
        address[1]?.address_components?.find(({ types }) =>
          types.some((t) => t === TYPES_PLACE.PROVINCE)
        )?.long_name ?? "",
      address_name: address[0].formatted_address,
    } as unknown as TAddress;

    // Lưu kết quả vào cache
    geocodeCache[cacheKey] = result;
    return result;
  } catch (error) {
    console.error("Error getting geocode:", error);
    return {} as TAddress;
  }
}; 