import { TAddress } from "@/types/address";
import { TExtendedAddress } from "./tripDataUtils";

/**
 * Kiểm tra xem một điểm dừng có phải là điểm dừng từ API hay không
 * Sử dụng place_id để xác định nguồn gốc của stopover
 */
export const isStopoverFromApi = (stopover: TAddress | TExtendedAddress): boolean => {
  // Kiểm tra theo place_id (ưu tiên và chắc chắn nhất)
  if (typeof stopover.place_id === 'string' && stopover.place_id.startsWith('related_trip_')) {
    return true;
  }
  
  // Kiểm tra theo thuộc tính đánh dấu đặc biệt
  if ('isFromRelatedTrip' in stopover && (stopover as any).isFromRelatedTrip === true) {
    return true;
  }
  
  // Kiểm tra theo thuộc tính originalIsSelected nếu có
  if ('originalIsSelected' in stopover && (stopover as any).originalIsSelected === true) {
    return true;
  }
  
  // Mặc định không phải từ API
  return false;
};

/**
 * Cập nhật danh sách stopovers, kết hợp điểm dừng hiện có và điểm gợi ý
 * Giữ trạng thái isSelected cho điểm dừng đã chọn
 * Sử dụng tọa độ để nhận dạng các điểm dừng giống nhau
 */
export const mergeStopoverLists = (
  existingStopovers: TExtendedAddress[], 
  newStopovers: TExtendedAddress[]
): TExtendedAddress[] => {
  // Tạo bản đồ từ stopovers hiện có để tra cứu nhanh dựa trên tọa độ
  const stopoverMap = new Map<string, TExtendedAddress>();
  existingStopovers.forEach(stopover => {
    if (stopover.latitude && stopover.longitude) {
      const coordKey = `${stopover.latitude},${stopover.longitude}`;
      stopoverMap.set(coordKey, stopover);
    }
  });

  // Tạo danh sách kết quả
  const result: TExtendedAddress[] = [];
  
  // Thêm stopovers mới, giữ lại trạng thái isSelected nếu đã tồn tại
  newStopovers.forEach(newStopover => {
    if (newStopover.latitude && newStopover.longitude) {
      const coordKey = `${newStopover.latitude},${newStopover.longitude}`;
      const existing = stopoverMap.get(coordKey);
      
      if (existing) {
        // Nếu điểm dừng đã tồn tại, giữ lại isSelected từ điểm hiện có
        result.push({
          ...newStopover,
          isSelected: existing.isSelected
        });
        // Xóa khỏi bản đồ để tránh thêm lại sau này
        stopoverMap.delete(coordKey);
      } else {
        // Nếu là điểm mới, thêm với isSelected dựa vào nguồn gốc của nó
        const isFromApi = isStopoverFromApi(newStopover);
        result.push({
          ...newStopover,
          isSelected: isFromApi
        });
      }
    }
  });
  
  // Thêm các stopovers hiện có còn lại vào kết quả
  stopoverMap.forEach(remainingStopover => {
    result.push(remainingStopover);
  });
  
  return result;
};

/**
 * Xử lý danh sách stopovers từ API
 * Đánh dấu isSelected cho các điểm dừng từ API mới (chưa có trạng thái)
 * Trả về cả danh sách đầy đủ và danh sách chỉ chứa stopovers từ API
 */
export const processApiStopovers = (
  stopovers: TAddress[] | TExtendedAddress[] | undefined
): { 
  allStopovers: TExtendedAddress[]; 
  apiStopovers: TExtendedAddress[] 
} => {
  if (!stopovers || stopovers.length === 0) {
    return { allStopovers: [], apiStopovers: [] };
  }
  
  // Xử lý tất cả stopovers
  const allStopovers = stopovers.map(stop => {
    const isFromApi = isStopoverFromApi(stop);
    const stopAsExtended = stop as TExtendedAddress;
    
    // Chỉ đặt isSelected cho các điểm mới chưa có trạng thái
    if (isFromApi && stopAsExtended.isSelected === undefined) {
      return {
        ...stop,
        isSelected: true,
        originalIsSelected: true
      } as TExtendedAddress;
    }
    
    // Giữ nguyên trạng thái nếu đã có
    return {
      ...stop,
      originalIsSelected: isFromApi ? true : stopAsExtended.originalIsSelected
    } as TExtendedAddress;
  });
  
  // Lọc ra chỉ các stopovers từ API
  const apiStopovers = allStopovers.filter(stop => isStopoverFromApi(stop));
  
  return { allStopovers, apiStopovers };
};

/**
 * Lọc danh sách stopovers từ API
 */
export const filterApiStopovers = (stopovers: TExtendedAddress[]): TExtendedAddress[] => {
  return stopovers.filter(stop => isStopoverFromApi(stop));
};

/**
 * Tìm các stopovers từ API bị thiếu trong danh sách đã chọn
 */
export const findMissingApiStopovers = (
  apiStopovers: TExtendedAddress[],
  selectedStopovers: TExtendedAddress[] = []
): TExtendedAddress[] => {
  // Lấy danh sách các place_id đã chọn
  const selectedIds = selectedStopovers.map(stop => stop.place_id);
  
  // Tìm các stopovers từ API chưa có trong danh sách đã chọn
  return apiStopovers.filter(stop => !selectedIds.includes(stop.place_id));
};

// Định nghĩa TExtendedAddress được import từ tripDataUtils

/**
 * Kiểm tra xem một stopover đã tồn tại trong danh sách hay chưa
 */
export const isStopoverExistInList = (
  stopover: TAddress,
  stopovers: TAddress[]
): boolean => {
  return stopovers.some(
    (s) =>
      s.place_id === stopover.place_id ||
      (s.latitude === stopover.latitude && s.longitude === stopover.longitude)
  );
};

/**
 * Lọc ra các stopovers đã được chọn
 */
export const filterSelectedStopovers = (
  stopovers: TExtendedAddress[]
): TExtendedAddress[] => {
  return stopovers.filter((stop) => stop.isSelected === true);
};

/**
 * Cập nhật trạng thái chọn/bỏ chọn của một stopover
 */
export const updateStopoverSelection = (
  stopovers: TExtendedAddress[],
  stopover: TAddress
): TExtendedAddress[] => {
  return stopovers.map((s) =>
    s.place_id === stopover.place_id
      ? { ...s, isSelected: !s.isSelected }
      : s
  );
};

/**
 * Thêm một stopover mới vào danh sách
 */
export const addStopoverToList = (
  stopover: TAddress,
  stopovers: TExtendedAddress[]
): TExtendedAddress[] => {
  return [...stopovers, { ...stopover, isSelected: true }];
};

/**
 * Kiểm tra xem có phải stopover trùng lặp không
 */
export const isDuplicateStopover = (
  stopover: TAddress,
  index: number,
  arr: TAddress[]
): boolean => {
  return arr.findIndex((it) => it.place_id === stopover.place_id) !== index;
};

/**
 * So sánh hai danh sách stopovers
 */
export const areStopoverListsEqual = (
  list1: TExtendedAddress[],
  list2: TExtendedAddress[]
): boolean => {
  return JSON.stringify(list1) === JSON.stringify(list2);
}; 