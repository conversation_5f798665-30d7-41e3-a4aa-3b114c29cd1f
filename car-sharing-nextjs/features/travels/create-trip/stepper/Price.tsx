import SearchResultTripCard, {
  TripCard,
} from "@/components/features/trip/SearchResultTripCard";
import { NumberField } from "@/components/form/NumberField";
import { Container } from "@/components/ui/Container";
import { TYPES_VIHICLE } from "@/constants/typeVihicle";
import { number2Currency } from "@/helper/currency";
import { loading } from "@/helper/loading";
import { calculateSuggestPrice, getRelatedTrips } from "@/services/trip";
import { cn } from "@/utils";
import { calculateRoute } from "@/utils/calculateRoute";
import { latlng2Str } from "@/utils/latlng2Str";
import {
  comparePriceWithSuggestion,
  getPriceMessage,
  getStatusColor,
  PriceStatus,
} from "@/utils/priceCompare";
import { toastAlert } from "@/utils/toastify";
import dayjs from "dayjs";
import { FC, useEffect, useMemo, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { FooterButton } from "../components/FooterButton";
import { StopoversPrice } from "../components/StopoversPrice";
import { useCreateTrip } from "@/features/travels/create-trip/providers/CreateTripProvider";

type Props = {
  isEdit?: boolean;
};

const Price: FC<Props> = ({ isEdit }) => {
  const [price, setPrice] = useState<any>();
  const { trip, routes, setRoutes, setTripData, prevStep } = useCreateTrip();
  const [relatedTrips, setRelatedTrips] = useState<TripCard[]>(
    [] as TripCard[]
  );
  const { control, getValues, setValue } = useForm({
    defaultValues: {
      seatPrice: 0,
    },
  });
  const [stopPrices, setStopPrices] = useState<any[]>([]);
  useEffect(() => {
    if (!price) return;
    setValue("seatPrice", price.price_avg);
  }, [price, setValue]);

  const calcRoutes = async () => {
    const result = await Promise.all(
      routes.map(
        async (route: any) =>
          await calculateRoute(
            [
              {
                lat: route.startPoint.latitude,
                lng: route.startPoint.longitude,
                address: route.startPoint.address_name,
              },
              {
                lat: route.endPoint.latitude,
                lng: route.endPoint.longitude,
                address: route.endPoint.address_name,
              },
            ],
            google.maps.TravelMode.DRIVING,
            trip.vehicleSelected?.type === TYPES_VIHICLE.MOTOBIKE,
            trip.vehicleSelected?.type === TYPES_VIHICLE.MOTOBIKE
          )
      )
    );
    console.log(result);

    setRoutes((prev: any) => {
      return prev.map((route: any, index: number) => {
        if (index === 0) return route;
        return {
          ...route,
          overviewPolyline: result[index].routes[0].overview_polyline ?? "",
        };
      });
    });
  };

  useEffect(() => {
    calcRoutes();
  }, []);

  useEffect(() => {
    if (!trip.vehicleSelected) return;
    loading.open();

    // Lấy stop_points từ routes hoặc từ trip.stopovers nếu routes không đầy đủ
    let stop_points: string[] = [];

    // 1. Thử lấy từ routes trước (flow ban đầu)
    if (routes && routes.length > 1) {
      stop_points = routes.flatMap((route: any, index: number) => {
        if (index === 0 || index === routes.length - 1) return [];
        const stopPoint = route.endPoint;
        return latlng2Str(stopPoint.latitude, stopPoint.longitude);
      });
    }

    // 2. Nếu không có stop_points từ routes, thử lấy từ trip.stopovers
    if (stop_points.length === 0 && trip.stopovers && Array.isArray(trip.stopovers)) {
      stop_points = trip.stopovers
        .filter((stopover: any) => stopover.isSelected === true)
        .map((stopover: any) => latlng2Str(stopover.latitude, stopover.longitude));
    }

    console.log("Sending stop_points to API:", stop_points);

    calculateSuggestPrice({
      origin: `${trip.pickup?.latitude}, ${trip.pickup?.longitude}`,
      destination: `${trip.dropOff?.latitude}, ${trip.dropOff?.longitude}`,
      type_vehicle: trip.vehicleSelected.type,
      stop_points,
    })
      .then(({ data }) => {
        console.log("🔍 === DEBUGGING Price.tsx API Response ===");
        console.log("🔍 Raw API data:", data);
        console.log("🔍 API data count:", data.length);

        data.sort((a: any, b: any) =>
          a.distance === b.distance ? 0 : a.distance < b.distance ? 1 : -1
        );

        console.log("🔍 After sorting by distance:", data.map((d: any) => ({
          start: d.start_location,
          end: d.end_location,
          startAddr: d.start_address,
          endAddr: d.end_address,
          distance: d.distance
        })));

        setRoutes((prev: any) => {
          return prev.map(({ price, ...rest }: any, index: number) => {
            console.log("🚀 ~ returnprev.map ~ price:", data[index])

            return {
              price: price || data[index].price_avg,
              distance: data[index].distance,
              completionTime: data[index].duration,
              ...rest
            };
          });
        });

        setPrice(data[0]);
        console.log("🔍 Set main price from data[0]:", data[0]);

        // FIX: Pass all data to StopoversPrice, let it handle filtering internally
        // The StopoversPrice component will filter out the main route using our improved logic
        console.log("🔍 Passing ALL API data to StopoversPrice:", data.length);
        console.log("🔍 All data:", data.map((d: any) => ({
          start: d.start_location,
          end: d.end_location,
          startAddr: d.start_address,
          endAddr: d.end_address
        })));

        setStopPrices(data);
        console.log("🔍 === END DEBUGGING Price.tsx ===");
        loading.close();
      })
      .catch((error) => {
        toastAlert({
          type: "error",
          message: error.response.message,
          duration: Infinity,
        });
        loading.close();
        prevStep();
      });
  }, []);

  useEffect(() => {
    return () => {
      setTripData("seatPrice", getValues().seatPrice);
      setRoutes((prev: any) => {
        prev[0].price = getValues().seatPrice;
        return prev;
      });
    };
  }, []);

  useEffect(() => {
    const fetchRelatedTrips = async () => {
      const res = await getRelatedTrips({
        departure_city: trip.pickup?.city?.toString() ?? "",
        destination_city: trip.dropOff?.city?.toString() ?? "",
        departure_date: dayjs(trip.departureDate).format("YYYY-MM-DD"),
        departure_time: trip.departureTime?.toString() ?? "",
        vehicle_type: trip.vehicleSelected?.type ?? 1,
      });
      console.log(res);

      setRelatedTrips(res);
    };

    fetchRelatedTrips();
  }, [
    trip.departureDate,
    trip.departureTime,
    trip.dropOff?.city,
    trip.pickup?.city,
    trip.vehicleSelected?.type,
  ]);

  // Check if there are any selected stopovers
  const hasSelectedStopovers = useMemo(() => {
    if (
      !trip.stopovers ||
      !Array.isArray(trip.stopovers) ||
      trip.stopovers.length === 0
    ) {
      console.log("Price - No stopovers found in trip data");
      return false;
    }

    // Look for stopovers with isSelected property set to true
    const selectedStopovers = trip.stopovers.filter(
      (stopover) => stopover.isSelected === true
    );
    console.log(`Price - Found ${selectedStopovers.length} selected stopovers`);

    return selectedStopovers.length > 0;
  }, [trip.stopovers]);

  const priceData = useWatch({ name: "seatPrice", control });
  const avgPriceCompare = useMemo(() => {
    if (!price) return;
    return comparePriceWithSuggestion(priceData, {
      price_suggest_from: price.price_suggest_from,
      price_suggest_to: price.price_suggest_to,
    });
  }, [price, priceData]);

  if (!price) return null;

  // Helper to determine if we should show stopover pricing
  const shouldShowStopoverPricing = () => {
    // Chỉ cần kiểm tra xem có điểm dừng được chọn không, không cần điều kiện khác
    return hasSelectedStopovers;
  };

  return (
    <Container>
      <div className="flex flex-col gap-4 py-11 items-center">
        <h1 className="text-5xl text-[#58aed8] font-bold text-center mb-4">
          Đặt giá cho chuyến đi
        </h1>
        <p className="text-black text-xl text-left">Giá cho mỗi hành khách</p>
        <NumberField
          max={price.price_max}
          min={price.price_min}
          name="seatPrice"
          control={control}
          renderValue={(value) => number2Currency(value ?? price.price_avg)}
          step={1000}
        />
        <div className="w-1/2">
          <div
            className={cn(
              "text-white p-1 rounded-md max-w-max select-none",
              getStatusColor(avgPriceCompare as PriceStatus)
            )}
          >
            Giá đề xuất: {number2Currency(price.price_suggest_from)} -{" "}
            {number2Currency(price.price_suggest_to)}
          </div>
          <div className="text-black my-4">
            <p>{getPriceMessage(avgPriceCompare as PriceStatus)}</p>
          </div>
          {shouldShowStopoverPricing() && (
            <StopoversPrice stopPrices={stopPrices.length ? stopPrices : []} />
          )}
        </div>

        <div className="w-full max-w-[800px] mx-auto flex flex-col gap-1">
          {!!relatedTrips?.length && (
            <>
              <div className="text-black mb-4 font-bold text-xl text-center">
                Giá các hành trình tương tự
              </div>
              {relatedTrips?.map((trip) => (
                <SearchResultTripCard
                  trip={trip}
                  key={trip.id}
                  className="text-black w-full"
                />
              ))}
            </>
          )}
        </div>

        <FooterButton isEdit={isEdit} enableContinue enableBack />
      </div>
    </Container>
  );
};

export default Price;
