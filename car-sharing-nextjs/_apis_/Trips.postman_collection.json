{"info": {"_postman_id": "211a07a7-6691-4994-9b3f-3afcd12d9a15", "name": "Trips", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "32054576", "_collection_link": "https://bold-trinity-622778.postman.co/workspace/Car-Sharing~8546aa02-fb19-4f66-9808-11c00eee0aee/collection/12242184-211a07a7-6691-4994-9b3f-3afcd12d9a15?action=share&source=collection_link&creator=32054576"}, "item": [{"name": "api/v1/trips", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "", "value": "", "type": "text"}]}, "url": {"raw": "{{url}}/api/v1/trips?page=1&cond[max_seat]=1&cond[departure_date]=2025-03-02&cond[departure_city]=<PERSON>&cond[departure_latitude]=14.2690896&cond[departure_longitude]=109.0788287&cond[destination_city]=Quang Nam&cond[destination_latitude]=15.5704741&cond[destination_longitude]=108.4866911&cond[sort_by]=earliest-departure", "host": ["{{url}}"], "path": ["api", "v1", "trips"], "query": [{"key": "page", "value": "1"}, {"key": "cond[max_seat]", "value": "1"}, {"key": "cond[departure_date]", "value": "2025-03-02"}, {"key": "cond[departure_city]", "value": "<PERSON>"}, {"key": "cond[departure_latitude]", "value": "14.2690896"}, {"key": "cond[departure_longitude]", "value": "109.0788287"}, {"key": "cond[destination_city]", "value": "Quang Nam"}, {"key": "cond[destination_latitude]", "value": "15.5704741"}, {"key": "cond[destination_longitude]", "value": "108.4866911"}, {"key": "cond[sort_by]", "value": "earliest-departure"}, {"key": "cond[auto_accept]", "value": "1", "description": "0 | 1", "disabled": true}, {"key": "cond[allow_smoking]", "value": "0", "description": "0 | 1", "disabled": true}, {"key": "cond[allow_pets]", "value": "0", "description": "0 | 1", "disabled": true}, {"key": "cond[vehicle_type]", "value": "1", "description": "0 | 1", "disabled": true}, {"key": "cond[has_helmet_available]", "value": "1", "description": "0 | 1", "disabled": true}, {"key": "cond[is_ID_card_verified]", "value": "0", "disabled": true}, {"key": "cond[departure_time][before-6]", "value": "before-6", "description": "before-6 | 6-to-12 | 12-to-18 | after-18", "disabled": true}, {"key": "cond[departure_time][6-to-12]", "value": "6-to-12", "disabled": true}, {"key": "cond[departure_time][12-to-18]", "value": "12-to-18", "disabled": true}, {"key": "cond[departure_time][after-18]", "value": "after-18", "disabled": true}]}}, "response": []}, {"name": "api/v1/trips/own", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************yVh7hln4B2oFIs53yTdpEQaHUGf12XmFm5YtJv7S5Pbc6o5jXfGNGxhVDx-Pzb1MkmT-EFvNvXagwzzlNKSOlX0AcLuuQOATPkOZ_cszE6cZen9dTdXShrGTt0v18r46ObdHV0gcFG3pXDyyTfzrlHFpPDE8-2CPeUjItb9e5lQ_W3aGVdninXISTzUjnVAv-qIeAkjiNHxQsH9wkHMQQW9vRLAYsygyUgJCxwu3vXRWyp_euVwQjYhjEe4Vu79ZUF0rWvkO3yekURtNKpzG-XshYUDDHLM9syNCIZqbzgGRHo7mXKCwugEoodCdoNSYRnRbsLgUDV8YFRmxEP_ulkYYymYRLlUVO1PIt8q9XeBfoBy4eVnyMgklDzE5gfuDo0Gnb7OonZstDV-ONoBym7eRU2aq_z1mirnbtNQH-ntZ5KCKwnkOSM_yvBYkbKScTVhGhiRGmNEQ8DLHxrEcue_jxyL5aaDKX3DArIX2gscOtZ0PKo-VUi-OXNx5uZnBn1CzwvnK5HGOKj_RbTHMvXjBhoK-9dN4pmWhkdYQf0HF_BXjp1tIJOAk6Pu0A_oc5zSy0mRzjAqRqRwxGP7f4", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "", "value": "", "type": "text"}]}, "url": {"raw": "{{url}}/api/v1/trips/own?page=1", "host": ["{{url}}"], "path": ["api", "v1", "trips", "own"], "query": [{"key": "page", "value": "1"}]}}, "response": []}, {"name": "api/v1/trips/{id}", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/api/v1/trip/497?departure_latitude=15.4391232&departure_longitude=107.7933119&destination_latitude=15.9999147&destination_longitude=108.2148977", "host": ["{{url}}"], "path": ["api", "v1", "trip", "497"], "query": [{"key": "departure_latitude", "value": "15.4391232"}, {"key": "departure_longitude", "value": "107.7933119"}, {"key": "destination_latitude", "value": "15.9999147"}, {"key": "destination_longitude", "value": "108.2148977"}]}}, "response": []}, {"name": "api/v1/trips/similar", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "", "value": "", "type": "text"}]}, "url": {"raw": "{{url}}/api/v1/trip/similar?departure_city=Dong Thap&destination_city=Thanh pho <PERSON>&departure_date=2024-06-01&vehicle_type=1", "host": ["{{url}}"], "path": ["api", "v1", "trip", "similar"], "query": [{"key": "departure_city", "value": "<PERSON>"}, {"key": "destination_city", "value": "<PERSON><PERSON> pho <PERSON>"}, {"key": "departure_date", "value": "2024-06-01"}, {"key": "vehicle_type", "value": "1"}]}}, "response": []}, {"name": "api/v1/trips/{trip_id}/cancel", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/api/v1/trips/7/cancel", "host": ["{{url}}"], "path": ["api", "v1", "trips", "7", "cancel"]}}, "response": []}, {"name": "api/v1/trips/{trip_id}/complete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{url}}/api/v1/trips/1/complete", "host": ["{{url}}"], "path": ["api", "v1", "trips", "1", "complete"]}}, "response": []}, {"name": "api/v1/trips", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "vehicle_id", "value": "44", "description": "Get from the api get vehicle info", "type": "text"}, {"key": "max_seat", "value": "1", "type": "text"}, {"key": "notes", "value": "<PERSON><PERSON><PERSON>ng chở nhiều hành lý", "type": "text"}, {"key": "auto_accept", "value": "0", "description": "1: Automatically accept reservations, 0: Manually //boolean", "type": "text"}, {"key": "has_helmet_available", "value": "1", "description": "0 | 1: c<PERSON> mang sẵn mủ bảo hiểm", "type": "text"}, {"key": "date", "value": "2025-04-20", "type": "text"}, {"key": "time", "value": "07:00", "type": "text"}, {"key": "completion_times[0]", "value": "1440", "description": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>", "type": "text"}, {"key": "completion_times[1]", "value": "900", "description": "Chuyến ph<PERSON> số 1", "type": "text"}, {"key": "completion_times[2]", "value": "540", "description": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON> số 2", "type": "text"}, {"key": "prices[0]", "value": "600000", "type": "text"}, {"key": "prices[1]", "value": "350000", "type": "text"}, {"key": "prices[2]", "value": "250000", "type": "text"}, {"key": "distances[0]", "value": "70000", "description": "đơn vị: m", "type": "text"}, {"key": "distances[1]", "value": "30000", "type": "text"}, {"key": "distances[2]", "value": "40000", "type": "text"}, {"key": "overview_polyline[0]", "value": "", "description": "nếu có chuyến con (đi<PERSON><PERSON> dừng) thì phần tử 0 rỗng nhưng vấn truyền key 0 = '' lên", "type": "text"}, {"key": "overview_polyline[1]", "value": "ykj_InnpJaMcIs^{Mi^eKmLmGoGqHuPeTyOai@gQqi@PuItE{ExMu`@tPs~@Ti|@aG_|@{F{fAcHyy@aAahAI}e@uEyg@cLug@mImi@g@iw@vJ}s@nTgf@`OoOru@sc@ng@u\\\\he@kj@h_@ym@lOmq@jDwp@oCmu@_Ekp@xBi_@nGoXfUip@nI{n@dCgn@bE{hAvJieEmAwnBeHglB}K}aBs@e}@dH_xAnSswCbQ{pArYebA~a@ufApg@w}Ah^ehA~UqjA`H}~@NcdBhDmlC~G}rDtEojAzHey@tTkpAf_@{pAbg@ioBjM}p@hSsuArKkeAdK}`B|FafClFsmDLknAbDaOvKqR|HiEtT{CfjA_Zl_@mSzk@iT`pAgc@d|A{Ub}@g[la@kU~YgVf{@ybAnu@qs@jl@_d@neAwbAv~@a}@zRsKh^eDpOcCtRoJjUcW|p@okAnj@wvAft@{vBp`@wmB|l@ysEzWo`B`h@_cBn\\\\izAbUylAbc@{lAzcAqhBziAagCbf@idAvd@cw@tw@kaAzs@mp@|gAieAxu@ypA`h@c_AfU{a@l[uv@v_AigCr]un@xy@mw@rXk\\\\hVag@xd@ycA`h@emAlc@_qAdSsn@h_@qbBzPsw@`Vuy@nVyq@|[q~@tQcw@|j@uoD`k@gpDjWixAxJs[p[_p@f[k_@bQsNx_Agv@fp@uh@|UqNnYaDtVKzTyCn]kNvXmTfYy_@|Yij@vRsVbZuTl[eLne@kD`_@uFfX{Lly@uj@xt@kg@n]w_@`WqWjSgEnWqDfOcMd`@sz@n_@os@zf@mo@vp@al@v`@qi@hTmSnQ{G~aAwJt`C}TxlCkZn|@_JnZ^vYjDx_ApQpb@dIvQlAjUsDvWwQdf@uw@pXyc@rTgU|XoMz_AuWfr@wSrz@wRpp@e`@r[_Gh[tDvPzB`Oa@zc@mNlWiM~Sy_@xZmt@f^{z@zP_Txi@eMlLuK`Nob@dJin@bRif@j[gjArJuq@b@o`@_Bu[iLk`AMmm@|FwZhOyWrP}I`b@mLbw@iu@l~@g}@dTqStReL~U{EhWt@rc@`MtY|A`O{BzN_HnOoRtG{VI_[mCqQcPkRaBo@pAoEnKuWhZa]dReMv]kO~HqKfH}_@jLsGpGaIlQka@lAoK~JeSdY}XjKyTrMeKn@bDdCtEtUpChGKhGcGpBe@~@rC`BlClIjEtMgDvT{`@hPy[tQcOjMgFfHoCJwGvJgDlEuDfT_MrWq\\\\`GeCxGKbAwEfAHZuCz@mh@JkOmSup@yJ}Vm@_@", "type": "text"}, {"key": "overview_polyline[2]", "value": "ykj_InnpJaMcIs^{Mi^eKmLmGoGqHuPeTyOai@gQqi@PuItE{ExMu`@tPs~@Ti|@aG_|@{F{fAcHyy@aAahAI}e@uEyg@cLug@mImi@g@iw@vJ}s@nTgf@`OoOru@sc@ng@u\\\\he@kj@h_@ym@lOmq@jDwp@oCmu@_Ekp@xBi_@nGoXfUip@nI{n@dCgn@bE{hAvJieEmAwnBeHglB}K}aBs@e}@dH_xAnSswCbQ{pArYebA~a@ufApg@w}Ah^ehA~UqjA`H}~@NcdBhDmlC~G}rDtEojAzHey@tTkpAf_@{pAbg@ioBjM}p@hSsuArKkeAdK}`B|FafClFsmDLknAbDaOvKqR|HiEtT{CfjA_Zl_@mSzk@iT`pAgc@d|A{Ub}@g[la@kU~YgVf{@ybAnu@qs@jl@_d@neAwbAv~@a}@zRsKh^eDpOcCtRoJjUcW|p@okAnj@wvAft@{vBp`@wmB|l@ysEzWo`B`h@_cBn\\\\izAbUylAbc@{lAzcAqhBziAagCbf@idAvd@cw@tw@kaAzs@mp@|gAieAxu@ypA`h@c_AfU{a@l[uv@v_AigCr]un@xy@mw@rXk\\\\hVag@xd@ycA`h@emAlc@_qAdSsn@h_@qbBzPsw@`Vuy@nVyq@|[q~@tQcw@|j@uoD`k@gpDjWixAxJs[p[_p@f[k_@bQsNx_Agv@fp@uh@|UqNnYaDtVKzTyCn]kNvXmTfYy_@|Yij@vRsVbZuTl[eLne@kD`_@uFfX{Lly@uj@xt@kg@n]w_@`WqWjSgEnWqDfOcMd`@sz@n_@os@zf@mo@vp@al@v`@qi@hTmSnQ{G~aAwJt`C}TxlCkZn|@_JnZ^vYjDx_ApQpb@dIvQlAjUsDvWwQdf@uw@pXyc@rTgU|XoMz_AuWfr@wSrz@wRpp@e`@r[_Gh[tDvPzB`Oa@zc@mNlWiM~Sy_@xZmt@f^{z@zP_Txi@eMlLuK`Nob@dJin@bRif@j[gjArJuq@b@o`@_Bu[iLk`AMmm@|FwZhOyWrP}I`b@mLbw@iu@l~@g}@dTqStReL~U{EhWt@rc@`MtY|A`O{BzN_HnOoRtG{VI_[mCqQcPkRaBo@pAoEnKuWhZa]dReMv]kO~HqKfH}_@jLsGpGaIlQka@lAoK~JeSdY}XjKyTrMeKn@bDdCtEtUpChGKhGcGpBe@~@rC`BlClIjEtMgDvT{`@hPy[tQcOjMgFfHoCJwGvJgDlEuDfT_MrWq\\\\`GeCxGKbAwEfAHZuCz@mh@JkOmSup@yJ}Vm@_@", "type": "text"}, {"key": "points[0][address_name]", "value": "<PERSON><PERSON> <PERSON> - 392 <PERSON><PERSON><PERSON><PERSON>", "type": "text"}, {"key": "points[0][city_name]", "value": "T<PERSON><PERSON> <PERSON><PERSON>", "type": "text"}, {"key": "points[0][latitude]", "value": "10.799713", "type": "text"}, {"key": "points[0][longitude]", "value": "106.680138", "type": "text"}, {"key": "points[1][address_name]", "value": "<PERSON><PERSON><PERSON><PERSON>ò Sữa Long Thành A", "type": "text"}, {"key": "points[1][city_name]", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "points[1][latitude]", "value": "10.865448", "type": "text"}, {"key": "points[1][longitude]", "value": "107.4816009", "type": "text"}, {"key": "points[2][address_name]", "value": "tt. <PERSON><PERSON>, Thăng Bình, Quảng Nam, Vietnam", "type": "text"}, {"key": "points[2][city_name]", "value": "Quảng Nam", "type": "text"}, {"key": "points[2][latitude]", "value": "15.7380811", "type": "text"}, {"key": "points[2][longitude]", "value": "108.3612602", "type": "text"}]}, "url": {"raw": "{{url}}/api/v1/trips", "host": ["{{url}}"], "path": ["api", "v1", "trips"]}}, "response": []}, {"name": "api/v1/trips/{trip_id}", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "prices[0]", "value": "260000", "type": "text", "disabled": true}, {"key": "auto_accept", "value": "1", "type": "text", "disabled": true}, {"key": "max_seat", "value": "3", "type": "text", "disabled": true}, {"key": "notes", "value": "<PERSON><PERSON><PERSON><PERSON> có lưu ý gì", "type": "text", "disabled": true}, {"key": "max_seat", "value": "2", "type": "text"}, {"key": "notes", "value": "<PERSON><PERSON><PERSON>ng chở nhiều hành lý", "type": "text"}, {"key": "auto_accept", "value": "0", "type": "text"}, {"key": "date", "value": "2025-04-20", "type": "text"}, {"key": "time", "value": "07:00", "type": "text"}, {"key": "completion_times[0]", "value": "1440", "type": "text"}, {"key": "completion_times[1]", "value": "900", "type": "text"}, {"key": "completion_times[2]", "value": "540", "type": "text"}, {"key": "prices[0]", "value": "600000", "type": "text"}, {"key": "prices[1]", "value": "350000", "type": "text"}, {"key": "prices[2]", "value": "250000", "type": "text"}, {"key": "distances[0]", "value": "70000", "type": "text"}, {"key": "distances[1]", "value": "30000", "type": "text"}, {"key": "distances[2]", "value": "40000", "type": "text"}, {"key": "overview_polyline[0]", "value": "", "type": "text"}, {"key": "overview_polyline[1]", "value": "_vfaAwkvlSyBqDkDsKaAyGHoEj@}CpCaHpEkJ|JyS|_@sw@jSgb@~H{TrDiQlBaOv@eR~@ki@~BuxALmZ[}NcFw~@cE_q@_@kIyB_W}EqYcHyVaYq|@qLo^{Xw{@yRcn@__@ujAcBoGiEqTuAyKuA{Qw@qk@Sk^s@miAu@woAk@yUg@wImDi\\uQwjAiRimAyOqaAgKea@yFwPuVss@}Vus@q[e~@c]waAqGgSsCsM{AiKoAuRU}Ku@ot@}A}tA}@ycA`@gJlAqKnDeOzWkw@fa@klAzNsc@~CeN`BkL|Euo@hFgt@pCi`@rAaRjDw[zByLvGqVxPql@~Ws~@pMgd@`G_W`DaU`AqMp@wWT_c@vAq{BZsbAq@cR}AySkCc]uIoeAwBq\\QkJf@yTpE{b@jL_`AnGgi@tTyhBtKk}@xBcRrBqZl@ec@kAcg@_Cir@iAu\\uC_n@sBcSqAyNk@{TRoQXyQpAq|@|@wn@jDebCjE}_@v@aG\\}B|@oODkHi@qLwCcRsNms@iN{r@qAuJaAeNU{PnAgk@HeF`@wNb@yb@QyKgAsTaE_]sH{_@cK}g@_Fo^_JwzAeB_Q_CuOsAeHaOqw@iEgSgJ_YcIoQqOmZ}MaWgMuQsG}IuGwKoAkCqBeHoByLqCeV}Ea[}EeUyEqQcGeRwG_QyGyOoGoM_R{]mMqVmw@a{AsTaa@oQqUw^u^eWyVoRoRwc@mb@iIeGmGaDgOmEo|@yO{d@gIelBg[m]wFkZuGeHcC}MaGiJoFcOgLoEmEuKwM}KiRiJ}Rw^yw@_^_v@suAmzCcg@sgA_}@ymBgJcR_MiTsR{XaPsRqYmYsUaRsb@iYu]_V{TeRkLuLiSuVmUe[_Wm\\{Z{]m~@u}@gn@cm@{UeUgQ{N{JsGcUmMun@i]s|A_{@wIoGyK}JaM}Oo^qo@u\\mm@{IiOcXmb@qe@wm@ci@up@akAaxAonB}{Bg|@ucAwt@u|@a`@yd@sPkSmFkHqNsUcLoV{CsI_HgW{@cEaDySyBoWmRqfDqQkuCkQqrCwKo|B_N{wCwK}jByJa{A_GsaAc@wSAgUh@sXbDqn@fDwk@~FicAbEip@`Gqt@tR{rBtNawAvFsg@`T{nBtRc_BpUglBv@mR?{E}@eRk@kFoDuQuBsHmHqQ_`@g}@kq@oaBeVom@wEwMqGgUoJ_g@uGaf@uG_g@{CiZoCqMiGuVuOu]sCwFeEyJwDqKmEkHgCwEeG}IiEoEwHaGgFqD{FkAm_AeY_]kKsLsEsDmBi@u@}@gD", "type": "text"}, {"key": "overview_polyline[2]", "value": "ykj_InnpJaMcIs^{Mi^eKmLmGoGqHuPeTyOai@gQqi@PuItE{ExMu`@tPs~@Ti|@aG_|@{F{fAcHyy@aAahAI}e@uEyg@cLug@mImi@g@iw@vJ}s@nTgf@`OoOru@sc@ng@u\\\\he@kj@h_@ym@lOmq@jDwp@oCmu@_Ekp@xBi_@nGoXfUip@nI{n@dCgn@bE{hAvJieEmAwnBeHglB}K}aBs@e}@dH_xAnSswCbQ{pArYebA~a@ufApg@w}Ah^ehA~UqjA`H}~@NcdBhDmlC~G}rDtEojAzHey@tTkpAf_@{pAbg@ioBjM}p@hSsuArKkeAdK}`B|FafClFsmDLknAbDaOvKqR|HiEtT{CfjA_Zl_@mSzk@iT`pAgc@d|A{Ub}@g[la@kU~YgVf{@ybAnu@qs@jl@_d@neAwbAv~@a}@zRsKh^eDpOcCtRoJjUcW|p@okAnj@wvAft@{vBp`@wmB|l@ysEzWo`B`h@_cBn\\\\izAbUylAbc@{lAzcAqhBziAagCbf@idAvd@cw@tw@kaAzs@mp@|gAieAxu@ypA`h@c_AfU{a@l[uv@v_AigCr]un@xy@mw@rXk\\\\hVag@xd@ycA`h@emAlc@_qAdSsn@h_@qbBzPsw@`Vuy@nVyq@|[q~@tQcw@|j@uoD`k@gpDjWixAxJs[p[_p@f[k_@bQsNx_Agv@fp@uh@|UqNnYaDtVKzTyCn]kNvXmTfYy_@|Yij@vRsVbZuTl[eLne@kD`_@uFfX{Lly@uj@xt@kg@n]w_@`WqWjSgEnWqDfOcMd`@sz@n_@os@zf@mo@vp@al@v`@qi@hTmSnQ{G~aAwJt`C}TxlCkZn|@_JnZ^vYjDx_ApQpb@dIvQlAjUsDvWwQdf@uw@pXyc@rTgU|XoMz_AuWfr@wSrz@wRpp@e`@r[_Gh[tDvPzB`Oa@zc@mNlWiM~Sy_@xZmt@f^{z@zP_Txi@eMlLuK`Nob@dJin@bRif@j[gjArJuq@b@o`@_Bu[iLk`AMmm@|FwZhOyWrP}I`b@mLbw@iu@l~@g}@dTqStReL~U{EhWt@rc@`MtY|A`O{BzN_HnOoRtG{VI_[mCqQcPkRaBo@pAoEnKuWhZa]dReMv]kO~HqKfH}_@jLsGpGaIlQka@lAoK~JeSdY}XjKyTrMeKn@bDdCtEtUpChGKhGcGpBe@~@rC`BlClIjEtMgDvT{`@hPy[tQcOjMgFfHoCJwGvJgDlEuDfT_MrWq\\\\`GeCxGKbAwEfAHZuCz@mh@JkOmSup@yJ}Vm@_@", "type": "text"}, {"key": "points[0][address_name]", "value": "<PERSON><PERSON> <PERSON> - 392 <PERSON><PERSON><PERSON><PERSON>", "type": "text"}, {"key": "points[0][city_name]", "value": "T<PERSON><PERSON> <PERSON><PERSON>", "type": "text"}, {"key": "points[0][latitude]", "value": "10.799713", "type": "text"}, {"key": "points[0][longitude]", "value": "106.680138", "type": "text"}, {"key": "points[1][address_name]", "value": "<PERSON><PERSON><PERSON><PERSON> Bò Sữa Long Thành B", "type": "text"}, {"key": "points[1][city_name]", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "points[1][latitude]", "value": "10.865448", "type": "text"}, {"key": "points[1][longitude]", "value": "107.4816009", "type": "text"}, {"key": "points[2][address_name]", "value": "tt. <PERSON><PERSON>, Thăng Bình, Quảng Nam, Vietnam", "type": "text"}, {"key": "points[2][city_name]", "value": "Quảng Nam", "type": "text"}, {"key": "points[2][latitude]", "value": "15.7380811", "type": "text"}, {"key": "points[2][longitude]", "value": "108.3612602", "type": "text"}, {"key": "vehicle_id", "value": "2", "type": "text"}]}, "url": {"raw": "{{url}}/api/v1/trip/303/update", "host": ["{{url}}"], "path": ["api", "v1", "trip", "303", "update"]}}, "response": []}]}