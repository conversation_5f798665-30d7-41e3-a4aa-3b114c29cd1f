# System Patterns: carShare

## Architecture Overview

The carShare platform follows a modern client-server architecture with a clear separation between the backend API and frontend application:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Next.js        │     │  Laravel API    │     │  External       │
│  Frontend       │◄────►  Backend        │◄────►  Services       │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Backend Architecture (Laravel)

- RESTful API design pattern
- MVC (Model-View-Controller) architecture
- Repository pattern for data access
- Service layer for business logic
- Authentication via Laravel Passport (OAuth2)

### Frontend Architecture (Next.js)

- React component-based architecture
- Server-side rendering for improved SEO and performance
- Client-side state management with React Query
- Component composition pattern
- Responsive design using modern CSS approaches

## Key Design Patterns

### Repository Pattern

Used in the backend to abstract data access logic from business logic, making the codebase more maintainable and testable.

### Service Layer Pattern

Implements business logic in dedicated service classes, keeping controllers thin and focused on request/response handling.

### Factory Pattern

Used for creating complex objects, particularly in the context of creating different types of notifications or payment processors.

### Observer Pattern

Implemented for the notification system, where events trigger observers that handle different types of notifications (email, SMS, in-app).

### Strategy Pattern

Applied to payment processing, allowing different payment methods to be used interchangeably.

## Data Flow

### Booking Flow

1. User searches for available vehicles
2. User selects a vehicle and booking dates
3. System checks availability and calculates price
4. User confirms booking and provides payment
5. System processes payment and creates booking
6. Notifications sent to both renter and owner
7. Owner confirms booking
8. Booking is finalized

### Authentication Flow

1. User registers or logs in
2. Backend validates credentials
3. JWT token issued to authenticated user
4. Token stored in client and used for subsequent requests
5. Token refresh mechanism maintains session

## Database Schema

Core entities in the system include:

- Users (owners and renters)
- Vehicles
- Bookings
- Payments
- Reviews
- Messages
- Notifications

Relationships follow standard relational database patterns with appropriate foreign keys and constraints.

## API Structure

The API follows RESTful conventions with resource-based endpoints:

- `/api/auth/*` - Authentication endpoints
- `/api/users/*` - User management
- `/api/vehicles/*` - Vehicle listings and management
- `/api/trips/*` - Trip creation and management
- `/api/bookings/*` - Booking creation and management
- `/api/payments/*` - Payment processing
- `/api/reviews/*` - Rating and review system
- `/api/messages/*` - Communication system

## Trip Management Pattern

### Multi-segment Trip Model

The system implements a flexible trip model that supports both direct trips and multi-segment trips with intermediate stops:

#### Direct Trip (No Intermediate Stops)

```
┌────────────┐                         ┌────────────┐
│            │                         │            │
│  Origin    │────────────────────────►│ Destination│
│  Point     │                         │            │
└────────────┘                         └────────────┘
      │                                      │
      │                                      │
      ▼                                      ▼
┌─────────────────────────────────────────────────┐
│                                                 │
│                   Segment 1                     │
│                                                 │
└─────────────────────────────────────────────────┘
```

#### Multi-segment Trip (With Intermediate Stops)

```
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│            │     │            │     │            │     │            │
│  Origin    │────►│  Stop 1    │────►│  Stop 2    │────►│ Destination│
│  Point     │     │            │     │            │     │            │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
      │                  │                  │                  │
      │                  │                  │                  │
      ▼                  ▼                  ▼                  ▼
┌────────────┐     ┌────────────┐     ┌────────────┐
│            │     │            │     │            │
│ Segment 1  │     │ Segment 2  │     │ Segment 3  │
│            │     │            │     │            │
└────────────┘     └────────────┘     └────────────┘
```

Each segment contains:

- Distance data (in meters)
- Completion time (in minutes)
- Pricing information
- Route visualization (polyline)

### Trip Creation Pattern

The trip creation process follows these steps:

1. Driver selects vehicle from their registered vehicles
2. Driver defines the route:
   - **For direct trips**: Origin and destination only
   - **For multi-segment trips**: Origin, intermediate stops, and destination
   - **For general locations**: If only general location is provided (city/district/country name), additional steps are required:
     - **PickUpExact**: Precise pickup address entry for general origin locations
     - **DropOffExact**: Precise dropoff address entry for general destination locations
3. System calculates distances, times, and suggested prices:
   - **For direct trips**: Single segment calculation
   - **For multi-segment trips**: Calculation for each segment
4. Driver can adjust seats, pricing, and preferences per trip
5. System generates the complete trip:
   - **For direct trips**: Single segment with full route data
   - **For multi-segment trips**: Multiple segments with individual route data
6. Trip becomes available for searching and booking

### Trip Data Structure

The trip data structure follows an indexed array pattern for segments and points:

- Points: `points[0]`, `points[1]`, ..., `points[n]` representing each location
- Segments: Arrays for `completion_times`, `distances`, `prices`, and `overview_polyline` with matching indices

This pattern allows for efficiently representing both direct trips and complex multi-segment trips with the same data structure.

#### Direct Trip Data Pattern

```
points[0] = origin
points[1] = destination
completion_times[0] = full trip time
distances[0] = full distance
prices[0] = full price
overview_polyline[0] = encoded route
```

#### Multi-segment Trip Data Pattern

```
points[0] = origin
points[1...n-1] = intermediate stops
points[n] = destination
completion_times[0...n-1] = segment times
distances[0...n-1] = segment distances
prices[0...n-1] = segment prices
overview_polyline[0] = empty (when stops exist)
overview_polyline[1...n-1] = encoded routes per segment
```

### Stopover Points Structure

Stopover points (intermediate stops) are a key component of the multi-segment trip model:

#### Stopover Point Data Structure

Each stopover point contains the following data:

```typescript
interface StopoverPoint {
  address_name: string; // Full address of the stopover
  city_name: string; // City name for the stopover
  latitude: number; // Geographic latitude
  longitude: number; // Geographic longitude
  is_parent_route?: number; // Flag for filtering (1 = parent route)
  trip_id?: number; // Associated trip ID
  is_selected?: boolean; // Selection status in UI
}
```

#### Stopover Selection Flow

The stopover selection process follows these steps:

1. After route selection, the system identifies potential stopover points along the route
2. Each potential stopover is presented with a checkbox for selection
3. User selects desired stopovers by checking the corresponding checkboxes
4. System collects selected stopovers and presents them for confirmation
5. Upon confirmation, stopovers are integrated into the trip data structure as points

#### Stopover Confirmation Structure

The stopover confirmation page displays the complete route with all selected points:

```
Origin → Stopover 1 → Stopover 2 → ... → Stopover n → Destination
```

Each segment between consecutive points is treated as a separate route segment with its own:

- Distance calculation
- Time estimation
- Price calculation
- Route visualization

#### Stopover Editing Behavior

When editing existing trips with stopovers:

1. Existing stopover points are pre-checked in the selection interface
2. Points with `is_parent_route=1` and matching trip IDs are filtered out
3. For existing stopover points, the system doesn't need to call Google API again
4. The stopover confirmation step is always shown, even if no stopovers are selected
5. The destination_address of element[i] in relatedTrips should match the departure_address of element[i+1]

#### Trip Editing Types

The system supports different types of trip editing based on reservation status:

1. **UPDATE_TYPE_ALL (Type 1)**:
   - Cho phép cập nhật các điểm dừng (points), giá, auto_accept, max_seat, remaining_seats, notes, overview_polyline
   - Không cho phép thay đổi điểm đón/trả (pickup/dropoff) hay phương tiện (vehicle)
   - Sử dụng khi chuyến đi chưa có đặt chỗ
   - Hiển thị luồng stepper cho phép chỉnh sửa các trường được phép

2. **UPDATE_TYPE_ACCEPTED (Type 2)**:
   - Cho phép cập nhật giá, auto_accept, max_seat, remaining_seats, notes
   - Không thể cập nhật điểm dừng (points) hoặc lộ trình
   - Sử dụng khi chuyến đi đã có đặt chỗ được chấp nhận
   - Hiển thị luồng stepper giới hạn tập trung vào các trường có thể chỉnh sửa

3. **UPDATE_TYPE_PENDING (Type 3)**:
   - Chỉ cho phép cập nhật giá
   - Sử dụng khi chuyến đi có đặt chỗ đang chờ phê duyệt
   - Hiển thị luồng stepper tối thiểu chỉ với việc chỉnh sửa giá

4. **UPDATE_TYPE_UNDEFINED (Type 0)**:
   - Không cho phép cập nhật
   - Sử dụng cho chuyến đi đã hoàn thành hoặc bị hủy
   - Không hiển thị luồng stepper nào

Các loại chỉnh sửa này đảm bảo tính toàn vẹn dữ liệu trong khi cung cấp tính linh hoạt phù hợp dựa trên trạng thái chuyến đi.

#### Stopover API Representation

In API requests, stopovers are represented as elements in the `points` array:

```json
{
  "points": [
    {
      "address_name": "180 Cao Lỗ, phường 4, Quận 8, Hồ Chí Minh",
      "city_name": "Hồ Chí Minh",
      "latitude": 10.7369,
      "longitude": 106.6334
    },
    {
      "address_name": "Đồng Nai",
      "city_name": "Đồng Nai",
      "latitude": 10.9559,
      "longitude": 106.8435
    },
    {
      "address_name": "Phú Yên",
      "city_name": "Phú Yên",
      "latitude": 13.1056,
      "longitude": 109.2929
    },
    {
      "address_name": "Ha Noi Calido Hotel",
      "city_name": "Hà Nội",
      "latitude": 21.0245,
      "longitude": 105.8412
    }
  ],
  "completion_times": [null, 120, 480, 960],
  "distances": [null, 35000, 520000, 1200000],
  "prices": [null, 50000, 250000, 500000],
  "overview_polyline": [
    "",
    "encoded_polyline_1",
    "encoded_polyline_2",
    "encoded_polyline_3"
  ]
}
```

## Security Patterns

- Authentication via OAuth2 (Laravel Passport)
- Authorization using role-based access control
- Input validation on both client and server
- CSRF protection
- Rate limiting for API endpoints
- Data encryption for sensitive information
- Secure payment processing via third-party providers

## Scalability Considerations

- Stateless API design for horizontal scaling
- Caching strategies for frequently accessed data
- Queue-based processing for background tasks
- Database indexing for performance optimization
- CDN integration for static assets

## State Management Patterns

### Context API

The application uses React Context API for global state management in several key areas:

1. **User Authentication**: Manages user login state, tokens, and permissions.
2. **Trip Creation/Editing**: Manages the multi-step process of creating or editing a trip.
3. **Booking Flow**: Manages the booking process state across multiple steps.

These context providers are typically set up at the top level of feature-specific components and make state available to all child components in that feature.

Example of Auth Context:

```tsx
export const AuthProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Authentication methods
  const login = async (credentials) => {
    /* ... */
  };
  const logout = async () => {
    /* ... */
  };
  const register = async (userData) => {
    /* ... */
  };

  // Context value
  const value = {
    user,
    loading,
    login,
    logout,
    register,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
```

### Local Component State

For UI-specific states that don't need to be shared across components, we use React's useState and useReducer hooks. This approach is used for:

1. **Form Inputs**: Managing form field values and validation states.
2. **UI Toggles**: Managing open/closed states of modals, dropdowns, etc.
3. **Pagination**: Managing current page, items per page, etc.

### Local Storage Persistence

For certain user preferences and session data that should persist across page reloads, we use local storage:

1. **Language Preference**: Remembering the user's selected language.
2. **Theme Selection**: Remembering the user's dark/light mode preference.
3. **Recent Searches**: Storing recent search queries for quick access.
4. **Step Progress**: Storing progress in multi-step flows to allow users to continue where they left off.

### Stopover Selection State Management

The stopover selection component uses a sophisticated state management approach that combines:

1. **React useState** for tracking:

   - Currently checked stopovers (`checkedIds`)
   - Grouped stopovers by category (`groupedStopovers`)

2. **React useRef** for detecting changes:

   - Previous state of stopovers (`prevStopoverState`)
   - Previous count of suggested stops (`prevSuggestedStops`)

3. **localStorage persistence** for maintaining state during navigation:

   - Saving stopover groupings to prevent loss during navigation
   - Recovering state when returning to the component

4. **Intelligent comparison logic** for detecting:
   - Newly added stopovers (by comparing current with previous states)
   - Duplicate stopovers (using multiple matching criteria)

```tsx
// State declarations
const [checkedIds, setCheckedIds] = useState<TStopoverAddress[]>([]);
const [groupedStopovers, setGroupedStopovers] = useState<{
  suggestedStops: TExtendedAddress[];
  relatedStops: TExtendedAddress[];
}>({
  suggestedStops: [],
  relatedStops: [],
});

// Refs for tracking previous state
const prevSuggestedStops = useRef<TExtendedAddress[]>([]);

// State persistence with localStorage
const saveStopoverState = useCallback(
  (state: {
    relatedStops: TExtendedAddress[];
    suggestedStops: TExtendedAddress[];
  }) => {
    try {
      localStorage.setItem("edit_trip_stopovers", JSON.stringify(state));
      console.log("✅ Stopover state saved to localStorage", state);
    } catch (error) {
      console.error("Failed to save stopover state to localStorage", error);
    }
  },
  []
);

// Detection of newly added stopovers
useEffect(() => {
  const currentStops = groupedStopovers.suggestedStops;

  // Only proceed if we have previous stops to compare with
  if (prevSuggestedStops.current.length > 0 && currentStops.length > prevSuggestedStops.current.length) {
    // Find the newly added stopover by comparing current with previous
    const newStopover = currentStops.find(current =>
      !prevSuggestedStops.current.some(prev =>
        // Check if stopover exists in previous list by place_id or coordinates
        // ...comparison logic...
      )
    );

    // If we found a new stopover, auto-check only that one
    if (newStopover) {
      setCheckedIds(prev => [...prev, { ...newStopover, isFromRelatedTrip: false }]);
    }
  }

  // Update the reference with current stops
  prevSuggestedStops.current = [...currentStops];
}, [groupedStopovers.suggestedStops]);
```

This approach enables several advanced features:

- Automatic checking of newly added stopovers
- Prevention of data loss during navigation
- Intelligent handling of duplicate stopovers
- Proper maintenance of related trip stopovers
