# Product Context: carShare

## Requirements

### Core Features

#### Vehicle Management

- Đăng ký và quản lý xe cho thuê (Vehicle registration and management)

  - Thêm thông tin xe: loại xe, hãng, mẫu, biển số, màu sắc (Add vehicle details: type, brand, model, license plate, color)
  - <PERSON><PERSON><PERSON> thực biển số xe (License plate validation)
  - <PERSON>ân loại xe (ô tô, xe máy) (Vehicle categorization - car, motorcycle)
  - Quản lý trạng thái xe (Vehicle status management)

- Upload và quản lý hình ảnh xe (Vehicle image upload and management)

  - T<PERSON><PERSON> lên nhiều hình ảnh (Multiple image upload)
  - Tối ưu hóa hình ảnh (Image optimization)
  - Quản lý thư viện ảnh xe (Vehicle gallery management)

- Cài đặt lịch cho thuê và giá (Availability and pricing management)

  - Lịch sẵn sàng cho thuê (Availability calendar)
  - <PERSON><PERSON><PERSON> cơ bản và gi<PERSON> đ<PERSON> (Base and dynamic pricing)
  - <PERSON><PERSON><PERSON> theo thời gian (ng<PERSON><PERSON> thường/cuối tuần) (Time-based pricing - weekday/weekend)
  - <PERSON><PERSON><PERSON><PERSON> gi<PERSON> cho thuê dài hạn (Discounts for longer rentals)

- Theo dõi trạng thái xe (Vehicle condition tracking)
  - Ghi nhận tình trạng xe trước/sau khi cho thuê (Pre/post-rental condition reporting)
  - Báo cáo hư hỏng (Damage reporting)
  - Theo dõi bảo trì (Maintenance tracking)
  - Theo dõi số km (Mileage tracking)

#### Booking System

- Tìm kiếm xe theo vị trí và thời gian (Vehicle search by location and time)

  - Tìm kiếm trên bản đồ (Map-based search)
  - Bộ lọc nâng cao (Advanced filters)
  - Sắp xếp kết quả (Result sorting)
  - Phân trang kết quả tìm kiếm (Search result pagination)

- Đặt xe với xác nhận thời gian (Booking with time confirmation)

  - Chọn ngày và giờ (Date and time selection)
  - Chọn số lượng ghế/hành khách (Seat/passenger selection)
  - Yêu cầu đặt xe (Booking request submission)
  - Xác nhận đặt xe (Booking confirmation)
  - Sửa đổi đặt xe (Booking modification)
  - Hủy đặt xe (Booking cancellation)
  - Lịch sử đặt xe (Booking history)

- Thanh toán qua Alepay (Payment via Alepay)

  - Xử lý thanh toán an toàn (Secure payment processing)
  - Xác nhận thanh toán (Payment confirmation)
  - Lịch sử thanh toán (Payment history)
  - Xử lý hoàn tiền (Refund processing)
  - Quản lý đặt cọc (Security deposit management)

- Quản lý đơn đặt xe (Booking management)
  - Xem tất cả đơn đặt xe (View all bookings)
  - Lọc theo trạng thái (Filter by status)
  - Xác nhận/từ chối đơn đặt (Confirm/reject bookings)
  - Chính sách hủy (Cancellation policies)
  - Chính sách trả muộn (Late return policies)

#### User Features

- Đăng ký/đăng nhập tài khoản (User registration/login)

  - Đăng ký bằng email (Email registration)
  - Đăng nhập bằng mạng xã hội (Social media login)
  - Xác minh email (Email verification)
  - Đặt lại mật khẩu (Password reset)
  - Xác thực hai yếu tố (Two-factor authentication)

- Xác thực người dùng (User verification)

  - Xác minh CMND/CCCD (ID verification)
  - Xác minh số điện thoại (Phone verification)
  - Xác minh giấy phép lái xe (Driver's license verification)
  - Hồ sơ xác minh (Verification profile)

- Quản lý hồ sơ cá nhân (Profile management)

  - Thông tin cá nhân (Personal information)
  - Ảnh đại diện (Profile photo)
  - Tiểu sử (Bio)
  - Tùy chọn thông báo (Notification preferences)
  - Tùy chọn cá nhân (Personal preferences)

- Đánh giá và nhận xét (Ratings and reviews)
  - Đánh giá chủ xe (Rate vehicle owners)
  - Đánh giá người thuê (Rate renters)
  - Viết nhận xét (Write reviews)
  - Quản lý đánh giá (Review management)
  - Hệ thống uy tín (Reputation system)

#### Maps Integration

- Hiển thị vị trí xe trên bản đồ (Display vehicle location on map)

  - Tích hợp Google Maps (Google Maps integration)
  - Hiển thị xe có sẵn (Display available vehicles)
  - Hiển thị thông tin xe khi nhấp vào (Display vehicle info on click)

- Tìm kiếm xe theo khu vực (Search vehicles by area)

  - Tìm kiếm theo bán kính (Search by radius)
  - Tìm kiếm theo quận/huyện (Search by district)
  - Tìm kiếm theo vị trí hiện tại (Search by current location)

- Tính toán khoảng cách (Distance calculation)
  - Khoảng cách đến xe (Distance to vehicle)
  - Tính toán lộ trình (Route calculation)
  - Ước tính thời gian di chuyển (Travel time estimation)
  - Hiển thị điểm đón và trả xe (Display pickup and drop-off points)

#### Notifications

- Thông báo đặt xe (Booking notifications)

  - Thông báo yêu cầu đặt xe mới (New booking request notifications)
  - Thông báo xác nhận đặt xe (Booking confirmation notifications)
  - Thông báo nhắc nhở trước chuyến đi (Trip reminder notifications)
  - Thông báo hủy đặt xe (Booking cancellation notifications)

- Thông báo trạng thái thanh toán (Payment status notifications)

  - Thông báo thanh toán thành công (Payment success notifications)
  - Thông báo thanh toán thất bại (Payment failure notifications)
  - Thông báo hoàn tiền (Refund notifications)

- Thông báo nhắc nhở (Reminder notifications)

  - Nhắc nhở trước chuyến đi (Pre-trip reminders)
  - Nhắc nhở trả xe (Return reminders)
  - Nhắc nhở đánh giá (Review reminders)

- Push notifications qua Firebase (Push notifications via Firebase)
  - Thông báo thời gian thực (Real-time notifications)
  - Thông báo trong ứng dụng (In-app notifications)
  - Thông báo nền (Background notifications)
  - Quản lý đăng ký thiết bị (Device registration management)

#### Communication System

- Tin nhắn trong ứng dụng (In-app messaging)

  - Nhắn tin giữa chủ xe và người thuê (Messaging between owners and renters)
  - Lịch sử tin nhắn (Message history)
  - Thông báo tin nhắn mới (New message notifications)
  - Đính kèm tệp/hình ảnh (File/image attachments)

- Hệ thống hỗ trợ (Support system)
  - Tạo ticket hỗ trợ (Support ticket creation)
  - Theo dõi ticket (Ticket tracking)
  - Mục FAQ (FAQ section)
  - Trung tâm trợ giúp (Help center)
  - Hỗ trợ trực tuyến (Live chat support)

#### Multilingual Support

- Hỗ trợ đa ngôn ngữ (Multilingual support)

  - Tiếng Việt (Vietnamese)
  - Tiếng Anh (English)
  - Chuyển đổi ngôn ngữ (Language switching)

- Định dạng thời gian và tiền tệ theo locale (Localized date and currency formats)
  - Định dạng ngày tháng (Date formatting)
  - Định dạng tiền tệ (Currency formatting)
  - Định dạng số (Number formatting)

### Technical Requirements

#### Backend (Laravel)

- RESTful API endpoints

  - API versioning (API versioning)
  - Tài liệu API với OpenAPI (API documentation with OpenAPI)
  - Xử lý lỗi API (API error handling)
  - Giới hạn tốc độ API (API rate limiting)

- OAuth2 authentication

  - Quản lý token JWT (JWT token management)
  - Kiểm soát truy cập dựa trên vai trò (Role-based access control)
  - Quản lý quyền (Permission management)
  - Quản lý phiên (Session management)

- File storage trên Google Cloud (File storage on Google Cloud)

  - Quản lý tải lên tệp (File upload management)
  - Xử lý và tối ưu hóa hình ảnh (Image processing and optimization)
  - Kiểm soát truy cập tệp (File access control)

- Redis caching

  - Bộ nhớ đệm truy vấn (Query caching)
  - Bộ nhớ đệm phản hồi (Response caching)
  - Chiến lược vô hiệu hóa bộ nhớ đệm (Cache invalidation strategies)

- Queue system với Redis (Queue system with Redis)

  - Xử lý công việc nền (Background job processing)
  - Tác vụ theo lịch (Scheduled tasks)
  - Xử lý công việc thất bại (Failed job handling)

- MySQL database
  - Di chuyển cơ sở dữ liệu (Database migrations)
  - Eloquent ORM
  - Lập chỉ mục cơ sở dữ liệu (Database indexing)
  - Tối ưu hóa truy vấn (Query optimization)

#### Frontend (Next.js)

- Responsive design

  - Thiết kế ưu tiên thiết bị di động (Mobile-first design)
  - Tương thích với tất cả kích thước thiết bị (Compatible with all device sizes)
  - Thành phần UI nhất quán (Consistent UI components)
  - Tuân thủ khả năng truy cập (Accessibility compliance)

- Server-side rendering

  - Kết xuất phía máy chủ cho các trang quan trọng (Server-side rendering for critical pages)
  - Tạo trang tĩnh khi thích hợp (Static site generation where appropriate)
  - Kết xuất phía máy khách cho nội dung động (Client-side rendering for dynamic content)
  - Tạo trang tĩnh gia tăng (Incremental static regeneration)

- Real-time updates

  - Tích hợp WebSocket (WebSocket integration)
  - Thông báo thời gian thực (Real-time notifications)
  - Nhắn tin thời gian thực (Real-time messaging)
  - Cập nhật thời gian thực (Real-time updates)

- Progressive web app features

  - Hỗ trợ ngoại tuyến (Offline support)
  - Thêm vào màn hình chính (Add to home screen)
  - Thông báo đẩy (Push notifications)
  - Đồng bộ hóa nền (Background sync)

- Google Maps integration
  - Hiển thị bản đồ (Map display)
  - Tìm kiếm dựa trên bản đồ (Map-based search)
  - Tự động hoàn thành địa chỉ (Address autocomplete)
  - Tính toán lộ trình (Route calculation)

#### Integration Requirements

- Alepay payment gateway

  - Quy trình xử lý thanh toán (Payment processing workflow)
  - Xác minh thanh toán (Payment verification)
  - Xử lý hoàn tiền (Refund processing)
  - Webhook thanh toán (Payment webhooks)

- Firebase notifications

  - Thông báo đẩy (Push notifications)
  - Thông báo trong ứng dụng (In-app notifications)
  - Quản lý đăng ký thiết bị (Device registration management)
  - Phân tích thông báo (Notification analytics)

- Google Cloud Storage

  - Lưu trữ hình ảnh xe (Vehicle image storage)
  - Lưu trữ tài liệu (Document storage)
  - Kiểm soát truy cập (Access control)
  - Tích hợp CDN (CDN integration)

- Twilio SMS

  - Thông báo SMS (SMS notifications)
  - Xác minh số điện thoại (Phone verification)
  - Nhắc nhở SMS (SMS reminders)
  - Theo dõi giao hàng SMS (SMS delivery tracking)

- SMTP email
  - Xác minh email (Email verification)
  - Thông báo email (Email notifications)
  - Mẫu email (Email templates)
  - Theo dõi giao hàng email (Email delivery tracking)

#### Security Requirements

- Secure authentication

  - Lưu trữ mật khẩu an toàn (Secure password storage)
  - Xác thực nhiều yếu tố (Multi-factor authentication)
  - Quản lý phiên (Session management)
  - Khóa tài khoản sau nhiều lần đăng nhập thất bại (Account lockout after failed attempts)

- Data encryption

  - Mã hóa dữ liệu lưu trữ (Data encryption at rest)
  - Mã hóa dữ liệu truyền tải (HTTPS) (Data encryption in transit - HTTPS)
  - Xử lý dữ liệu nhạy cảm (Sensitive data handling)
  - Tuân thủ quyền riêng tư (Privacy compliance)

- Input validation

  - Xác thực đầu vào phía máy khách (Client-side input validation)
  - Xác thực đầu vào phía máy chủ (Server-side input validation)
  - Làm sạch dữ liệu đầu vào (Input data sanitization)
  - Xác thực loại dữ liệu (Data type validation)

- XSS protection

  - Mã hóa đầu ra (Output encoding)
  - Chính sách bảo mật nội dung (Content Security Policy)
  - Làm sạch HTML (HTML sanitization)
  - Bảo vệ khỏi tấn công XSS (Protection against XSS attacks)

- CSRF protection
  - Token CSRF (CSRF tokens)
  - Xác thực nguồn gốc (Origin verification)
  - Bảo vệ khỏi tấn công CSRF (Protection against CSRF attacks)
  - Kiểm tra tiêu đề (Header checks)

#### Performance Requirements

- Fast page load times

  - Thời gian tải trang < 2 giây (Page load time < 2s)
  - Thời gian tương tác < 3 giây (Time to interactive < 3s)
  - Tối ưu hóa First Contentful Paint (Optimize First Contentful Paint)
  - Tối ưu hóa Largest Contentful Paint (Optimize Largest Contentful Paint)

- Responsive UI

  - Giao diện người dùng mượt mà (Smooth user interface)
  - Không có độ trễ đầu vào (No input lag)
  - Hiệu ứng chuyển đổi mượt mà (Smooth transitions)
  - Tối ưu hóa Cumulative Layout Shift (Optimize Cumulative Layout Shift)

- Efficient database queries

  - Thời gian phản hồi API < 200ms (API response time < 200ms)
  - Tối ưu hóa truy vấn cơ sở dữ liệu (Database query optimization)
  - Lập chỉ mục thích hợp (Proper indexing)
  - Giảm thiểu số lượng truy vấn (Minimize number of queries)

- API rate limiting

  - Giới hạn tốc độ dựa trên người dùng (User-based rate limiting)
  - Giới hạn tốc độ dựa trên IP (IP-based rate limiting)
  - Giới hạn tốc độ dựa trên endpoint (Endpoint-based rate limiting)
  - Xử lý vượt quá giới hạn (Rate limit exceeding handling)

- Image optimization
  - Nén hình ảnh (Image compression)
  - Định dạng hình ảnh hiện đại (Modern image formats)
  - Kích thước hình ảnh phản hồi (Responsive image sizes)
  - Tải lười biếng (Lazy loading)

## Market Problem

Traditional car ownership is becoming increasingly expensive and inefficient, with many vehicles sitting idle for most of their lifespan. Meanwhile, people who need occasional access to vehicles face high rental costs from commercial services.

## Solution

carShare creates a peer-to-peer marketplace that connects car owners with potential renters, allowing for more efficient use of existing vehicles while providing income to owners and affordable access to renters.

## Target Users

### Car Owners

- People who own vehicles but don't use them full-time
- Individuals looking to offset the costs of car ownership
- Small fleet owners seeking additional revenue streams

### Car Renters

- Urban dwellers who need occasional access to a vehicle
- Travelers visiting areas with limited public transportation
- People who need specific vehicle types for short periods
- Individuals who prefer not to own a car but need occasional access

## User Experience Goals

### For Car Owners

- Simple, streamlined process to list vehicles
- Flexible control over availability and pricing
- Secure verification of potential renters
- Protection against damage or misuse
- Reliable payment processing

### For Renters

- Easy discovery of available vehicles
- Transparent pricing with no hidden fees
- Streamlined booking process
- Clear communication with vehicle owners
- Reliable access to booked vehicles

## Competitive Landscape

- Commercial car rental companies (Enterprise, Hertz, etc.)
- Existing car sharing platforms (Turo, Getaround)
- Ride-sharing services (Uber, Lyft)
- Public transportation and bike/scooter sharing

## Unique Value Proposition

- More personalized experience than commercial rentals
- Lower overhead costs allowing for competitive pricing
- Community-based approach with direct owner-renter relationships
- Flexible rental periods from hours to months
- Wide variety of vehicle types and models

## Business Model

- Service fee percentage from each completed booking
- Premium listing features for owners
- Insurance partnerships
- Verification service fees

## Regulatory Considerations

- Insurance requirements for peer-to-peer vehicle sharing
- Local transportation regulations
- User identity verification requirements
- Payment processing regulations
- Data privacy compliance (GDPR, CCPA, etc.)
