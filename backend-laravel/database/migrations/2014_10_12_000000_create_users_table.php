<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('middle_name')->nullable();
            $table->string('last_name');
            $table->string('full_name');
            $table->string('email')->unique()->nullable();
            $table->string('phone')->unique()->nullable();
            $table->string('avatar_url')->nullable();
            $table->string('address')->nullable();
            $table->string('identity_card')->nullable();
            $table->text('bio')->nullable();
            $table->date('birthday')->comment('yyyy-mm-dd');
            $table->string('preference')->nullable();
            $table->text('history')->nullable()->comment('Search history of the user');
            $table->enum('register_from', ['web', 'app']);
            $table->enum('gender', ['male', 'female']);
            $table->tinyInteger('status')->default(1)->comment('User status:1 - Active, 2 - Flag penalty, 3 - Block');
            $table->boolean('is_noti_promo_email')->default(false)->comment('Receive notified about news, deals and free stuff via email');
            $table->boolean('is_noti_message_email')->default(false)->comment('Receive notified when messages arrive via email');
            $table->boolean('has_motor')->default(false);
            $table->boolean('has_car')->default(false);
            $table->boolean('is_ID_card_verified')->default(false);
            $table->boolean('is_recommend_app')->default(false);
            $table->string('reason_close_account')->nullable()->comment('Reason for closing account');
            $table->string('feedback')->nullable()->comment('Share ideas for improvement');
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('phone_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
