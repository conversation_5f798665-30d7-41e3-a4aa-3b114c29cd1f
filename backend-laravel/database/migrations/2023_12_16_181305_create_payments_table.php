<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('reservation_id');
            $table->string('transaction_code');
            $table->unsignedBigInteger('user_id')->comment('Payer');
            $table->tinyInteger('status')->default(0);
            $table->decimal('price', 10, 2)->comment('Price per seat');
            $table->string('payment_gateway')->comment('Payment gateway');
            $table->string('method')->nullable()->comment('Payment method');
            $table->string('token')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
