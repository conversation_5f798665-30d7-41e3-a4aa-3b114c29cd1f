<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reservations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('Passenger');
            $table->unsignedBigInteger('trip_id');
            $table->tinyInteger('number_of_seats')->default(0);
            $table->tinyInteger('payment_method')->nullable();
            $table->decimal('actual_amount_paid', 10, 2)->default(0);
            $table->boolean('is_paid')->default(false);
            $table->tinyInteger('status')->default(1)->comment('Status of trip: 1 - Open(reserved), 2 - Accepted, 3 - Paid, 4 - Finished, 5 - Cancel, 6: Reject, 7: Outdated');
            $table->string('cancel_reason')->nullable();
            $table->datetime('accept_deadline');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('trip_id')->references('id')->on('trips')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reservations');
    }
};
