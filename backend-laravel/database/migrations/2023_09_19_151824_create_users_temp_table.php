<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $table = 'users_temp';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users_temp', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique()->nullable();
            $table->string('phone')->unique()->nullable();
            $table->string('otp');
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->unsignedTinyInteger('count')->default(1)->comment('Count the number of requests to send OTP');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users_temp');
    }
};
