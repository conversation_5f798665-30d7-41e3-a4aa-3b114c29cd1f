<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trips', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('vehicle_id');
            $table->string('departure_address')->comment('Departure address full text');
            $table->string('departure_city')->comment('To display');
            $table->string('departure_city_normalized')->comment('To search');
            $table->string('departure_latitude')->comment('Departure latitude');
            $table->string('departure_longitude')->comment('Departure longitude');
            $table->date('departure_date');
            $table->time('departure_time');
            $table->string('destination_address')->comment('Destination address full text');
            $table->string('destination_city')->comment('To display');
            $table->string('destination_city_normalized')->comment('To search');
            $table->string('destination_latitude')->comment('Destination latitude');
            $table->string('destination_longitude')->comment('Destination address longitude');
            $table->string('route_id');
            $table->text('overview_polyline')->nullable()->comment('Used to draw road maps');
            $table->boolean('is_parent_route');
            $table->tinyInteger('max_seat')->comment('Maximum seating capacity');
            $table->tinyInteger('remaining_seats')->nullable()->comment('Available seating capacity');
            $table->decimal('price', 10, 2)->default(0)->comment('Price per seat');
            $table->float('distance')->default(0)->comment('Distance from departure to destination');
            $table->float('completion_time')->default(0);
            $table->boolean('auto_accept')->default(false)->comment('Passengers can make an immediate reservation');
            $table->boolean('has_helmet_available')->default(false);
            $table->tinyText('notes')->nullable();
            $table->tinyInteger('status')->default(1)->comment('Status of trip: 1 - Open, 2 - Finished, 3 - Cancel');
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('vehicle_id')->references('id')->on('vehicles');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trips');
    }
};
