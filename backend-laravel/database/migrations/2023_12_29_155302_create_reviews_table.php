<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('trip_id');
            $table->unsignedBigInteger('reviewee_id');
            $table->unsignedBigInteger('reviewer_id');
            $table->string('comment');
            $table->tinyInteger('rating')->comment('1 - Very disappointing, 2 - Disappointing, 3 - Okay, 4 - Good, 5 - Excellent');
            $table->timestamps();

            $table->foreign('reviewee_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('reviewer_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
