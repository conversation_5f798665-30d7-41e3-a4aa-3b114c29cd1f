<?php

use App\Admin\Extensions\Form\CKEditor;

/**
 * Laravel-admin - admin builder based on <PERSON><PERSON>.
 *
 * <AUTHOR> <https://github.com/z-song>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 * Encore\Admin\Form::forget(['map', 'editor']);
 *
 * Or extend custom form field:
 * Encore\Admin\Form::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 */
Encore\Admin\Form::forget(['map', 'editor']);
Encore\Admin\Form::extend('ckeditor', CKEditor::class);
