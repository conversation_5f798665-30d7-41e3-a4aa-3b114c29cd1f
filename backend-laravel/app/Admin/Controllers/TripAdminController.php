<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Post\CancelTrip;
use App\Models\Trip;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class TripAdminController extends AdminController
{
    public static $js = [
        '/packages/ckeditor/ckeditor.js',
        '/packages/ckeditor/adapters/jquery.js',
    ];

    protected $view = 'admin.ckeditor';

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Trip';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Trip);
        $grid->actions(function ($actions) {
            $actions->add(new CancelTrip);
            $actions->disableDelete();
            $actions->disableView();
            $actions->disableEdit();
        });
        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->in('status', __('Status'))->multipleSelect([
                Trip::STATUS_OPEN => 'OPEN',
                Trip::STATUS_FINISHED => 'FINISHED',
                Trip::STATUS_CANCELLED => 'CANCELED',
                Trip::STATUS_OUTDATED => 'OUTDATED',
            ]);
            $filter->equal('is_parent_route', __('Parent Route'))->select([
                1 => 'TRUE',
                0 => 'FALSE'
            ]);
        });
        $grid->disableCreateButton();
        $grid->model()->orderBy('id', 'desc');
        $grid->column('id', __('Id'))->sortable();
        $grid->column('driver.full_name', __('Driver'));
        $grid->column('vehicle.model', __('Vehicle'));
        $grid->column('departure_address', __('Departure Address'));
        $grid->column('destination_address', __('Destination Address'));
        $grid->column('departure_date', __('Departure Date'))->display(function () {
            return Carbon::parse($this->departure_date)->format('Y-m-d');
        })->sortable();
        $grid->column('price', __('Price'))->display(function () {
            return number_format($this->price);
        })->sortable();
        $grid->column('is_parent_route', __('Parent Route'))->display(function () {
            return $this->is_parent_route ? 'TRUE' : 'FALSE';
        })->sortable();
        $grid->column('status', __('Status'))->using([
            Trip::STATUS_OPEN => 'OPEN',
            Trip::STATUS_FINISHED => 'FINISHED',
            Trip::STATUS_CANCELLED => 'CANCELED',
            Trip::STATUS_OUTDATED => 'OUTDATED',
        ])->dot([
            1 => 'warning',
            2 => 'success',
            3 => 'danger',
            4 => 'danger',
        ])->sortable();
        $grid->column('created_at', __('Created at'))->display(function () {
            return Carbon::parse($this->created_at)->format('Y-m-d H:i:s');
        })->sortable();
        $grid->column('updated_at', __('Updated at'))->display(function () {
            return Carbon::parse($this->updated_at)->format('Y-m-d H:i:s');
        })->sortable();

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Trip::findOrFail($id));
        $show->field('id', __('Id'));
        $show->field('full_name', __('Full Name'));
        $show->field('avatar_url', __('Avatar'));
        $show->field('status', __('status'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Trip);

        $form->text('driver.full_name', __('Driver'))->disable();
        $form->text('vehicle.model', __('Vehicle'))->disable();
        $form->text('departure_address', __('Departure Address'))->disable();
        $form->date('departure_date', __('Departure Date'))->disable();
        $form->time('departure_time', __('Departure Time'))->disable();
        $form->text('destination_address', __('Destination Address'))->disable();
        $form->number('max_seat', __('Max Seat'))->disable();
        $form->number('remaining_seats', __('Remaining Seats'))->disable();
        $form->number('price', __('Price'))->disable();
        $form->number('distance', __('Distance'))->disable();
        $form->date('completion_time', __('Completion Time'))->disable();
        $form->switch('is_parent_route', __('Parent Route'))->disable();
        $form->switch('auto_accept', __('Auto Accept'))->disable();
        $form->text('notes', __('Notes'))->disable();
        $form->text('cancel_reason', __('Cancel Reason'))->disable();

        $form->select('status', __('Status'))->options([
            Trip::STATUS_OPEN => 'OPEN',
            Trip::STATUS_FINISHED => 'FINISHED',
            Trip::STATUS_CANCELLED => 'CANCELED',
            Trip::STATUS_OUTDATED => 'OUTDATED',
        ])->default(1);

        return $form;
    }
}
