<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Post\CancelReservertion;
use App\Models\Payment;
use App\Models\Reservation;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class ReservationAdminController extends AdminController
{
    public static $js = [
        '/packages/ckeditor/ckeditor.js',
        '/packages/ckeditor/adapters/jquery.js',
    ];

    protected $view = 'admin.ckeditor';

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Reservation';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Reservation);
        $grid->actions(function ($actions) {
            $actions->add(new CancelReservertion);
            $actions->disableDelete();
            $actions->disableView();
            $actions->disableEdit();
        });
        $grid->disableCreateButton();
        $grid->model()->orderBy('id', 'desc');

        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->in('status', __('Status Payment'))->multipleSelect([
                Reservation::STATUS_PENDING_CONFIRM => 'PENDING CONFIRM',
                Reservation::STATUS_ACCEPTED => 'ACCEPTED',
                Reservation::STATUS_PAID => 'PAID',
                Reservation::STATUS_FINISHED => 'FINISHED',
                Reservation::STATUS_CANCELLED => 'CANCELED',
            ]);
        });

        $grid->column('id', __('Id'))->sortable();
        $grid->column('user.full_name', __('User Booking'))->display(function () {
            return $this->user->full_name.' (Id: '.$this->user_id.')' ?? '';
        })->sortable();

        $grid->column('trip_id', __('Trip Id'))->sortable();

        $grid->column('trip.departure_address', __('Departure Address'));
        $grid->column('trip.destination_address', __('Destination Address'));
        $grid->column('number_of_seats', __('Number Of Seats'))->sortable();
        $grid->column('total_amount', __('Total Amount'))->display(function () {
            return number_format($this->total_amount);
        })->sortable();

        $grid->column('status', __('Status Reservation'))->using([
            Reservation::STATUS_PENDING_CONFIRM => 'PENDING CONFIRM',
            Reservation::STATUS_ACCEPTED => 'ACCEPTED',
            Reservation::STATUS_PAID => 'PAID',
            Reservation::STATUS_FINISHED => 'FINISHED',
            Reservation::STATUS_CANCELLED => 'CANCELED',
        ])->dot([
            Reservation::STATUS_PENDING_CONFIRM => 'warning',
            Reservation::STATUS_ACCEPTED => 'primary',
            Reservation::STATUS_PAID => 'success',
            Reservation::STATUS_FINISHED => 'info',
            Reservation::STATUS_CANCELLED => 'danger',
        ])->sortable();

        $grid->column('created_at', __('Created at'))->display(function () {
            return Carbon::parse($this->created_at)->format('Y-m-d H:i:s');
        })->sortable();
        $grid->column('updated_at', __('Updated at'))->display(function () {
            return Carbon::parse($this->updated_at)->format('Y-m-d H:i:s');
        })->sortable();

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Payment::findOrFail($id));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Payment);

        return $form;
    }
}
