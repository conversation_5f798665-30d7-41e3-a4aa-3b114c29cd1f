<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Post\RefundPayment;
use App\Models\Payment;
use App\Models\Reservation;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class PaymentAdminController extends AdminController
{
    public static $js = [
        '/packages/ckeditor/ckeditor.js',
        '/packages/ckeditor/adapters/jquery.js',
    ];

    protected $view = 'admin.ckeditor';

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Payment';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Payment);
        $grid->actions(function ($actions) {
            $actions->add(new RefundPayment);
            $actions->disableDelete();
            $actions->disableView();
            $actions->disableEdit();
        });
        $grid->disableCreateButton();
        $grid->model()->orderBy('id', 'desc');
        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->in('reservation.status', __('Status Reservation'))->multipleSelect([
                Reservation::STATUS_PENDING_CONFIRM => 'PENDING CONFIRM',
                Reservation::STATUS_ACCEPTED => 'ACCEPTED',
                Reservation::STATUS_PAID => 'PAID',
                Reservation::STATUS_FINISHED => 'FINISHED',
                Reservation::STATUS_CANCELLED => 'CANCELED',
            ]);

            $filter->in('status', __('Status Payment'))->multipleSelect([
                Payment::STATUS_OPEN => 'OPEN',
                Payment::STATUS_PAID => 'PAID',
                Payment::STATUS_CANCELLED => 'CANCELED',
                Payment::STATUS_REFUNDED => 'REFUNDED',
            ]);
        });

        $grid->column('id', __('Id'))->sortable();
        $grid->column('reservation.user.full_name', __('User'))->display(function () {
            return $this->reservation->user->full_name.' (Id: '.$this->reservation->user_id.')' ?? '';
        })->sortable();

        $grid->column('reservation_id', __('Reservation Id'))->sortable();
        $grid->column('reservation.status', __('Status Reservation'))->using([
            Reservation::STATUS_PENDING_CONFIRM => 'PENDING CONFIRM',
            Reservation::STATUS_ACCEPTED => 'ACCEPTED',
            Reservation::STATUS_PAID => 'PAID',
            Reservation::STATUS_FINISHED => 'FINISHED',
            Reservation::STATUS_CANCELLED => 'CANCELED',
        ])->dot([
            Reservation::STATUS_PENDING_CONFIRM => 'warning',
            Reservation::STATUS_ACCEPTED => 'primary',
            Reservation::STATUS_PAID => 'success',
            Reservation::STATUS_FINISHED => 'info',
            Reservation::STATUS_CANCELLED => 'danger',
        ])->sortable();
        $grid->column('reservation.trip_id', __('Trip Id'))->sortable();
        $grid->column('reservation.number_of_seats', __('Number Of Seats'))->sortable();

        $grid->column('transaction_code', __('Transaction Code'))->sortable();
        $grid->column('payment_gateway', __('Payment Gateway'))->sortable();
        $grid->column('price', __('Price'))->display(function () {
            return number_format($this->price);
        })->sortable();
        $grid->column('status', __('Status Payment'))->using([
            Payment::STATUS_OPEN => 'OPEN',
            Payment::STATUS_PAID => 'PAID',
            Payment::STATUS_CANCELLED => 'CANCELED',
            Payment::STATUS_REFUNDED => 'REFUNDED',
        ])->dot([
            Payment::STATUS_OPEN => 'primary',
            Payment::STATUS_PAID => 'success',
            Payment::STATUS_CANCELLED => 'danger',
            Payment::STATUS_REFUNDED => 'secondary',
        ])->sortable();
        $grid->column('created_at', __('Created at'))->display(function () {
            return Carbon::parse($this->created_at)->format('Y-m-d H:i:s');
        })->sortable();
        $grid->column('updated_at', __('Updated at'))->display(function () {
            return Carbon::parse($this->updated_at)->format('Y-m-d H:i:s');
        })->sortable();

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Payment::findOrFail($id));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Payment);

        return $form;
    }
}
