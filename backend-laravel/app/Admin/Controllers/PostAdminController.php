<?php

namespace App\Admin\Controllers;

use App\Models\Post;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class PostAdminController extends AdminController
{
    public static $js = [
        '/packages/ckeditor/ckeditor.js',
        '/packages/ckeditor/adapters/jquery.js',
    ];

    protected $view = 'admin.ckeditor';

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Post';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Post);
        $grid->model()->orderBy('id', 'desc');
        $grid->column('id', __('Id'))->sortable();
        $grid->column('image', __('Image'))->image('', 50, 50);
        $grid->column('title', __('Title'));
        $grid->column('type', __('Type'))->using(Post::TYPE_POST, 'Unknown')->sortable();
        $grid->column('status', __('Status'))->using([
            0 => 'OFF',
            1 => 'ON',
        ], 'Unknown')->dot([
            0 => 'danger',
            1 => 'success',
        ], 'warning')->sortable();
        $grid->column('created_at', __('Created at'))->display(function () {
            return Carbon::parse($this->created_at)->format('Y-m-d H:i:s');
        })->sortable();
        $grid->column('updated_at', __('Updated at'))->display(function () {
            return Carbon::parse($this->updated_at)->format('Y-m-d H:i:s');
        })->sortable();

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Post::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('title', __('Title'));
        $show->field('content', __('Content'));
        $show->field('image', __('Image'));
        $show->field('type', __('Type'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Post);

        $form->text('title', __('Title'));
        $form->ckeditor('content', __('Content'));
        $form->image('image', __('Image'));
        $form->select('type', __('Type'))->options(Post::TYPE_POST)->default(1);
        $form->switch('status', __('Status'))->default(1);

        return $form;
    }
}
