<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class UserAdminController extends AdminController
{
    public static $js = [
        '/packages/ckeditor/ckeditor.js',
        '/packages/ckeditor/adapters/jquery.js',
    ];

    protected $view = 'admin.ckeditor';

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'User';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new User);
        $grid->model()->orderBy('id', 'desc');
        $grid->column('id', __('Id'))->sortable();
        $grid->column('avatar_url', __('Avatar'))->image('', 50, 50);
        $grid->column('full_name', __('Full Name'));
        $grid->column('phone', __('Phone'));
        $grid->column('email', __('Email'));
        $grid->column('gender', __('Gender'))->sortable();
        $grid->column('status', __('Status'))->switch([
            0 => 'OFF',
            1 => 'ON',
        ])->default(1)->sortable();
        $grid->column('is_avatar_verified', __('Avatar Verified'))->using([
            0 => 'Open',
            1 => 'Verified',
            2 => 'Rejected',
        ], 'Unknown')->dot([
            0 => 'warning',
            1 => 'success',
            2 => 'danger',
        ], 'warning')->sortable();

        $grid->column('is_ID_card_verified', __('Card Verified'))->using([
            0 => 'Open',
            1 => 'Verified',
            2 => 'Rejected',
        ], 'Unknown')->dot([
            0 => 'warning',
            1 => 'success',
            2 => 'danger',
        ], 'warning')->sortable();

        $grid->column('created_at', __('Created at'))->display(function () {
            return Carbon::parse($this->created_at)->format('Y-m-d H:i:s');
        })->sortable();
        $grid->column('updated_at', __('Updated at'))->display(function () {
            return Carbon::parse($this->updated_at)->format('Y-m-d H:i:s');
        })->sortable();

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param  mixed  $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(User::findOrFail($id));
        $show->field('id', __('Id'));
        $show->field('full_name', __('Full Name'));
        $show->field('avatar_url', __('Avatar'));
        $show->field('status', __('status'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new User);

        $form->text('full_name', __('Full Name'));
        $form->email('email', __('Email'));
        $form->text('phone', __('Phone'));
        $form->image('avatar_url', __('Avatar'))->disable();
        $form->select('is_avatar_verified', __('Avatar Verified'))->options([
            User::IS_CARD_OPEN => 'Open',
            User::IS_CARD_VERIFIED => 'Verified',
            User::IS_CARD_REJECTED => 'Rejected',
        ])->default(0);
        $form->multipleFile('identity_card', __('Identity Card'))->disable();
        $form->select('is_ID_card_verified', __('Identity Card Verified'))->options([
            User::IS_CARD_OPEN => 'Open',
            User::IS_CARD_VERIFIED => 'Verified',
            User::IS_CARD_REJECTED => 'Rejected',
        ])->default(0);

        $form->switch('status', __('Status'))->default(1);

        return $form;
    }
}
