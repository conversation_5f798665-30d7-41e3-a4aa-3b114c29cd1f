<?php

namespace App\Admin\Actions\Post;

use App\Jobs\SendBookingCancelEmail;
use App\Jobs\SendFirebaseNotification;
use App\Models\Reservation;
use App\Models\Trip;
use App\Services\ReservationService;
use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CancelTrip extends RowAction
{
    public $name = 'Cancel trip';

    public function handle(Model $trip)
    {
        if ($trip->status != Trip::STATUS_OPEN) {
            return $this->response()->error('Cancel trip error');
        }
        try {
            DB::beginTransaction();

            $cancel_reason = 'Chuyến của bạn đã bị hủy bởi admin #' . auth()->guard('admin')->id();
            $reservations = [];
            // Cancel trip child
            if ($trip->is_parent_route) {
                $tripChilds = Trip::where('route_id', $trip->route_id)->where('user_id', Auth::id())->get();
                foreach ($tripChilds as $tripChild) {
                    if ($tripChild->reservations) {
                        $reservations[] = $tripChild->reservations;
                    }
                    $tripChild->update(['status' => Trip::STATUS_CANCELLED, 'cancel_reason' => $cancel_reason]);
                }
            }

            if ($trip->reservations) {
                $reservations[] = $trip->reservations;
            }
            // Cancel trip
            $trip->update(['status' => Trip::STATUS_CANCELLED, 'cancel_reason' => $cancel_reason]);

            // Cancel reservations
            if ($reservations) {
                foreach ($reservations as $reservation) {
                    if ($reservation->status == Reservation::STATUS_ACCEPTED) {
                        $reservation->status = Reservation::STATUS_REJECTED_AFTER_ACCEPTED;
                        $type = ReservationService::ACTION_TYPE_CANCEL_BOOKING;
                    } else {
                        $reservation->status = Reservation::STATUS_CANCELLED;
                        $type = ReservationService::ACTION_TYPE_REJECT_AFTER_ACCEPTED_BOOKING;
                    }
                    $reservation->save();
                    $sendMessageNotification = new SendFirebaseNotification(
                        $reservation->user_id,
                        $type,
                        [
                            'title' => 'Chuyến của bạn đã bị hủy bởi admin',
                            'body' => 'Chuyến đi từ ' . $trip->departure_address . ' đến ' . $trip->destination_address . ' đã bị hủy do lý do: ' . $trip->cancel_reason,
                            'type' => $type,
                            'trip_id' => $reservation->trip_id,
                            'reservation_id' => $reservation->id,
                            'sound' => config('firebase.sound'),
                        ],
                    );
                    dispatch($sendMessageNotification)->onConnection('sync');

                    dispatch(new SendBookingCancelEmail($reservation));
                }
            }
            DB::commit();

            return $this->response()->success('Success cancelled trip.')->refresh();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->success('Error cancelled trip.')->refresh();
        }
    }

    public function dialog()
    {
        $this->confirm('Are you sure to cancel this trip?');
    }
}
