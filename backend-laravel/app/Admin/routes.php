<?php

use App\Admin\Controllers\ReservationAdminController;
use Illuminate\Routing\Router;

Admin::routes();

Route::group([
    'prefix' => config('admin.route.prefix'),
    'namespace' => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
    'as' => config('admin.route.prefix').'.',
], function (Router $router) {

    $router->get('/', 'HomeController@index')->name('home');
    $router->resource('posts', PostAdminController::class);
    $router->resource('users', UserAdminController::class);
    $router->resource('trips', TripAdminController::class);
    $router->resource('reservations', ReservationAdminController::class);
    $router->resource('payments', PaymentAdminController::class);
});
