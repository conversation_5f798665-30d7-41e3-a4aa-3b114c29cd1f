<?php

namespace App\Helpers;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Common
{
    /**
     * Upload vehicle images.
     */
    public static function uploadImages($request, $userId, $key)
    {
        $imageUrl = [];
        if (! $request->hasFile($key)) {
            return $imageUrl;
        }

        $images = Arr::wrap($request->file($key));
        $storagePath = 'images/user'.$userId;
        Storage::makeDirectory($storagePath, 0755, true);

        foreach ($images as $index => $image) {
            $index += 1;
            $extension = $image->extension();
            $imageFilename = self::generateImageFilename($key, $index, $extension);

            $image->storeAs($storagePath, $imageFilename, 'public');
            $imageUrl[] = Storage::url("$storagePath/$imageFilename");
        }

        return $imageUrl;
    }

    /**
     * Generate a image filename.
     */
    private static function generateImageFilename($key, $index, $extension)
    {
        return Str::uuid()->toString().'-'.$key.'-'.$index.'.'.$extension;
    }
}
