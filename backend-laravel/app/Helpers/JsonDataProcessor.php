<?php

namespace App\Helpers;

use Illuminate\Http\Response;

class JsonDataProcessor
{
    /**
     * Get the master data from the file path.
     *
     * @param  string  $filePath  The file path
     * @return object The master data
     */
    public static function getFileContent($filePath)
    {
        $filePath = base_path($filePath);
        if (file_exists($filePath)) {
            $data = file_get_contents($filePath);

            return json_decode($data, false);
        } else {
            throw new \Exception(trans('messages.error.file_not_found'), Response::HTTP_BAD_REQUEST);
        }
    }
}
