<?php

namespace App\Helpers;

use Illuminate\Http\Response;
use Illuminate\Support\Str;

class Format
{
    /**
     * Format the phone number from +84 to 0
     *
     * @param  string  $phone  The phone number
     * @return string The formatted phone number
     */
    public static function formatPhone($phone)
    {
        if (! $phone) {
            throw new \Exception(trans('messages.error.undefined'), Response::HTTP_BAD_REQUEST);
        }

        if (Str::startsWith($phone, '+84')) {
            $phone = '0'.substr($phone, 3);
        }

        return $phone;
    }

    /**
     * Format the price to VND
     *
     * @param  int  $price  The price
     * @return int The formatted price
     */
    public static function formatVnd($price)
    {
        if (! $price) {
            throw new \Exception(trans('messages.error.undefined'), Response::HTTP_BAD_REQUEST);
        }

        return intval($price / 1000) * 1000;
    }

    /**
     * Format the text to VN
     *
     * @param  string  $text  The text
     * @return string The formatted text
     */
    public static function formatTextVN($text)
    {
        $unicode = [
            'a' => 'á|à|ả|ã|ạ|ă|ắ|ặ|ằ|ẳ|ẵ|â|ấ|ầ|ẩ|ẫ|ậ',
            'd' => 'đ',
            'e' => 'é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ',
            'i' => 'í|ì|ỉ|ĩ|ị',
            'o' => 'ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ',
            'u' => 'ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự',
            'y' => 'ý|ỳ|ỷ|ỹ|ỵ',
            'A' => 'Á|À|Ả|Ã|Ạ|Ă|Ắ|Ặ|Ằ|Ẳ|Ẵ|Â|Ấ|Ầ|Ẩ|Ẫ|Ậ',
            'D' => 'Đ',
            'E' => 'É|È|Ẻ|Ẽ|Ẹ|Ê|Ế|Ề|Ể|Ễ|Ệ',
            'I' => 'Í|Ì|Ỉ|Ĩ|Ị',
            'O' => 'Ó|Ò|Ỏ|Õ|Ọ|Ô|Ố|Ồ|Ổ|Ỗ|Ộ|Ơ|Ớ|Ờ|Ở|Ỡ|Ợ',
            'U' => 'Ú|Ù|Ủ|Ũ|Ụ|Ư|Ứ|Ừ|Ử|Ữ|Ự',
            'Y' => 'Ý|Ỳ|Ỷ|Ỹ|Ỵ',

        ];

        foreach ($unicode as $nonUnicode => $uni) {
            $text = preg_replace("/($uni)/i", $nonUnicode, $text);
        }

        return $text;
    }
}
