<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class EmailNotification extends Notification
{
    use Queueable;

    protected $message;

    protected $subject;

    protected $markdownTemplate;

    public function __construct($subject, $message, $markdownTemplate = null)
    {
        $this->subject = $subject;
        $this->message = $message;
        $this->markdownTemplate = $markdownTemplate;
    }

    /**
     * Get the queue connection for the notification.
     *
     * @param  mixed  $notifiable
     * @return string|null
     */
    // public function queue($notifiable)
    // {
    //     return 'redis'; // Use Redis as a queue driver
    // }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject($this->subject)
            ->line($this->message)
            ->markdown($this->markdownTemplate);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    /**
     * @return Log
     */
    public function failed($notifiable, $e)
    {
        Log::channel('api')->error(trans('messages.error.send_mail').$e->getMessage());
    }
}
