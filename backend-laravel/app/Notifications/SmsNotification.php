<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Twilio\Rest\Client;

class SmsNotification extends Notification
{
    use Queueable;

    protected $message;

    /**
     * Create a new notification instance.
     */
    public function __construct($message)
    {
        $this->message = $message;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the Twilio / SMS representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return TwilioMessage
     */
    public function toMail($notifiable)
    {
        $phoneTo = $notifiable->routeNotificationFor('twilio');
        if (substr($phoneTo, 0, 1) == '0') {
            Log::channel('api', ['debug' => [substr($phoneTo, 0, 1), $phoneTo]]);
            $phoneTo = '+84'.substr($phoneTo, 1); // Remove the leading '0' and add '+84'
        }

        $twilioSid = config('twilio.sid');
        $twilioAuthToken = config('twilio.auth_token');
        $twilioFromPhoneNumber = config('twilio.phone_number');

        $client = new Client($twilioSid, $twilioAuthToken);
        $client->messages->create(
            $phoneTo,
            [
                'from' => $twilioFromPhoneNumber,
                'body' => $this->message,
            ]
        );
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
