<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\CalculateDistanceRequest;
use App\Http\Requests\DirectionsRequest;
use App\Http\Requests\DistanceMatrixRequest;
use App\Http\Requests\GeoCodeRequest;
use App\Http\Requests\PlaceAutoCompleteRequest;
use App\Http\Requests\PlaceDetailsRequest;
use App\Services\GoogleMapApiService;
use Exception;

class GoogleMapApiController extends BaseController
{
    protected $googleMapApiService;

    public function __construct(GoogleMapApiService $googleMapApiService)
    {
        $this->googleMapApiService = $googleMapApiService;
    }

    /**
     * Display a listing of the resource.
     */
    public function calculateDistance(CalculateDistanceRequest $request)
    {
        try {
            $data = $this->googleMapApiService->getPriceDistance($request->all());

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get directions for trip.
     */
    public function directions(DirectionsRequest $request)
    {
        try {
            $data = $this->googleMapApiService->directions($request->all());

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get the distance between locations.
     */
    public function distanceMatrix(DistanceMatrixRequest $request)
    {
        try {
            $data = $this->googleMapApiService->distanceMatrix($request->all());

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Convert lat, lng to address.
     */
    public function geoCode(GeoCodeRequest $request)
    {
        try {
            $data = $this->googleMapApiService->geoCode($request->all());

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Display a listing of the resource.
     */
    public function placeAutoComplete(PlaceAutoCompleteRequest $request)
    {
        try {
            $data = $this->googleMapApiService->placeAutoComplete($request->all());

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Convert lat, lng to address.
     */
    public function placeDetails(PlaceDetailsRequest $request)
    {
        try {
            $data = $this->googleMapApiService->placeDetails($request->all());

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
