<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\DeleteFCMRequest;
use App\Http\Requests\FCMRequest;
use App\Services\FCMService;
use App\Services\NotificationService;
use Exception;

class NotificationController extends BaseController
{
    protected $fcmService;

    protected $notificationService;

    public function __construct(FCMService $fcmService, NotificationService $notificationService)
    {
        $this->fcmService = $fcmService;
        $this->notificationService = $notificationService;
    }

    public function deleteFcm(DeleteFCMRequest $request)
    {
        try {
            return $this->sendSuccessData(['data' => $this->fcmService->deleteFcm($request->all())]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    public function fcm(FCMRequest $request)
    {
        try {
            return $this->sendSuccessData(['data' => $this->fcmService->storeFcm(
                $request->device_token,
                $request->device_type,
                $request->device_id,
            )]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    public function index()
    {
        try {
            return $this->sendSuccessData(['data' => $this->notificationService->list()]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);
        }
    }

    public function test()
    {
        $deviceTokens = auth()->user()->tokens;
        $totalUnread = 1;
        foreach ($deviceTokens as $deviceToken) {
            $data = [
                'func_name' => config('firebase.notification.func'),
                'screen' => config('firebase.notification.screen'),
                'total_unread' => (string) $totalUnread ?? '',
                'total_count' => '1',
                'device_type' => $deviceToken->device_type,
            ];
            $content = [
                'title' => 'xechiase.vn',
                'body' => 'Xin Chào',
            ];
            // Push notification
            $this->fcmService->send($deviceToken->device_token, $content, $data);
        }

        return $this->sendSuccessData(['data' => 'success']);
    }

    public function update($id)
    {
        try {
            return $this->sendSuccessData(['data' => $this->notificationService->markAsRead($id)]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);
        }
    }
}
