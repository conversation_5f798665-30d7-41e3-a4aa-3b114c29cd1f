<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\CreateUserRequest;
use App\Http\Requests\LoginUserRequest;
use App\Http\Requests\SendOtpRequest;
use App\Http\Requests\VerifyOtpRequest;
use App\Models\User;
use App\Services\AuthService;
use App\Services\Helpers\OtpService;
use App\Services\OtpCodeService;
use Exception;

class AuthController extends BaseController
{
    protected $authService;

    protected $otpService;

    protected $otpCodeService;

    public function __construct(authService $authService, OtpService $otpService, OtpCodeService $otpCodeService)
    {
        $this->authService = $authService;
        $this->otpService = $otpService;
        $this->otpCodeService = $otpCodeService;
    }

    /**
     * Get the authenticated user's information.
     */
    public function getAuthUserInfo()
    {
        try {
            $data = $this->authService->getAuthUserInfo();

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Log in a user by validating their credentials and generating an access token.
     *
     * @param  \Illuminate\Http\Request  $request  The HTTP request object.
     * @return array
     */
    public function login(LoginUserRequest $request)
    {
        try {
            $data = $this->authService->login($request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Log out the authenticated user.
     */
    public function logout()
    {
        try {
            return $this->sendSuccessData($this->authService->logout());
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Refresh the access token for the authenticated user.
     */
    public function refreshToken()
    {
        try {
            $data = $this->responseSuccessData($this->authService->refreshToken());

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Register a new account in the User table.
     */
    public function register(CreateUserRequest $request)
    {
        try {
            $data = $this->authService->register($request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Send OTP to user.
     */
    public function sendOtp(SendOtpRequest $request)
    {
        try {
            $actionType = $request->action_type;
            switch ($actionType) {
                case User::ACTION_VERIFY_REGISTER:
                    $data = $this->otpService->sendOtp($request->all());
                    break;
                case User::ACTION_FORGET_PASSWORD:
                    $data = $this->otpCodeService->sendOtp($request->all());
                    break;

                default:
                    $data = $this->responseErrorData();
                    break;
            }

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Check if the OTP is valid, valid: allow register, invalid: not allow register.
     */
    public function verifyOtp(VerifyOtpRequest $request)
    {
        try {
            $actionType = $request->action_type;
            switch ($actionType) {
                case User::ACTION_VERIFY_REGISTER:
                    $data = $this->otpService->verifyOtp($request->all());
                    break;
                case User::ACTION_FORGET_PASSWORD:
                    $data = $this->otpCodeService->verifyOtp($request->all());
                    break;

                default:
                    $data = $this->responseErrorData();
                    break;
            }

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
