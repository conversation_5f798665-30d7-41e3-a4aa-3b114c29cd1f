<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\AcceptReservationRequest;
use App\Http\Requests\CancelReservationRequest;
use App\Http\Requests\GetBookedTripRequest;
use App\Http\Requests\MakeReservationRequest;
use App\Services\ReservationService;
use Exception;

class ReservationController extends BaseController
{
    protected $reservationService;

    public function __construct(ReservationService $reservationService)
    {
        $this->reservationService = $reservationService;
    }

    /**
     * Accept reservation of passenger.
     */
    public function acceptReservation(AcceptReservationRequest $request, $id)
    {
        try {
            $this->getLogger()->info('Accept reservation: '.$id);

            $data = $this->reservationService->acceptReservation($id);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Cancel reservation.
     */
    public function cancelReservation(CancelReservationRequest $request, $id)
    {
        try {
            $this->getLogger()->info('Cancel reservation: '.$id);

            $data = $this->reservationService->cancelReservation($id, $request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get the booked trip list.
     */
    public function getReservations(GetBookedTripRequest $request)
    {
        try {
            $this->getLogger()->info('Get the reservations list');

            $data = $this->reservationService->getReservations($request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Make a reservation.
     */
    public function makeReservation(MakeReservationRequest $request, $id)
    {
        try {
            $this->getLogger()->info('Book a trip: '.$id);

            $data = $this->reservationService->makeReservation($id, $request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Booking Rejection - Driver Action.
     */
    public function rejectReservation($id)
    {
        try {
            $this->getLogger()->info('Booking Rejection: '.$id);

            $data = $this->reservationService->rejectReservation($id);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
    /**
     * Booking Rejection After Accept - Driver Action.
     */
    public function rejectAfterAcceptReservation(CancelReservationRequest $request, $id)
    {
        try {
            $this->getLogger()->info('Booking Rejection After Accept: '.$id);

            $data = $this->reservationService->rejectAfterAcceptReservation($id, $request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
