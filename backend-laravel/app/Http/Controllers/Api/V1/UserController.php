<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\ChangePasswordRequest;
use App\Http\Requests\CloseAccountRequest;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\GetUserReviewRequest;
use App\Http\Requests\SendOtpUserRequest;
use App\Http\Requests\UpdateProfileRequest;
use App\Http\Requests\VerifyOtpUserRequest;
use App\Models\Reservation;
use App\Services\ChatService;
use App\Services\OtpCodeService;
use App\Services\UserService;
use Exception;
use Illuminate\Support\Facades\Auth;

class UserController extends BaseController
{
    protected $userService;

    protected $otpCodeService;

    public function __construct(UserService $userService, OtpCodeService $otpCodeService)
    {
        $this->userService = $userService;
        $this->otpCodeService = $otpCodeService;
    }

    /**
     * Change password.
     */
    public function changePassword(ChangePasswordRequest $request)
    {
        try {
            $user = $this->getAuthUser();
            $this->getLogger()->info('Change password: '.$user->id);

            $data = $this->userService->updatePassword($request, $user);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Close account.
     */
    public function closeAccount(CloseAccountRequest $request)
    {
        try {
            return $this->sendSuccessData($this->userService->closeAccount($request));
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Update password for user when forgotten.
     */
    public function forgotPassword(ForgotPasswordRequest $request)
    {
        try {
            $data = $this->userService->forgotPassword($request->all());

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get user information.
     */
    public function getAuthUserInfo()
    {
        try {
            $data = $this->userService->getUserInfo(Auth::id());

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get Total Message And Reservation Pending.
     */
    public function getTotalMessageAndReservationPending()
    {
        try {
            $data = [];
            $chatService = new ChatService();
            $data['total_message_unread'] = $chatService->getCountMessageUnread();
            $data['total_reservation_pending_confirm'] = Reservation::whereHas('trip', function ($q) {
                $q->where('trips.status', Trip::STATUS_OPEN);
                $q->where('trips.user_id', Auth::id());
            })->wherePending()->count();

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get list of reviews for user.
     */
    public function getReviews(GetUserReviewRequest $request, $userId)
    {
        try {
            $data = $this->userService->getReviews($request, $userId);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get user information - co-passenger | driver.
     */
    public function getUserInfo($userId)
    {
        try {
            $this->getLogger()->info('Get other user information: '.$userId);

            $data = $this->userService->getUserInfo($userId);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Send OTP to user.
     */
    public function sendOtp(SendOtpUserRequest $request)
    {
        try {
            $user = $this->getAuthUser();
            $this->getLogger()->info('Send OTP to user: '.$user->id, $request->all());

            $data = $this->otpCodeService->sendOtp($request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Update user profile.
     */
    public function updateProfile(UpdateProfileRequest $request)
    {
        try {
            $data = $this->userService->updateProfile($request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Check if the OTP is valid.
     */
    public function verifyOtp(VerifyOtpUserRequest $request)
    {
        try {
            $user = $this->getAuthUser();
            $this->getLogger()->info('Verify OTP for user: '.$user->id, $request->all());

            $data = $this->otpCodeService->verifyOtp($request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
