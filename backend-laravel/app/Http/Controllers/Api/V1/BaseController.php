<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\User;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BaseController extends Controller
{
    const RESPONSE_STATUS_SUCCESS = true;

    const RESPONSE_STATUS_FAILED = false;

    private $_status;

    private $_code;

    private $_message;

    private $_data;

    private $_errors = null;

    /**
     * Get authenticated user information.
     */
    public function getAuthUser()
    {
        $user = Auth::user();
        if (! $user) {
            throw new \Exception(trans('messages.user.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        return $user;
    }

    /**
     * @return Log
     */
    public function getLogger()
    {
        return Log::channel('api');
    }

    /**
     * Returns a JSON response object containing the response data.
     *
     * @return Illuminate\Http\JsonResponse The JSON response object
     */
    public function response()
    {
        $response = [
            'success' => $this->_status,
            'code' => $this->_code,
            'message' => $this->_message,
            'data' => $this->_data,
            'errors' => $this->_errors,
        ];

        return response()->json($response, $this->_code);
    }

    /**
     * Generate the response error data.
     *
     * @return array The response data
     */
    public function responseErrorData()
    {
        return [
            'data' => null,
            'message' => trans('messages.error.undefined'),
        ];
    }

    /**
     * Generate the response success data.
     *
     * @return array The response data
     */
    public function responseSuccessData($data, $message = null)
    {
        return [
            'data' => $data,
            'message' => $message,
        ];
    }

    /**
     * Returns an error response object
     *
     * @param  int|null  $code  HTTP status code
     * @param  string|null  $message  Error message
     * @return Illuminate\Http\JsonResponse A JSON response object
     */
    public function sendErrorData($code = null, $message = null)
    {
        return $this->setResponseStatus(self::RESPONSE_STATUS_FAILED)
            ->setMessage($message)
            ->setStatusCode($code ?: Response::HTTP_INTERNAL_SERVER_ERROR)
            ->response();
    }

    /**
     * Returns a successful response object
     *
     * @param  object  $data  Object include the message and data response
     * @return Illuminate\Http\JsonResponse A JSON response object
     */
    public function sendSuccessData($data)
    {
        return $this->setResponseStatus(self::RESPONSE_STATUS_SUCCESS)
            ->setMessage($data['message'] ?? 'Success')
            ->setStatusCode(Response::HTTP_OK)
            ->setData($data['data'])
            ->response();
    }

    /**
     * Sets the data for the response object
     *
     * @param  object  $data  The data response
     * @return $this The instance of the object with the new data payload set
     */
    protected function setData($data)
    {
        $this->_data = $data;

        return $this;
    }

    /**
     * Sets the error for the response object
     *
     * @param  object  $errors  The \Exception
     * @return $this The instance of the object with the new data payload set
     */
    protected function setError($errors)
    {
        $this->_errors = $errors;

        return $this;
    }

    /**
     * Sets the message for the response object
     *
     * @param  string  $message  The message
     * @return $this The instance of the object with the new message set
     */
    protected function setMessage($message)
    {
        $this->_message = $message;

        return $this;
    }

    /**
     * Sets the response status for the response object
     *
     * @param  int  $status  The response status: true | false
     * @return $this The instance of the object with the new status code set
     */
    protected function setResponseStatus($status)
    {
        $this->_status = $status;

        return $this;
    }

    /**
     * Sets the HTTP status code for the response object
     *
     * @param  int  $code  The HTTP status code
     * @return $this The instance of the object with the new HTTP status code set
     */
    protected function setStatusCode($code)
    {
        if (! empty($code) && $code > 599) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
        }
        $this->_code = $code;

        return $this;
    }
}
