<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\JsonDataProcessor;
use App\Models\User;
use App\Models\Vehicle;
use Exception;

class DataController extends BaseController
{
    /**
     * Get the preference setting.
     */
    public function getPreferenceSetting()
    {
        try {
            $data = JsonDataProcessor::getFileContent(User::PREFERENCE_DATA_PATH);

            return $this->sendSuccessData($this->responseSuccessData($data));
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get the reason for closing account.
     */
    public function getReasonsCloseAccount()
    {
        try {
            $data = JsonDataProcessor::getFileContent(User::REASONS_DATA_PATH);

            return $this->sendSuccessData($this->responseSuccessData($data));
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get vehicle master data.
     */
    public function getVehicles()
    {
        try {
            $data = JsonDataProcessor::getFileContent(Vehicle::VEHICLE_DATA_PATH);

            return $this->sendSuccessData($this->responseSuccessData($data));
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
