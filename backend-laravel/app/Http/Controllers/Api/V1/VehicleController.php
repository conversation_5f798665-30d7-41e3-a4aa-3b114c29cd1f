<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\CreateVehicleRequest;
use App\Http\Requests\DeleteVehicleRequest;
use App\Models\Vehicle;
use App\Services\VehicleService;
use Exception;

class VehicleController extends BaseController
{
    protected $vehicleService;

    public function __construct(VehicleService $vehicleService)
    {
        $this->vehicleService = $vehicleService;
    }

    /**
     * Create a vehicle (motor | car) for user.
     */
    public function createVehicle(CreateVehicleRequest $request)
    {
        try {
            $user = $this->getAuthUser();
            $this->getLogger()->info('Create vehicle for user: '.$user->id, $request->all());

            $data = $this->vehicleService->createVehicle($request, $user);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Delete a vehicle.
     */
    public function deleteVehicle(DeleteVehicleRequest $request, $id)
    {
        try {
            $user = $this->getAuthUser();
            $this->getLogger()->info('Delete vehicle for user: '.$user->id, $request->all());

            $data = $this->vehicleService->deleteVehicle($user, $id);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get vehicles list.
     *
     * @param  string  $userId  The user id.
     * @return array The response data
     */
    public function getVehicles()
    {
        try {
            $user = $this->getAuthUser();
            $this->getLogger()->info('Get vehicles list for user: '.$user->id);

            $data = $this->vehicleService->getVehicles($user);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
