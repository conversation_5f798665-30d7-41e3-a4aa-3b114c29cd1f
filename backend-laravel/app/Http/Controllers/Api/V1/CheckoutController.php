<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\CheckoutRequest;
use App\Http\Requests\CheckoutReturnRequest;
use App\Services\CheckoutService;
use Exception;
use Illuminate\Support\Facades\DB;

class CheckoutController extends BaseController
{
    protected $checkoutService;

    public function __construct(CheckoutService $checkoutService)
    {
        $this->checkoutService = $checkoutService;
    }

    /**
     * Get payment
     */
    public function index()
    {
        try {
            $data = $this->checkoutService->list();

            return $data;
        } catch (Exception $exception) {
            $this->getLogger()->error('ERROR_CHECKOUT', [$exception->getMessage()]);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Store checkout
     */
    public function checkout(CheckoutRequest $request)
    {
        try {
            $input = $request->all();
            $data = $this->checkoutService->checkout($input);

            return $data;
        } catch (Exception $exception) {
            $this->getLogger()->error('ERROR_CHECKOUT', [$exception->getMessage()]);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get return
     */
    public function return(CheckoutReturnRequest $request)
    {
        try {
            DB::beginTransaction();
            $input = $request->all();
            $this->checkoutService->checkoutReturn($input);
            DB::commit();

            return redirect()->to(config('alepay.frontend_url').'/user/travels');
        } catch (Exception $exception) {
            DB::rollback();
            $this->getLogger()->error('ERROR_CHECKOUT', [$exception->getMessage()]);

            return redirect()->to(config('alepay.frontend_url'));
        }
    }

    /**
     * Get cancel
     */
    public function cancel()
    {
        return redirect()->to(config('alepay.frontend_url'));
    }

    /**
     * Get return
     */
    public function getBanks()
    {
        try {
            return $this->checkoutService->getBanks();
        } catch (Exception $exception) {
            $this->getLogger()->error('ERROR_CHECKOUT', [$exception->getMessage()]);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
