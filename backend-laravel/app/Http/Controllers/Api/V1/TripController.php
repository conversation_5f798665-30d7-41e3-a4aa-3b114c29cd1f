<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\CancelTripRequest;
use App\Http\Requests\CreateTripRequest;
use App\Http\Requests\GetOwnTripRequest;
use App\Http\Requests\GetSimilarTripRequest;
use App\Http\Requests\GetTripDetailRequest;
use App\Http\Requests\MarkTripCompletedRequest;
use App\Http\Requests\SearchTripRequest;
use App\Http\Requests\UpdateTripRequest;
use App\Services\TripService;
use Exception;

class TripController extends BaseController
{
    protected $tripService;

    public function __construct(TripService $tripService)
    {
        $this->tripService = $tripService;
    }

    /**
     * Cancel trip (close trip)
     */
    public function cancelTrip(CancelTripRequest $request, $id)
    {
        try {
            $this->getLogger()->info('Cancel trip: '.$id);
            $data = $this->tripService->cancelTrip($id, $request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Create a trip.
     */
    public function create(CreateTripRequest $request)
    {
        try {
            $this->getLogger()->info('Create a trip');

            $data = $this->tripService->create($request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get the created trip list of the user.
     */
    public function getOwnTrips(GetOwnTripRequest $request)
    {
        try {
            $this->getLogger()->info('Get the created trip list of the user');

            $data = $this->tripService->getOwnTrips();

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get the similar trips.
     */
    public function getSimilarTrips(GetSimilarTripRequest $request)
    {
        try {
            $this->getLogger()->info('Get similar trips: ', $request->all());

            $data = $this->tripService->getSimilarTrips($request->all());

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get trip detail.
     */
    public function getTripDetail(GetTripDetailRequest $request, $id)
    {
        $userId = auth('api')->id();

        try {
            $this->getLogger()->info('Get trip: '.$id);

            $data = $this->tripService->getTripDetail($id, $request->all(), $userId);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Mark trip completed.
     */
    public function markTripCompleted(MarkTripCompletedRequest $request, $id)
    {
        try {
            $this->getLogger()->info('Mark trip completed: '.$id, $request->all());

            $data = $this->tripService->markTripCompleted($id);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get list of trips.
     *
     * @param  \Illuminate\Http\Request  $request  The HTTP request object.
     * @return array The response data
     */
    public function searchTrips(SearchTripRequest $request)
    {
        try {
            $this->getLogger()->info('Get list of trips: ', $request->all());

            $data = $this->tripService->searchTrips($request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Update a trip.
     */
    public function updateTrip($tripId, UpdateTripRequest $request)
    {
        try {
            $this->getLogger()->info('Update trip: '.$tripId);

            $data = $this->tripService->updateTrip($tripId, $request);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
