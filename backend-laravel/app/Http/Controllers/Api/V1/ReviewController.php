<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\CreateReviewRequest;
use App\Services\ReviewService;
use Exception;

class ReviewController extends BaseController
{
    protected $reviewService;

    public function __construct(ReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    /**
     * Make a rating for the user in the trip.
     */
    public function review(CreateReviewRequest $request, $tripId)
    {
        try {
            $user = $this->getAuthUser();
            $this->getLogger()->info('Make rating for user in the trip: '.$tripId.' by: '.$user->id, $request->all());

            $data = $this->reviewService->review($request, $tripId, $user);

            return $this->sendSuccessData($data);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
