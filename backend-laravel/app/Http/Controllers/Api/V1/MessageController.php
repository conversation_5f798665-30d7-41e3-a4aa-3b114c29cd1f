<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\MessageRequest;
use App\Services\ChatService;
use Exception;

class MessageController extends BaseController
{
    protected $chatService;

    public function __construct(ChatService $chatService)
    {
        $this->chatService = $chatService;
    }

    /**
     * Store message
     */
    public function store(MessageRequest $request)
    {
        try {
            $input = $request->all();
            $data = $this->chatService->storeMessage($input);
            $data['message_identify'] = $input['message_identify'] ?? '';

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Update message
     */
    public function update($id)
    {
        try {
            $data = $this->chatService->updateMessage($id);
            if ($data) {
                return $this->sendSuccessData(['data' => $data]);
            }

            return $this->sendErrorData(409, trans('messages.error.validate'));
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
