<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\GetChatRequest;
use App\Http\Requests\GetMessageRequest;
use App\Http\Requests\RoomChatRequest;
use App\Services\ChatService;
use Exception;

class ChatController extends BaseController
{
    protected $chatService;

    public function __construct(ChatService $chatService)
    {
        $this->chatService = $chatService;
    }

    /**
     * Get rooms
     *
     * @return void
     */
    public function index(GetChatRequest $request)
    {
        try {
            $data = [];
            if ($request->has('room_id')) {
                $data = $this->chatService->getRooms($request->all());
                $data['message_identify'] = $request->message_identify ?? '';

                return $this->sendSuccessData(['data' => $data]);
            }
            $data = $this->chatService->getRooms()->toArray();
            $data['message_identify'] = $request->message_identify ?? '';

            return $data;
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Get messages room chat
     *
     * @param  mixed  $id
     * @return void
     */
    public function show($id, GetMessageRequest $request)
    {
        try {
            $data = [];
            if ($request->has('message_id')) {
                $data = $this->chatService->getMessages($id, $request->all());
                $data['message_identify'] = $request->message_identify ?? '';

                return $this->sendSuccessData(['data' => $data]);
            }
            $data = $this->chatService->getMessages($id)->toArray();
            $data['message_identify'] = $request->message_identify ?? '';

            return $data;
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Store room
     */
    public function store(RoomChatRequest $request)
    {
        try {
            $data = $this->chatService->storeRoom($request->all());

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }

    /**
     * Update unread messages
     */
    public function update($id)
    {
        try {
            $data = $this->chatService->updateUnreadMessage($id);

            return $this->sendSuccessData(['data' => $data]);
        } catch (Exception $exception) {
            $this->getLogger()->error($exception);

            return $this->sendErrorData($exception->getCode(), $exception->getMessage());
        }
    }
}
