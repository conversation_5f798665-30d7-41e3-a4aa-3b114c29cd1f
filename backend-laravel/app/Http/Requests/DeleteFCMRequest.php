<?php

namespace App\Http\Requests;

class DeleteFCMRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'device_token' => 'nullable|exists:device_token,device_token',
            'device_id' => 'nullable|exists:device_token,device_id',
        ];
    }
}
