<?php

namespace App\Http\Requests;

class CreateUserRequest extends BaseFormRequest
{
    const LIMIT_AGE = 16;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $sixteenYearsAgo = now()->subYears(self::LIMIT_AGE)->format('Y-m-d');

        return [
            'first_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required_without:phone|string|email|max:255|unique:users',
            'phone' => 'required_without:email|phone_number|unique:users',
            'password' => 'required|strong_password|string|max:32|confirmed',
            'birthday' => 'required|date|date_format:Y-m-d|before:'.$sixteenYearsAgo,
            'register_from' => 'required|in:web,app',
            'gender' => 'required|in:male,female',
            'is_receive_noti' => 'required|boolean',
        ];
    }
}
