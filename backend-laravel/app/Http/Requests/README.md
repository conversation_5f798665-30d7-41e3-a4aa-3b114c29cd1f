### 📌 How to Name a Request Class

Request class names should follow this structure:

```
[Method] + [Model] + [Action] + Request
```

OR

```
[Action] + [Model] + Request
```

#### **Example:**

-   `GetUserReviewRequest` → Used for fetching user reviews.
-   `CreateOrderRequest` → Used for creating a new order.
-   `UpdateProfileRequest` → Used for updating a user profile.

💡 **Notes:**

-   `[Method]` represents the request type (e.g., `Get`, `Create`, `Update`, `Delete`).
-   `[Model]` is the entity the request is related to.
-   `[Action]` describes what the request does.
-   Always suffix with `Request` for clarity.
