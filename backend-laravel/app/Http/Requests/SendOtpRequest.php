<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;

class SendOtpRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required_without:phone|string|email|max:255',
            'phone' => 'required_without:email|phone_number',
            'action_type' => 'required|integer|in:1,2',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        parent::withValidator($validator);

        $validator
            ->sometimes(['email', 'phone'], [Rule::unique('users')], function ($input) {
                return $input->action_type == 1;
            })
            ->sometimes(['email', 'phone'], [Rule::exists('users')], function ($input) {
                return $input->action_type == 2;
            });
    }
}
