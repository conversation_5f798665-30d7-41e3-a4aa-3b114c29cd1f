<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Auth;

class UpdateProfileRequest extends BaseFormRequest
{
    const LIMIT_AGE = 16;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (Auth::check()) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $sixteenYearsAgo = now()->subYears(self::LIMIT_AGE)->format('Y-m-d');

        return [
            'phone' => 'nullable|phone_number|unique:users',
            'email' => 'nullable|string|email|max:255|unique:users',
            'last_name' => 'nullable|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'first_name' => 'nullable|string|max:255',
            'birthday' => 'nullable|date|date_format:Y-m-d|before:'.$sixteenYearsAgo,
            'bio' => 'nullable|string',
            'preference' => 'nullable|array|size:4',
            'preference.*' => 'nullable|integer',
            'avatar_url' => 'nullable|mimes:jpeg,jpg,png|max:5120',
            'identity_card' => 'nullable|array|size:2',
            'identity_card.*' => 'nullable|mimes:jpeg,jpg,png|max:5120',
            'is_noti_promo_email' => 'nullable|boolean',
            'is_noti_message_email' => 'nullable|boolean',
        ];
    }
}
