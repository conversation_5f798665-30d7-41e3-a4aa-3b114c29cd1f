<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Auth;

class UpdateTripRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (Auth::check()) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'points' => 'nullable|array|min:2',
            'points.*.address_name' => 'nullable|string',
            'points.*.city_name' => 'nullable|string',
            'points.*.latitude' => 'nullable|string',
            'points.*.longitude' => 'nullable|string',
            'date' => 'nullable|date|date_format:Y-m-d',
            'time' => 'nullable|date_format:H:i',
            'notes' => 'nullable|string',
            'prices' => 'nullable|array',
            'prices.*' => 'nullable|integer',
            'distances' => 'nullable|array',
            'distances.*' => 'nullable|numeric',
            'completion_times' => 'nullable|array',
            'completion_times.*' => 'nullable|integer',
            'overview_polyline' => 'nullable|array',
            'overview_polyline.*' => 'nullable|string',
            'max_seat' => 'nullable|integer|between:1,4',
            'vehicle_id' => 'nullable|integer',
            'auto_accept' => 'nullable|boolean',
            'has_helmet_available' => 'nullable|boolean',
        ];
    }
}
