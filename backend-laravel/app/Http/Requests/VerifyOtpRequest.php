<?php

namespace App\Http\Requests;

class VerifyOtpRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required_without:phone|string|email|max:255',
            'phone' => 'required_without:email|phone_number',
            'otp' => 'required|string|size:6|regex:/^\S*$/',
            'action_type' => 'required|integer|in:1,2',
        ];
    }
}
