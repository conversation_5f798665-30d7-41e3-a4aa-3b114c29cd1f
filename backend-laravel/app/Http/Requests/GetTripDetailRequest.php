<?php

namespace App\Http\Requests;

class GetTripDetailRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // 'departure_latitude' => 'required|string',
            // 'departure_longitude' => 'required|string',
            // 'destination_latitude' => 'required|string',
            // 'destination_longitude' => 'required|string',
        ];
    }
}
