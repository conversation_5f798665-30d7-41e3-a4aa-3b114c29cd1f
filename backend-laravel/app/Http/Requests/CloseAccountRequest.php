<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Auth;

class CloseAccountRequest extends BaseFormRequest
{
    const LIMIT_AGE = 16;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (Auth::check()) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'reason_close_account' => 'required|string',
            'is_recommend_app' => 'required|boolean',
            'feedback' => 'required|string',
        ];
    }
}
