<?php

namespace App\Http\Requests;

class SearchTripRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => 'nullable|integer',
            'cond' => 'required|array',
            'cond.max_seat' => 'required|integer',
            'cond.departure_city' => 'required|string|max:255',
            'cond.departure_latitude' => 'required|string',
            'cond.departure_longitude' => 'required|string',
            'cond.destination_city' => 'required|string|max:255',
            'cond.destination_latitude' => 'required|string',
            'cond.destination_longitude' => 'required|string',
            'cond.auto_accept' => 'nullable|boolean',
            'cond.allow_pets' => 'nullable|boolean',
            'cond.allow_smoking' => 'nullable|boolean',
            'cond.has_helmet_available' => 'nullable|boolean',
            'cond.is_ID_card_verified' => 'nullable|boolean',
            'cond.verified_profile' => 'nullable|boolean',
            'cond.vehicle_type' => 'nullable|integer',
            'cond.sort_by' => 'required|string',
            'cond.departure_date' => 'required|date|date_format:Y-m-d|after_or_equal:today',
            'cond.departure_time' => 'nullable|array',
            'cond.departure_time.*' => 'string',
        ];
    }
}
