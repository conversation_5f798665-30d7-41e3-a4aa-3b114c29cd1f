<?php

namespace App\Http\Requests;

class FCMRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'device_token' => 'required',
            'device_type' => 'required',
            'device_id' => 'required',
        ];
    }
}
