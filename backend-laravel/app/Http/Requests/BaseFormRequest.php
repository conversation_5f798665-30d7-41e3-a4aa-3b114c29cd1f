<?php

namespace App\Http\Requests;

use App\Helpers\Format;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class BaseFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  mixed  $input  The input that failed validation
     * @param  array  $errors  An array containing the validation errors
     *
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->errors()->toArray();

        Log::channel('validation')->error('Data input: ', $this->all());
        Log::channel('validation')->error('Errors: ', $errors);

        $response = [
            'success' => false,
            'code' => Response::HTTP_UNPROCESSABLE_ENTITY,
            'message' => $validator->errors()->first(),
            'data' => null,
            'errors' => $validator->errors(),
        ];

        throw new HttpResponseException(response()->json($response, Response::HTTP_UNPROCESSABLE_ENTITY));
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected function withValidator($validator)
    {
        $phone = $this->input('phone');
        if ($phone) {
            $formattedPhone = Format::formatPhone($this->input('phone'));
            $this->merge(['phone' => $formattedPhone]);
        }
    }
}
