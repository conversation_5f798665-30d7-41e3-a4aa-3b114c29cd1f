<?php

namespace App\Http\Requests;

use App\Models\Payment;

class CheckoutReturnRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'token' => 'required',
            'transactionCode' => 'required|exists:payments,transaction_code,status,'.Payment::STATUS_OPEN,
        ];
    }
}
