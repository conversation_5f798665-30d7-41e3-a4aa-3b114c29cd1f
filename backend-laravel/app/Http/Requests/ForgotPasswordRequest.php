<?php

namespace App\Http\Requests;

class ForgotPasswordRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required_without:phone|string|email|max:255',
            'phone' => 'required_without:email|phone_number',
            'password' => 'required|strong_password|string|min:6|max:32|confirmed',
        ];
    }
}
