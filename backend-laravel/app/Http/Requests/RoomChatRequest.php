<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Auth;

class RoomChatRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (Auth::check()) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'trip_id' => 'required',
            'user_id_driver' => 'required',
            'user_id_passenger' => 'required',
        ];
    }
}
