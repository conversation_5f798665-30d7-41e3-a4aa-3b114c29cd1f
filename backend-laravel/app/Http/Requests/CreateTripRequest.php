<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Auth;

class CreateTripRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (Auth::check()) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'points' => 'required|array|min:2',
            'points.*.address_name' => 'required|string',
            'points.*.city_name' => 'required|string',
            'points.*.latitude' => 'required|string',
            'points.*.longitude' => 'required|string',
            'date' => 'required|date|date_format:Y-m-d',
            'time' => 'required|date_format:H:i',
            'notes' => 'nullable|string',
            'prices' => 'required|array',
            'prices.*' => 'required|integer',
            'distances' => 'required|array',
            'distances.*' => 'required|numeric',
            'completion_times' => 'required|array',
            'completion_times.*' => 'required|integer',
            'overview_polyline' => 'required|array',
            'overview_polyline.*' => 'nullable|string',
            'max_seat' => 'required|integer|between:1,4',
            'vehicle_id' => 'required|integer',
            'auto_accept' => 'required|boolean',
            'has_helmet_available' => 'nullable|boolean',
        ];
    }
}
