<?php

namespace App\Http\Requests;

class CalculateDistanceRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type_vehicle' => 'required|in:1,2',
            'destination' => 'required',
            'origin' => 'required',
            'stop_points' => 'array',
        ];
    }
}
