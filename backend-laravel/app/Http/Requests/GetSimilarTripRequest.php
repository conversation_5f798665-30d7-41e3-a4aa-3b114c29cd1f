<?php

namespace App\Http\Requests;

class GetSimilarTripRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'departure_city' => 'nullable|string|max:255',
            'destination_city' => 'nullable|string|max:255',
            'departure_date' => 'required|date|date_format:Y-m-d|after_or_equal:today',
            'ignore_trip_id' => 'nullable',
            'vehicle_type' => 'nullable|integer|in:1,2',
        ];
    }
}
