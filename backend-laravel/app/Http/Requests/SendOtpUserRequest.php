<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Auth;

class SendOtpUserRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (Auth::check()) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required_without:phone|string|email|max:255|unique:users',
            'phone' => 'required_without:email|phone_number|unique:users',
            'action_type' => 'required|integer|in:1,2,3,4',
        ];
    }
}
