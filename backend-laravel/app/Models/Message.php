<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Message extends Model
{
    use HasFactory, SoftDeletes;

    const MESSAGE_TYPE_CHECK_FAILSE = 0;

    const MESSAGE_TYPE_CHECK = 1;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'room_chat_id',
        'messages',
        'user_send',
        'user_receiver',
        'message_status',
        'type',
    ];

    /**
     * Return user send
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function send()
    {
        return $this->belongsTo(User::class, 'user_send');
    }

    /**
     * Return user receiver
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function receiver()
    {
        return $this->belongsTo(User::class, 'user_receiver');
    }
}
