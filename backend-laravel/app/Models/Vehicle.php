<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Vehicle extends Model
{
    use HasFactory, SoftDeletes;

    const VEHICLE_DATA_PATH = 'data/vehicle.json';

    const NUMBER_OF_SEATS_MOTOR = 1;

    const NUMBER_OF_SEATS_CAR = 4;

    const TYPE_MOTOR = 1;

    const TYPE_CAR = 2;

    protected $table = 'vehicles';

    protected $fillable = [
        'type',
        'brand',
        'model',
        'color',
        'user_id',
        'is_verified',
        'number_of_seats',
        'license_plate',
    ];

    protected $dates = ['deleted_at'];

    /**
     * Return the vehicles of user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Return the trip of vehicle.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function trips()
    {
        return $this->hasMany(Trip::class);
    }
}
