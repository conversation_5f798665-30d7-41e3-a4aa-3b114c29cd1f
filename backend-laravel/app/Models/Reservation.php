<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Reservation extends Model
{
    use HasFactory, SoftDeletes;

    const STATUS_WAITING = 0;

    const STATUS_PENDING_CONFIRM = 1;

    const STATUS_ACCEPTED = 2;

    const STATUS_PAID = 3; // TODO: remove

    const STATUS_FINISHED = 4; // TODO: remove

    const STATUS_CANCELLED = 5;

    const STATUS_REJECTED = 6;

    const STATUS_OUTDATED = 7;

    const STATUS_REJECTED_AFTER_ACCEPTED = 8;

    protected $table = 'reservations';

    protected $fillable = [
        'user_id',
        'trip_id',
        'number_of_seats',
        'payment_method',
        'is_paid',
        'actual_amount_paid',
        'total_amount',
        'status',
        'cancel_reason',
        'accept_deadline',
    ];

    /**
     * Return the trip of this reservation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function trip()
    {
        return $this->belongsTo(Trip::class, 'trip_id');
    }

    /**
     * Return the user who make this reservation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Return the payment.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function payment_paid()
    {
        return $this->hasOne(Payment::class, 'reservation_id', 'id')->where('status', Payment::STATUS_PAID);
    }

    /**
     * Scope a query to only include pending reservations.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWherePending($query)
    {
        return $query->where('status', self::STATUS_PENDING_CONFIRM);
    }
}
