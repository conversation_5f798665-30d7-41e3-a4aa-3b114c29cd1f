<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeviceToken extends Model
{
    use HasFactory;

    const ACTIVE = 1;

    const UNACTIVE = 0;

    protected $table = 'device_token';

    protected $fillable = [
        'user_id',
        'device_token',
        'device_type',
        'device_id',
        'active',
    ];

    /**
     * Return the user who make this reservation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
