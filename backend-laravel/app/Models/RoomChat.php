<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RoomChat extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'trip_id',
        'user_id_driver',
        'user_id_passenger',
    ];

    /**
     * Return the last message.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function last_message()
    {
        return $this->hasOne(Message::class, 'room_chat_id')
            ->where('created_at', '>=', Carbon::now()->subDay(1))->where('type', Message::MESSAGE_TYPE_CHECK)->orderBy('id', 'desc');
    }

    /**
     * Return the messages.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function message()
    {
        return $this->hasMany(Message::class, 'room_chat_id');
    }

    /**
     * Return the messages.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function messagesUnread()
    {
        return $this->hasMany(Message::class, 'room_chat_id')->where('message_status', 0)->where('user_receiver', auth()->user()->id);
    }

    /**
     * Return the trip.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function trip()
    {
        return $this->belongsTo(Trip::class, 'trip_id');
    }

    /**
     * Return the driver.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user_driver()
    {
        return $this->belongsTo(User::class, 'user_id_driver');
    }

    /**
     * Return the passenger.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user_passenger()
    {
        return $this->belongsTo(User::class, 'user_id_passenger');
    }
}
