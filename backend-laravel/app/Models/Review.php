<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Review extends Model
{
    use HasFactory;

    const REVIEW_GIVEN = 1;

    const REVIEW_RECEIVED = 2;

    const TIME_UP = 30;

    protected $table = 'reviews';

    protected $fillable = [
        'trip_id',
        'reviewee_id',
        'reviewer_id',
        'comment',
        'rating',
    ];

    /**
     * Return the user information of this review.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function reviewee()
    {
        return $this->belongsTo(User::class, 'reviewee_id');
    }

    /**
     * Return the user information of this review.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }
}
