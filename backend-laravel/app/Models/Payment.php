<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    const STATUS_OPEN = 0;

    const STATUS_PAID = 1;

    const STATUS_CANCELLED = 2;

    const STATUS_REFUNDED = 3;

    const GATEWAY_ALEPAY = 'alepay';

    protected $fillable = [
        'reservation_id',
        'transaction_code',
        'user_id',
        'status',
        'price',
        'token',
        'payment_gateway',
        'method',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Return the reservation.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function reservation()
    {
        return $this->belongsTo(Reservation::class, 'reservation_id');
    }
}
