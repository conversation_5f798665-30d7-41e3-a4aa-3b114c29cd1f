<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserTemp extends Model
{
    use HasFactory, SoftDeletes;

    const OTP_SENT = 1;

    const OTP_VERIFIED = 2;

    const REGISTER_SUCCESSFUL = 3;

    protected $table = 'users_temp';

    protected $fillable = [
        'email',
        'phone',
        'otp',
        'expires_at',
        'is_verified',
        'count',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
    ];

    protected $dates = ['deleted_at'];
}
