<?php

namespace App\Models;

use App\Helpers\Format;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;
use Laravel\Passport\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    const STATUS_ACTIVE = 1;

    const STATUS_FLAG_PENALTY = 2;

    const ACTION_VERIFY_REGISTER = 1;

    const ACTION_VERIFY_EMAIL_OR_PHONE = 1;

    const ACTION_FORGET_PASSWORD = 2;

    // User profile
    const ACTION_VERIFY_EMAIL = 3;

    const ACTION_VERIFY_PHONE = 4;

    const PREFERENCE_DATA_PATH = 'data/preference.json';

    const REASONS_DATA_PATH = 'data/reasons_close_account.json';

    const IS_CARD_OPEN = 0;

    const IS_CARD_VERIFIED = 1;

    const IS_CARD_REJECTED = 2;

    const RECEIVE_ALL_NOTI = 1;

    const NOT_RECEIVE_NOTI = 2;

    const POSITIVE_REVIEW = 5; // 5 star

    const PREFERENCE_DEFAULT = '2,5,8,11';

    const PREFERENCE_KEY = ['chat', 'music', 'smoking', 'pet'];

    protected $table = 'users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'full_name',
        'email',
        'phone',
        'password',
        'avatar_url',
        'address',
        'identity_card',
        'bio',
        'birthday',
        'preference',
        'register_from',
        'gender',
        'status',
        'is_noti_promo_email',
        'is_noti_message_email',
        'has_motor',
        'has_car',
        'is_ID_card_verified',
        'is_avatar_verified',
        'is_recommend_app',
        'reason_close_account',
        'feedback',
        'email_verified_at',
        'phone_verified_at',
        'cancel_count',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'birthday' => 'date',
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'password' => 'hashed',
        'identity_card' => 'json',
    ];

    /**
     * Override the default method to find the user by the given username.
     *
     * @param  string  $username  The email|phone of the user.
     * @return \App\Models\User|null
     */
    public function findForPassport($username)
    {
        return $this->where('email', $username)
            ->orWhere('phone', Format::formatPhone($username))
            ->first();
    }

    /**
     * Automatically called whenever you access the 'preference' attribute and format.
     */
    public function getPreferenceAttribute($value)
    {
        if (empty($value) || ! is_string($value)) {
            $value = self::PREFERENCE_DEFAULT;
        }

        return array_combine(self::PREFERENCE_KEY, explode(',', $value));
    }

    /**
     * Automatically called whenever you access the 'rating_avg' attribute and format.
     */
    public function getRatingAvgAttribute($ratingAvg)
    {
        $defaultRating = self::POSITIVE_REVIEW; // Make default rating explicit

        return round($ratingAvg ?? $defaultRating, 1, PHP_ROUND_HALF_UP);
    }

    /**
     * Return reservations of the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Reservations()
    {
        return $this->hasMany(Reservation::class, 'user_id');
    }

    /**
     * Return the OTP code of user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function otpCode()
    {
        return $this->hasOne(OtpCode::class, 'user_id');
    }

    /**
     * Return the list of reviews given.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function reviewGiven()
    {
        return $this->hasMany(Review::class, 'reviewer_id');
    }

    /**
     * Return the list of reviews received.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function reviewReceived()
    {
        return $this->hasMany(Review::class, 'reviewee_id');
    }

    /**
     * Route notifications for the mail channel.
     *
     * @return string
     */
    public function routeNotificationForMail(Notification $notification)
    {
        return $this->email;
    }

    /**
     * Route notifications for the Sms channel.
     *
     * @return string
     */
    public function routeNotificationForTwilio(Notification $notification)
    {
        return $this->phone;
    }

    /**
     * Return the trips of user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function trips()
    {
        return $this->hasMany(Trip::class, 'user_id');
    }

    /**
     * Return the list of tokens.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function tokens()
    {
        return $this->hasMany(DeviceToken::class, 'user_id')->where('active', DeviceToken::ACTIVE);
    }

    /**
     * Return the vehicles of user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function vehicles()
    {
        return $this->hasMany(Vehicle::class, 'user_id');
    }
}
