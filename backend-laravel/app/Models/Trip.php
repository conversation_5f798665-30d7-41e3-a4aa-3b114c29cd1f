<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Trip extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Trip is open for booking
     */
    const STATUS_OPEN = 1;

    /**
     * Trip is finished
     */
    const STATUS_FINISHED = 2;

    /**
     * Trip is cancelled by driver
     */
    const STATUS_CANCELLED = 3;

    /**
     * Trip is outdated (departure time is passed) - performed by Cronjob
     */
    const STATUS_OUTDATED = 4;

    /**
     * Penalty for driver by admin
     */
    const STATUS_PENALTY = 5;

    /**
     * Maximum number of seats for a trip
     */
    const MIN_SEAT = 1;

    /**
     * Booking deadline in minutes before departure time
     */
    const BOOKING_DEADLINE_MINUTES = 15;

    /**
     * Undefined update type
     * - no update allowed
     */
    const UPDATE_TYPE_UNDEFINED = 0;

    /**
     * Allow update: points, price, auto_accept, max_seat, remaining_seats, notes, overview_polyline
     */
    const UPDATE_TYPE_ALL = 1;

    /**
     * Allow update: price, auto_accept, max_seat, remaining_seats, notes
     */
    const UPDATE_TYPE_ACCEPTED = 2;

    /**
     * Allow update: price
     */
    const UPDATE_TYPE_PENDING = 3;

    protected $table = 'trips';

    protected $primaryKey = 'id';

    protected $fillable = [
        'user_id',
        'vehicle_id',
        'departure_address',
        'departure_city',
        'departure_city_normalized',
        'departure_latitude',
        'departure_longitude',
        'departure_date',
        'departure_time',
        'destination_address',
        'destination_city',
        'destination_city_normalized',
        'destination_latitude',
        'destination_longitude',
        'route_id',
        'overview_polyline',
        'is_parent_route',
        'max_seat',
        'remaining_seats',
        'price',
        'distance',
        'completion_time',
        'auto_accept',
        'has_helmet_available',
        'notes',
        'status',
        'cancel_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // 'departure_time' => 'datetime:H:i',
    ];

    /**
     * Return the vehicles of user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function driver()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the computed departure_datetime attribute.
     */
    public function getDepartureDatetimeAttribute(): ?Carbon
    {
        return Carbon::parse("$this->departure_date $this->departure_time");
    }

    /**
     * Return the vehicles of user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class, 'vehicle_id');
    }

    /**
     * Return reservations of the trip.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class, 'trip_id');
    }

    /**
     * Return passengers accompanying on this trip.
     *
     * @return \Illuminate\Database\Eloquent\Relations\hasManyThrough
     */
    public function co_passenger()
    {
        return $this->hasManyThrough(User::class, Reservation::class, 'trip_id', 'id', 'id', 'user_id');
    }

    /**
     * Return the route of this trip.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function scopeWhereUpcoming($query)
    {
        return $query->where(function ($q) {
            $q->where('departure_date', '>', now()->toDateString())
                ->orWhere(function ($q) {
                    $q->where('departure_date', now()->toDateString())
                        ->where('departure_time', '>', now()->toTimeString());
                });
        });
    }
}
