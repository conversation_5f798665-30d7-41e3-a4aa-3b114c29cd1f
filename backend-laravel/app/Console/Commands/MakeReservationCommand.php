<?php

namespace App\Console\Commands;

use App\Jobs\Reservations\MakeReservationOutdated;
use Illuminate\Console\Command;

class MakeReservationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reservation:outdated';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reservation outdated';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Running reservation outdated!');
        dispatch(new MakeReservationOutdated());

    }
}
