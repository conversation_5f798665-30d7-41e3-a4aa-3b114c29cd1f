<?php

namespace App\Console\Commands;

use App\Jobs\Trips\MakeTripOutdated;
use Illuminate\Console\Command;

class MakeTripOutdatedCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trip:outdated';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Trip outdated';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Running trip outdated!');
        dispatch(new MakeTripOutdated());

    }
}
