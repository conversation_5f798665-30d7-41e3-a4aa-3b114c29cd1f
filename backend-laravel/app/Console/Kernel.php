<?php

namespace App\Console;

use App\Console\Commands\MakeReservationCommand;
use App\Console\Commands\MakeTripOutdatedCommand;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command(MakeReservationCommand::class)->everyFiveMinutes();
        $schedule->command(MakeTripOutdatedCommand::class)->everySixHours();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
