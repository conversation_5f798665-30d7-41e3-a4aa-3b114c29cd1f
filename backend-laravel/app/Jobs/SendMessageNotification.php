<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\FCMService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendMessageNotification implements ShouldQueue
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $device_tokens;

    private $content;

    private $data;

    private $user_receiver;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_send, $user_receiver, $message_id, $messages, $room_id, $message_identify)
    {
        $user = User::find($user_receiver);
        $this->device_tokens = $user->tokens;
        $this->content = [
            'title' => 'Bạn có 1 tin nhắn mới',
            'body' => $messages,

        ];
        $this->data = array_map('strval', [
            'func_name' => config('firebase.notification.func'),
            'screen' => config('firebase.notification.screen'),
            'total_unread' => '1',
            'total_count' => '1',
            'device_type' => '',
            'room_id' => $room_id,
            'message_id' => $message_id,
            'message_identify' => $message_identify,
            'type' => 'message',
            'sender_id' => $user_send,
            'receive_id' => $user_receiver,
        ]);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if ($this->device_tokens) {
                $fcmService = new FCMService;
                foreach ($this->device_tokens as $deviceToken) {
                    $this->data['device_type'] = $deviceToken->device_type;
                    $fcmService->send($deviceToken->device_token, $this->content, $this->data);
                    Log::channel('api')->info('SendMessageNotification', ['content' => $this->content, 'data' => $this->data]);
                }
            }
        } catch (\Exception $e) {
            Log::channel('api')->error('SendMessageNotification', ['Error' => $e->getMessage()]);
        }
    }
}
