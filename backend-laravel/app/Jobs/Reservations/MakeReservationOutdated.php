<?php

namespace App\Jobs\Reservations;

use App\Models\Reservation;
use App\Services\ReservationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Cancels outdated reservations by changing their status from Pending to Cancelled.
 *
 * This job updates reservations that have exceeded their acceptance deadline.
 *
 * - Status updated to: Reservations::STATUS_CANCELLED
 * - Cancel reason set to: trans('messages.reservation.jobs.default_cancelled')
 */
class MakeReservationOutdated implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();
            // Get reservations that are outdated
            $outdatedReservations = Reservation::where('status', Reservation::STATUS_PENDING_CONFIRM)
                ->whereHas('trip', function ($query) {
                    $query->where('reservations.accept_deadline', '<', now());
                })
                ->get();

            // Collect reservation IDs
            $expiredReservationIds = $outdatedReservations->pluck('id')->toArray();

            if (! empty($expiredReservationIds)) {
                $reservationService = new ReservationService(new Reservation);
                $reservationService->cancelReservationsAuto($expiredReservationIds);

                Log::channel('api')->info('Cancelled outdated reservations', ['reservation_ids' => $expiredReservationIds]);
            } else {
                Log::channel('api')->info('No outdated reservations found.');
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('api')->error('Error cancelling outdated reservations: '.$e->getMessage());
        }
    }
}
