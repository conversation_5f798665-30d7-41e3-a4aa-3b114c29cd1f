# 📌 Rules for Creating a New Cronjob

When creating a new cronjob, follow this naming convention for job class names to keep the project structured and maintainable.

## 📂 Folder Structure

```
app/
│── Jobs/
│   │── Model/       // Target models
│   │   │── Action   // Specific action to perform
```

## 📌 Naming Conventions

-   Each job should be placed inside the corresponding model folder.
-   The job name should clearly describe the action it performs.

## ✅ Examples

-   `app/Jobs/Trips/MakeTripOutdated.php` → Marks trips as outdated.
-   `app/Jobs/Trips/MakeReservationOutdated.php` → Marks reservations as outdated.

## 💡 Notes

-   Follow this structure to keep the job organization clear.
-   Ensure that job names are descriptive and easy to understand.
-   Jobs should be properly queued and scheduled in `app/Console/Kernel.php` if needed.

### 📌 Run Jobs

```
docker exec -it php_app php artisan schedule:run
```

## 💡 Available commands for the "schedule" namespace:

```
schedule:clear-cache Delete the cached mutex files created by scheduler

schedule:interrupt Interrupt the current schedule run

schedule:list List all scheduled tasks

schedule:run Run the scheduled commands

schedule:test Run a scheduled command

schedule:work Start the schedule worker
```
