<?php

namespace App\Jobs;

use App\Models\Notification;
use App\Models\User;
use App\Services\FCMService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendFirebaseNotification implements ShouldQueue
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $device_tokens;

    private $content;

    private $data;

    private $user_receiver;

    private $type;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_receiver, $type, $data = [])
    {
        $this->user_receiver = User::find($user_receiver);
        $this->device_tokens = $this->user_receiver->tokens ?? [];
        $this->content = [
            'title' => $data['title'],
            'body' => $data['body'],
        ];
        $this->data = array_map('strval', $data);
        $this->type = $type;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if ($this->device_tokens) {
                $fcmService = new FCMService;
                foreach ($this->device_tokens as $deviceToken) {
                    $this->data['device_type'] = $deviceToken->device_type;
                    Notification::create([
                        'id' => uniqid(),
                        'notifiable_id' => $this->user_receiver->id,
                        'data' => $this->data,
                        'type' => 'notification',
                        'notifiable_type' => $this->type,
                    ]);
                    $fcmService->send($deviceToken->device_token, $this->content, $this->data);
                    Log::channel('api')->info('SendFirebaseNotification', ['content' => $this->content, 'data' => $this->data]);
                }
            }
        } catch (\Exception $e) {
            Log::channel('api')->error('SendFirebaseNotification', ['Error' => $e->getMessage()]);
        }
    }
}
