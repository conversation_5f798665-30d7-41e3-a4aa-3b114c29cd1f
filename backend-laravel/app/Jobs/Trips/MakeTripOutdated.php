<?php

namespace App\Jobs\Trips;

use App\Jobs\SendFirebaseNotification;
use App\Models\Reservation;
use App\Models\Trip;
use Illuminate\Support\Facades\DB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class MakeTripOutdated implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();
            // Get trip that are outdated
            $outdatedTrips = Trip::where('status', Trip::STATUS_OPEN)
                ->where('departure_date', '<', now())
                ->whereDoesntHave('reservations', function ($query) {
                    $query->where('reservations.status', Reservation::STATUS_ACCEPTED);
                })
                ->get();
            if (!empty($outdatedTrips)) {
                $this->cancelTripOutdated($outdatedTrips);
                Log::channel('api')->info('Cancelled outdated trips', ['trip_ids' => $outdatedTrips->pluck('id')->toArray()]);
            } else {
                Log::channel('api')->info('No outdated trips found.');
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('api')->error('Error cancelling outdated trips: ' . $e->getMessage());
        }
    }
    /**
     * Cancel trip outdated.
     */
    public function cancelTripOutdated($outdatedTrips)
    {
        foreach ($outdatedTrips as $trip) {
            $trip->update(['status' => Trip::STATUS_OUTDATED, 'cancel_reason' => trans('messages.trip.notification.cancelled_outdated')]);
            $sendMessageNotification = new SendFirebaseNotification(
                $trip->user_id,
                'cancelled_trip_outdated',
                [
                    'title' => trans('messages.trip.notification.cancelled_outdated'),
                    'body' => 'Chuyến đi từ ' . $trip->departure_address . ' đến ' . $trip->destination_address . ' đã bị hủy do lý do: ' . $trip->cancel_reason,
                    'type' => 'cancelled_trip_outdated',
                    'trip_id' => $trip->id,
                    'user_id' => $trip->user_id,
                    'sound' => config('firebase.sound'),
                ],
            );
            dispatch($sendMessageNotification)->onConnection('sync');
        }
    }
}
