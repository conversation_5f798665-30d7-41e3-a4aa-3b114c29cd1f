<?php

namespace App\Services;

use App\Models\UserTemp;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;

class UserTempService extends BaseService
{
    protected $userTemp;

    public function __construct(UserTemp $userTemp)
    {
        $this->userTemp = $userTemp;
    }

    /**
     * Create a new temporary User record and store it in the database.
     *
     * @param  array  $request  The request information.
     * @return \App\Models\UserTemp
     */
    public function create($request)
    {
        return UserTemp::create([
            'email' => Arr::get($request, 'email'),
            'phone' => Arr::get($request, 'phone'),
            'otp' => Hash::make(Arr::get($request, 'otp')),
            'expires_at' => Arr::get($request, 'expires_at'),
        ]);
    }

    /**
     * Update the temporary User.
     *
     * @param  array  $request  The request information.
     * @return \App\Models\UserTemp
     */
    public function update($request)
    {
        $emailOrPhoneKey = array_key_exists('email', $request) ? 'email' : 'phone';
        $emailOrPhoneValue = Arr::get($request, $emailOrPhoneKey);
        $userTemp = UserTemp::where($emailOrPhoneKey, $emailOrPhoneValue)->first();

        return $userTemp->update([
            'otp' => Hash::make(Arr::get($request, 'otp')),
            'is_verified' => false,
            'expires_at' => Arr::get($request, 'expires_at'),
            'count' => Arr::get($request, 'count'),
        ]);
    }
}
