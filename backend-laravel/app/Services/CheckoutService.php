<?php

namespace App\Services;

use App\Helpers\Format;
use App\Jobs\SendBookingSuccessEmail;
use App\Jobs\SendFirebaseNotification;
use App\Models\Payment;
use App\Models\Reservation;
use Carbon\Carbon;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CheckoutService extends BaseService
{
    /**
     * Checkout
     *
     * @param  mixed  $request
     * @return void
     */
    public function checkout($data)
    {
        $reservation = Reservation::find($data['reservation_id']);
        if ($reservation->payment_paid) {
            return $this->responseError(trans('messages.reservation.status.3'));
        }
        if ($reservation->number_of_seats > $reservation->trip->remaining_seats) {
            return $this->responseError(trans('messages.reservation.error.full_seats'));
        }
        $checkoutType = $data['checkoutType'] ?? 0;

        return $this->checkoutAlepay($reservation, $checkoutType);
    }

    /**
     * Checkout Alepay
     *
     * @param  mixed  $request
     * @return void
     */
    public function checkoutAlepay($reservation, $checkoutType)
    {
        $user = Auth::user();
        $token = config('alepay.token_key');
        $checksumkey = config('alepay.checksum_key');
        $merchantId = config('alepay.merchant_id');
        $apiPath = config('alepay.url');

        $inputs['iat'] = Carbon::now()->timestamp;
        $inputs['exp'] = Carbon::now()->addMinutes(15)->timestamp;
        $inputs['email'] = $user->email;
        $token_auth = JWT::encode($inputs, $token, 'HS256');
        $cancelUrl = config('alepay.callback_url').'/api/v1/checkout/cancel';
        $returnUrl = config('alepay.callback_url').'/api/v1/checkout/return?token='.$token_auth;
        $buyerAddress = $reservation->trip->departure_address ?? 'Quan Hai Chau';
        $city = explode(', ', $buyerAddress);
        $buyerCity = 'Da Nang';
        $count = count($city);
        if ($city && $count > 1) {
            $buyerCity = $city[($count - 2)] ?? $buyerCity;
        } else {
            $buyerCity = $city[0] ?? $buyerCity;
        }
        $type_vehicle = $reservation->trip->vehicle->type ?? 1;
        $percent_service_and_vat = config('price')[$type_vehicle]['percent_service_and_vat'] ?? 20;
        $amount_service_and_vat = (int) $reservation->actual_amount_paid * ($percent_service_and_vat / 100);
        $reservation->total_amount = (int) $reservation->actual_amount_paid + $amount_service_and_vat;
        $checkout = [
            'amount' => $reservation->total_amount,
            'buyerAddress' => Format::formatTextVN($buyerAddress),
            'buyerCity' => Format::formatTextVN($buyerCity),
            'buyerCountry' => 'Viet Nam',
            'buyerEmail' => $user->email,
            'buyerName' => Format::formatTextVN($user->full_name),
            'buyerPhone' => $user->phone ?? '0374288399',
            'cancelUrl' => $cancelUrl,
            // "checkoutType" => (int) 4,
            // "allowDomestic" => true,
            'currency' => 'VND',
            'customMerchantId' => $merchantId,
            'orderCode' => 'ORDER-XE-CHIA-SE-'.$reservation->id,
            'orderDescription' => 'Thanh toan hoa don cho chuyen đi cho xe chia se code: #'.$reservation->id,
            'returnUrl' => $returnUrl,
            'tokenKey' => $token,
            'totalItem' => 1,
        ];
        $data = '';
        foreach ($checkout as $key => $value) {
            $data .= $key.'='.$value.'&';
        }
        $data = rtrim($data, '&');

        $signature = hash_hmac('sha256', $data, $checksumkey);
        $checkout['signature'] = $signature;
        $response = Http::post($apiPath, $checkout)->json();
        Log::info('checkoutAlepay', [$response]);
        if (! isset($response['checkoutUrl'])) {
            $trip = $reservation->trip()->lockForUpdate()->first();
            $trip->remaining_seats += $reservation->number_of_seats;
            $trip->save();
            $reservation->delete();

            return $this->responseError(trans('messages.trip.error.checkout'), 409);
        }
        $reservation->save();
        $this->createPayment($reservation, $response['transactionCode'], $token_auth);

        return $response;
    }

    /**
     * Checkout return
     *
     * @param  mixed  $data
     * @return void
     */
    public function checkoutReturn($data)
    {
        $payment = Payment::where([
            'token' => $data['token'],
            'transaction_code' => $data['transactionCode'],
        ])->first();
        if ($payment) {
            $payment->status = Payment::STATUS_PAID;
            $reservation = Reservation::where('id', $payment->reservation_id)->first();
            $trip = $reservation->trip;
            $trip->remaining_seats = $trip->remaining_seats - $reservation->number_of_seats;
            $payment->save();
            $trip->save();
            if ($trip->auto_accept) {
                $reservation->status = Reservation::STATUS_ACCEPTED;
                $title = 'Đã có hành khách đặt chuyến xe';
                $type = 'success_booking';
            } else {
                $reservation->status = Reservation::STATUS_PENDING_CONFIRM;
                $title = 'Hành khách đã thanh toán chuyến đi vui lòng xét duyệt hành khách';
                $type = 'success_paid';
               
            }
            $reservation->save();
            $sendMessageNotification = new SendFirebaseNotification(
                $trip->user_id,
                $type,
                [
                    'title' => $title,
                    'body' => 'Chuyến đi từ '.$reservation->trip->departure_address.' đến '.$reservation->trip->destination_address.' hành khách '.$reservation->user->full_name.' đã thanh toán thành công.',
                    'type' => $type,
                    'trip_id' => $reservation->trip_id,
                    'reservation_id' => $reservation->id,
                    'user_id' => $reservation->user_id,
                    'sound' => config('firebase.sound'),
                ],
            );
            dispatch($sendMessageNotification)->onConnection('sync');
            dispatch(new SendBookingSuccessEmail($reservation));
        }
    }

    /**
     * Checkout
     *
     * @param  mixed  $request
     * @return void
     */
    public function getBanks()
    {
        $token = config('alepay.token_key');
        $checksumkey = config('alepay.checksum_key');
        // $merchantId = config('alepay.merchant_id');
        $apiPath = config('alepay.url_list_banks');

        $checkout['tokenKey'] = $token;
        $data = '';
        foreach ($checkout as $key => $value) {
            $data .= $key.'='.$value.'&';
        }
        $data = rtrim($data, '&');

        $signature = hash_hmac('sha256', $data, $checksumkey);
        $checkout['signature'] = $signature;

        return Http::post($apiPath, $checkout)->json();
    }

    public function list()
    {
        return Payment::with(['reservation', 'reservation.trip', 'reservation.trip.vehicle'])->where([
            'user_id' => Auth::id(),
        ])->where('status', '!=', Payment::STATUS_OPEN)->orderBy('id', 'desc')->paginate(self::PER_PAGE);
    }

    private function createPayment($reservation, $transaction_code, $token)
    {
        Payment::updateOrCreate([
            'reservation_id' => $reservation->id,
            'user_id' => Auth::id(),
            'status' => Payment::STATUS_OPEN,
        ], [
            'transaction_code' => $transaction_code,
            'payment_gateway' => Payment::GATEWAY_ALEPAY,
            'price' => $reservation->actual_amount_paid,
            'token' => $token,
        ]);
    }
}
