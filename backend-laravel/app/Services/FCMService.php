<?php

namespace App\Services;

use App\Models\DeviceToken;
use Firebase\JWT\JWT;
use <PERSON>uz<PERSON>Http\Client;
use Log;

class FCMService
{
    private $apiConfig;

    public function __construct()
    {
        $this->apiConfig = [
            'url' => config('firebase.push_url'),
            'server_key' => config('firebase.server_key'),
            'device_type' => config('firebase.device_type'),
        ];
    }

    public function deleteFcm($device)
    {
        if (isset($device['device_token']) && $device['device_token']) {
            return DeviceToken::where(
                [
                    'device_token' => $device['device_token']
                ]
            )->delete();
        }
        if (isset($device['device_id']) && $device['device_id']) {
            return DeviceToken::where(
                [
                    'device_id' => $device['device_id'],
                    'user_id' => auth()->user()->id,
                ]
            )->delete();
        }
    }

    /**
     * Sending push message to single user by Firebase
     *
     * @return bool|string
     */
    public function send(string $device_token, array $notification, array $data)
    {
        $fields = [
            'message' => [
                'token' => $device_token,
                'notification' => $notification,
                'data' => $data,
            ],
        ];

        return $this->sendPushNotification($fields);
    }

    /**
     * Sending push message to multiple users by firebase
     *
     * @return bool|string
     */
    public function sendMultiple(array $device_tokens, array $notification, array $data)
    {
        $fields = [
            'registration_ids' => $device_tokens,
            'data' => $data,
            'notification' => $notification,
        ];

        return $this->sendPushNotification($fields);
    }

    public function storeFcm(string $device_token, string $device_type, string $device_id)
    {
        return DeviceToken::updateOrCreate(
            [
                'device_token' => $device_token,
                'device_type' => $device_type,
                'device_id' => $device_id,
                'user_id' => auth()->user()->id,
            ],
            [
                'active' => DeviceToken::ACTIVE,
            ]
        );
    }

    /**
     * GuzzleHTTP request to firebase servers
     *
     * @return bool
     */
    private function sendPushNotification(array $fields)
    {
        $accessToken = $this->getAccessToken();

        $client = new Client([
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $accessToken",
            ],
        ]);
        $res = $client->post(
            $this->apiConfig['url'],
            ['body' => json_encode($fields)]
        );
        // Request api fcm by json data
        // $res = $client->post($this->apiConfig['url'], json_encode($fields), ['type' => 'json']);

        $res = json_decode($res->getBody());

        if (! isset($res->name)) {
            Log::error('ERROR_PUSH_NOTIFICATION: '.$fields['message']);
        }

        return true;
    }

    private function getAccessToken()
    {
        $serviceAccountJson = json_decode(file_get_contents(storage_path(config('firebase.firebase_credentials'))), true);

        $now = time();
        $jwtHeader = ['alg' => 'RS256', 'typ' => 'JWT'];
        $jwtPayload = [
            'iss' => $serviceAccountJson['client_email'],
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
            'aud' => $serviceAccountJson['token_uri'],
            'exp' => $now + 3600,
            'iat' => $now,
        ];

        $jwt = JWT::encode($jwtPayload, $serviceAccountJson['private_key'], 'RS256');

        $response = (new Client)->post($serviceAccountJson['token_uri'], [
            'form_params' => [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt,
            ],
        ]);

        $data = json_decode($response->getBody(), true);

        return $data['access_token'];
    }
}
