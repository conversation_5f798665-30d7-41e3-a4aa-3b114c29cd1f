<?php

namespace App\Services;

use App\Jobs\SendFirebaseNotification;
use App\Models\Reservation;
use App\Models\Trip;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

abstract class BaseService
{
    const PER_PAGE = 20;

    /**
     * Calculate the booking deadline of the trip.
     */
    public function calculateBookingDeadline($trip): ?Carbon
    {
        return $trip->departure_datetime->subMinutes(Trip::BOOKING_DEADLINE_MINUTES);
    }

    public function DateTimeFormat($datetime)
    {
        return date('Y-m-d H:i:s', strtotime($datetime));
    }

    /**
     * Increase the cancel count of the user who cancels the trip or reservation.
     */
    public function increaseCancelCount()
    {
        $user = Auth::user();
        $user->increment('cancel_count');
    }

    /**
     * Generate a unique id for the trip.
     */
    public function genUniqueId()
    {
        return md5(Carbon::now());
    }

    /**
     * Throws bad request exception or otherwise
     *
     * @param  string  $message  The message response.
     * @param  int  $statusCode  The response status code.
     * @return array The response data.
     */
    public function responseError($message, $statusCode = null)
    {
        throw new \Exception($message, $statusCode ?? Response::HTTP_BAD_REQUEST);
    }

    /**
     * Generate the response success data.
     *
     * @param  mixed  $data  The response data.
     * @param  string  $message  The message response.
     * @return array The response data.
     */
    public function responseSuccess($message, $data = null)
    {
        return [
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * Send Firebase notifications
     *
     * @param  array  $data  Notification payload data
     * @return void
     */
    public function sendFirebaseNotification($data)
    {
        $sendMessageNotification = new SendFirebaseNotification($data['user_id'], $data['type'], $data);

        dispatch($sendMessageNotification)->onConnection('sync');
    }

    /**
     * Refresh the trip information - update remaining_seats if necessary.
     */
    public function refreshTripAndReservation(&$trip)
    {
        $expiredReservationIds = $trip->reservations
            ->where('status', Reservation::STATUS_PENDING_CONFIRM)
            ->where('accept_deadline', '<', now())
            ->pluck('id')
            ->toArray();

        if (! empty($expiredReservationIds)) {
            $reservationService = new ReservationService(new Reservation);

            $reservationService->cancelReservationsAuto($expiredReservationIds);
        }

        // Refresh trip to update information (like remaining_seats)
        $trip->refresh();
    }
}
