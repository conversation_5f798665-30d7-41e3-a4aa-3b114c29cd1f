<?php

namespace App\Services;

use App\Models\Review;
use App\Models\Trip;
use Carbon\Carbon;

class ReviewService extends BaseService
{
    protected $review;

    protected $message_err;

    public function __construct(Review $review)
    {
        $this->review = $review;
        $this->message_err = '';
    }

    /**
     * Create a new review.
     */
    public function review($request, $tripId, $user)
    {
        $userId = $user->id;
        // Validation before review
        if (! $this->isValid($request, $tripId, $userId)) {
            return $this->responseError($this->message_err);
        }

        $reviewInfo = $this->create($request, $tripId, $userId);

        return $this->responseSuccess(
            trans('messages.review.success.create'),
            Review::find($reviewInfo->id)
        );
    }

    /**
     * Create a new review record and store it in the database.
     */
    private function create($request, $tripId, $userId)
    {
        return $this->review->create([
            'trip_id' => $tripId,
            'reviewee_id' => $request->get('reviewee_id'),
            'reviewer_id' => $userId,
            'comment' => $request->get('comment'),
            'rating' => $request->get('rating'),
        ]);
    }

    /**
     * Validate the request before review.
     */
    private function isValid($request, $tripId, $userId)
    {
        // Check the reviews that exist
        if ($this->isReviewed($request, $userId, $tripId)) {
            $this->message_err = trans('messages.review.error.existed');

            return false;
        }

        $trip = Trip::find($tripId);
        if (! $this->isValidTrip($trip)) {
            $this->message_err = trans('messages.review.error.trip');

            return false;
        }

        // Check the due date
        if ($this->isTimeUp($trip)) {
            $this->message_err = trans('messages.review.error.timeout');

            return false;
        }

        // Check whether they went together.
        if (! $this->isWentTogether($request, $trip, $userId)) {
            $this->message_err = trans('messages.review.error.create.together');

            return false;
        }

        return true;
    }

    /**
     * Check the due date.
     */
    private function isTimeUp($trip)
    {
        return Carbon::parse($trip->updated_at)->addDays(Review::TIME_UP) < Carbon::now();
    }

    /**
     * Check the reviews that exist
     */
    private function isReviewed($request, $userId, $tripId)
    {
        return Review::where([
            'trip_id' => $tripId,
            'reviewee_id' => $request->get('reviewee_id'),
            'reviewer_id' => $userId,
        ])
            ->exists();
    }

    /**
     * Check the trip is finished.
     */
    private function isValidTrip($trip)
    {
        return $trip && $trip->status == Trip::STATUS_FINISHED;
    }

    /**
     * Check whether they went together.
     */
    private function isWentTogether($request, $trip, $reviewerId)
    {
        $driverId = $trip->user_id;
        $revieweeId = $request->get('reviewee_id');

        return $trip->reservations->first(function ($reservation) use ($reviewerId, $revieweeId, $driverId) {
            return ($reservation->user_id == $reviewerId && $revieweeId == $driverId) ||
                ($reservation->user_id == $revieweeId && $reviewerId == $driverId);
        }) !== null;
    }
}
