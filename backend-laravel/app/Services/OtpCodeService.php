<?php

namespace App\Services;

use App\Models\OtpCode;
use App\Models\User;
use App\Services\Helpers\MailService;
use App\Services\Helpers\OtpService;
use App\Services\Helpers\SmsService;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class OtpCodeService extends BaseService
{
    protected $mailService;

    protected $smsService;

    protected $otpService;

    public function __construct(MailService $mailService, SmsService $smsService, OtpService $otpService)
    {
        $this->mailService = $mailService;
        $this->smsService = $smsService;
        $this->otpService = $otpService;
    }

    /**
     * Create a OTP code for user and store it in the database.
     *
     * @param  array  $request  The request information.
     * @return \App\Models\OtpCode
     */
    public function create($data)
    {
        if (! $data) {
            return $this->responseError(trans('messages.user.error.create_otp'));
        }

        // Get email|phone key
        $emailOrPhoneKey = array_key_exists('email', $data) ? 'email' : 'phone';

        return OtpCode::create([
            'user_id' => $data['user_id'],
            'code' => Hash::make($data['code']),
            'action_type' => $data['action_type'],
            'expires_at' => $data['expires_at'],
            $emailOrPhoneKey => $data[$emailOrPhoneKey],
        ]);
    }

    /**
     * Sent the OTP code to user and stored in DB to verify.
     */
    public function sendOtp($request)
    {
        $emailOrPhoneKey = array_key_exists('email', $request->all()) ? 'email' : 'phone';
        $emailOrPhoneValue = $request->get($emailOrPhoneKey);
        $actionType = $request->get('action_type');

        // Get the user corresponding to action type
        switch ($actionType) {
            case User::ACTION_VERIFY_EMAIL_OR_PHONE:
            case User::ACTION_VERIFY_PHONE:
            case User::ACTION_VERIFY_EMAIL:
                $user = User::find(Auth::id());
                break;
            case User::ACTION_FORGET_PASSWORD:
                $user = User::where($emailOrPhoneKey, $emailOrPhoneValue)->first();
                break;

            default:
                $user = null;
                break;
        }

        if (! $user) {
            return $this->responseError(trans('messages.user.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        // Check if this email|phone has been verified
        $columnName = $emailOrPhoneKey.'_verified_at';
        if ($user->$emailOrPhoneKey && $user->$columnName && $actionType == User::ACTION_VERIFY_EMAIL_OR_PHONE) {
            return $this->responseError(trans('messages.user.success.verify.'.$emailOrPhoneKey));
        }

        // Delete old OTP before sending a new OTP
        $otpCode = $user->otpCode()->first();
        if ($otpCode) {
            $otpCode->delete();
        }

        // Create a new OTP code stored in database
        $otpInfo = $this->otpService->generateOtpInfo();
        extract($otpInfo); // Includes $code, $expiresAt
        $data = [
            'user_id' => $user->id,
            'code' => $code,
            'action_type' => $actionType,
            'expires_at' => $expiresAt,
            $emailOrPhoneKey => $emailOrPhoneValue,
        ];
        $otpCode = $this->create($data);

        $service = ($emailOrPhoneKey === 'email') ? $this->mailService : $this->smsService;
        $service->sendOtp($emailOrPhoneValue, $code);

        return $this->responseSuccess(
            trans('messages.otp.success.sent.'.$emailOrPhoneKey),
            ['expires_at' => $expiresAt]
        );
    }

    /**
     * Verify the OTP code.
     */
    public function verifyOtp($request)
    {
        $otp = $request->get('otp');
        $emailOrPhoneKey = array_key_exists('email', $request->all()) ? 'email' : 'phone';
        $emailOrPhoneValue = $request->get($emailOrPhoneKey);
        $actionType = $request->get('action_type');

        // Get the user corresponding to action type
        switch ($actionType) {
            case User::ACTION_VERIFY_EMAIL_OR_PHONE:
            case User::ACTION_VERIFY_PHONE:
            case User::ACTION_VERIFY_EMAIL:
                $user = User::find(Auth::id());
                break;
            case User::ACTION_FORGET_PASSWORD:
                $user = User::where($emailOrPhoneKey, $emailOrPhoneValue)->first();
                break;

            default:
                $user = null;
                break;
        }

        if (! $user) {
            return $this->responseError(trans('messages.user.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        // Check if this email|phone has been verified
        $columnName = $emailOrPhoneKey.'_verified_at';
        if ($user->$emailOrPhoneKey && $user->$columnName && $actionType == User::ACTION_VERIFY_EMAIL_OR_PHONE) {
            return $this->responseError(trans('messages.user.success.verify.'.$emailOrPhoneKey));
        }

        // Get the OTP of current user
        $otpCode = $user->otpCode()->first();
        if (! $otpCode || ($emailOrPhoneValue != $otpCode->$emailOrPhoneKey)) {
            return $this->responseError(trans('messages.otp.error.verify.not_yet_send'));
        }

        if (! Hash::check($otp, $otpCode->code)) {
            $isVerifySuccess = false;
            $message = trans('messages.otp.error.verify.incorrect');
        } elseif ($otpCode->expires_at < Carbon::now()) {
            $isVerifySuccess = false;
            $message = trans('messages.otp.error.verify.expired');
        } elseif ($otpCode->is_verified) {
            $isVerifySuccess = false;
            $message = trans('messages.otp.error.update_password.'.$emailOrPhoneKey);
        } else {
            $isVerifySuccess = true;
            $message = trans('messages.otp.success.verify.correct');
        }

        // Check whether the verification was successful or not
        if (! $isVerifySuccess) {
            return $this->responseError($message);
        }

        // Update information for user
        $columnName = $emailOrPhoneKey.'_verified_at';
        $user->update([
            $emailOrPhoneKey => $emailOrPhoneValue,
            $columnName => Carbon::now(),
        ]);

        switch ($actionType) {
            case User::ACTION_VERIFY_EMAIL_OR_PHONE:
            case User::ACTION_VERIFY_PHONE:
            case User::ACTION_VERIFY_EMAIL:
                // Delete OTP after successful email or phone verification
                $otpCode->delete();
                break;
            case User::ACTION_FORGET_PASSWORD:
                // Change the status for this OTP
                $otpCode->is_verified = true;
                $otpCode->save();
                break;

            default:
                break;
        }

        return $this->responseSuccess($message);
    }
}
