<?php

namespace App\Services\Helpers;

use App\Notifications\EmailNotification;
use Illuminate\Support\Facades\Notification;

class MailService extends Notification
{
    /**
     * Get mail notification template.
     *
     * @param  string  $otp  The OTP code.
     * @return EmailNotification
     */
    public function getMailNotificationTemplate($otp)
    {
        $subject = trans('messages.attribute.otp');
        $message = trans('messages.otp.success.sent.content', ['value' => $otp]);
        $markdownTemplate = 'notifications.emails.otp';

        return new EmailNotification($subject, $message, $markdownTemplate);
    }

    /**
     * Send OTP via email.
     *
     * @param  string  $mailTo  User email
     * @param  string  $otp  The OTP code.
     * @return void
     */
    public function sendOtp($mailTo, $otp)
    {
        Notification::route('mail', $mailTo)->notify($this->getMailNotificationTemplate($otp));
    }
}
