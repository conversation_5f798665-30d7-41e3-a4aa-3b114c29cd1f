<?php

namespace App\Services\Helpers;

use App\Notifications\SmsNotification;
use Illuminate\Support\Facades\Notification;

class SmsService
{
    /**
     * Send OTP via phone number.
     *
     * @param  string  $phoneTo
     * @param  string  $otp
     * @return void
     */
    public function sendOtp($phoneTo, $otp)
    {
        Notification::route('twilio', $phoneTo)->notify($this->getSmsNotificationTemplate($otp));
    }

    /**
     * Get SMS notification template.
     *
     * @param  string  $otp
     * @return SmsNotification
     */
    private function getSmsNotificationTemplate($otp)
    {
        $message = trans('messages.otp.success.sent.content', ['value' => $otp]);

        return new SmsNotification($message);
    }
}
