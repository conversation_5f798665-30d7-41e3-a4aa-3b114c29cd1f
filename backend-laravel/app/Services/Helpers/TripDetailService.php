<?php

namespace App\Services\Helpers;

use App\Models\Reservation;
use App\Models\Review;
use App\Models\Trip;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TripDetailService extends TripCommonService
{
    /**
     * Format trip detail.
     */
    public function formatTripDetail(&$trip, $searchParams)
    {
        $relatedTrips = $this->getRelatedTrips($trip->route_id);
        $prevNextTripData = $this->getPrevNextTripData($trip->id, $relatedTrips);
        $trip->relatedTrips = $relatedTrips;
        $trip->prev = $prevNextTripData['prev'];
        $trip->next = $prevNextTripData['next'];
        $trip->price = (int) $trip->price;
        $type_vehicle = $trip->vehicle()->type ?? 1;
        $percent_service_and_vat = config('price')[$type_vehicle]['percent_service_and_vat'] ?? 20;
        $trip->percent_service_and_vat = $percent_service_and_vat;
        $trip->price_service_and_vat = (int) $trip->price * ($percent_service_and_vat / 100);
        $trip->price_total = (int) $trip->price + $trip->price_service_and_vat;
        $trip->driver->title = $this->generateUserTitle($trip->driver->id);
        $trip->update_type = $this->getUpdateType($trip->route_id);

        // If not viewable from the trip search screen (NOT created, booked screen)
        if ($this->isNotEmptyLatLng($searchParams)) {
            $pickupDistance = $this->getDistanceBetweenLocations($trip, $searchParams, 'departure');
            $dropOffDistance = $this->getDistanceBetweenLocations($trip, $searchParams, 'destination');
            $pickupDistanceFormat = $this->formatDistance($pickupDistance);
            $dropOffDistanceFormat = $this->formatDistance($dropOffDistance);
            $trip->pickup_distance = $pickupDistanceFormat['value'];
            $trip->pickup_distance_unit = $pickupDistanceFormat['unit'];
            $trip->pickup_distance_level = $pickupDistanceFormat['level'];
            $trip->drop_off_distance = $dropOffDistanceFormat['value'];
            $trip->drop_off_distance_unit = $dropOffDistanceFormat['unit'];
            $trip->drop_off_distance_level = $dropOffDistanceFormat['level'];
        }

        // Get the date of the first trip
        $trip->driver->first_trip_date = $trip->driver->trips->first()?->first_trip_date;
        unset($trip->driver->trips);

        if (count($trip->co_passenger) > 0) {
            foreach ($trip->co_passenger as $key => $passenger) {
                $passenger->rating = $passenger->rating ?? 0;
                $passenger->status_text = trans('messages.reservation.status.' . $passenger->status);

                if ($passenger->user_id == Auth::id()) {
                    $trip->status_text = trans('messages.reservation.status.' . $passenger->status);
                }
            }
        }

        return $trip;
    }

    /**
     * Return the type of update based on the reservation status.
     */
    public function getUpdateType($routeId)
    {
        // Get reservation status flags
        $hasPending = $this->hasTripsWithReservationStatus($routeId, [Reservation::STATUS_PENDING_CONFIRM]);
        $hasAccepted = $this->hasTripsWithReservationStatus($routeId, [Reservation::STATUS_ACCEPTED]);

        if ($hasPending) {
            // If there are only pending reservations
            return Trip::UPDATE_TYPE_PENDING;
        } elseif ($hasAccepted) {
            // If there are only accepted reservations
            return Trip::UPDATE_TYPE_ACCEPTED;
        }   
        return Trip::UPDATE_TYPE_ALL;
    }

    /**
     * Check if there is at least one trip for a given route ID that has reservations with a specific status.
     */
    private function hasTripsWithReservationStatus($routeId, $status): bool
    {
        return Trip::where('route_id', $routeId)
            ->whereHas('reservations', function ($query) use ($status) {
                $query->whereIn('status', $status);
            })
            ->exists();
    }

    /**
     * Create a query to get the trip list.
     */
    public function getTripDetailData($id, $userId = null)
    {
        return Trip::query()
            ->select(
                'trips.id',
                'trips.route_id',
                'trips.user_id',
                'trips.vehicle_id',
                'trips.departure_address',
                'trips.departure_city',
                'trips.departure_latitude',
                'trips.departure_longitude',
                'trips.departure_date',
                // 'trips.departure_time',
                DB::raw("TIME_FORMAT(trips.departure_time, '%H:%i') AS departure_time"),
                'trips.destination_address',
                'trips.destination_city',
                'trips.destination_latitude',
                'trips.destination_longitude',
                'trips.completion_time',
                'trips.max_seat',
                'trips.price',
                'trips.auto_accept',
                'trips.notes',
                'trips.status',
                'trips.remaining_seats',
                'trips.overview_polyline',
                'trips.is_parent_route',
                'trips.has_helmet_available',
                DB::raw('
                    CASE
                        WHEN ? IS NULL THEN 0
                        WHEN EXISTS (
                            SELECT 1
                            FROM reservations
                            WHERE reservations.trip_id = trips.id
                            AND reservations.user_id = ?
                            AND reservations.status = ?
                        )
                            AND NOT EXISTS (
                                SELECT 1
                                FROM reviews
                                WHERE reviewer_id = ?
                                AND trip_id = ?
                            )
                            AND DATEDIFF(NOW(), trips.updated_at) <= ?
                        THEN 1
                        ELSE 0
                    END AS can_review
                '),
            )
            ->addBinding([$userId, $userId, Reservation::STATUS_FINISHED, $userId, $id, Review::TIME_UP], 'select')
            ->with([
                'driver' => function ($query) {
                    $query->select('id', 'full_name', 'avatar_url', 'is_avatar_verified', 'is_ID_card_verified', 'phone', 'email', 'bio', 'birthday', 'preference', 'gender', 'email_verified_at', 'phone_verified_at')
                        ->withCount(['trips as completed_trip_count' => function ($q) {
                            $q->where('trips.status', Trip::STATUS_FINISHED);
                        }])
                        ->with(['trips' => function ($q) {
                            $q->select('user_id', DB::raw('MIN(departure_date) AS first_trip_date'))
                                ->where('status', Trip::STATUS_FINISHED)
                                ->groupBy('user_id');
                        }])
                        ->withCount('reviewGiven as rating_count')
                        ->withAvg('reviewGiven as rating_avg', 'rating')
                        ->withCount(['reservations as cancelled_trip_count' => function ($q) {
                            $q->whereHas('trip', function ($subQuery) {
                                $subQuery->where('reservations.status', Trip::STATUS_CANCELLED);
                            });
                        }]);
                },
                'vehicle',
                'co_passenger' => function ($query) use ($id) {
                    $query->select(
                        'users.id',
                        'users.avatar_url',
                        'users.full_name',
                        'users.phone',
                        'users.email',
                        'users.address',
                        'reservations.id as booking_id',
                        'reservations.number_of_seats',
                        'reservations.status',
                        DB::raw('
                            IF(
                                EXISTS(
                                    SELECT *
                                    FROM reviews
                                    WHERE reviews.reviewer_id = users.id
                                    AND reviews.trip_id = ' . $id . '
                                ),
                                1,
                                0
                            ) AS is_reviewed
                        '),
                    );
                },
            ])
            ->find($id);
    }

    /**
     * Format data for prev and next trip
     */
    private function getAddressOfTrip($trip, $type)
    {
        if (! $trip) {
            return null;
        }

        return [
            'address' => $trip[$type . '_address'],
            'latitude' => $trip[$type . '_latitude'],
            'longitude' => $trip[$type . '_longitude'],
        ];
    }

    /**
     * Find stops prev and next of trip.
     */
    private function getPrevNextTripData($targetTripId, $relatedTrips)
    {
        // Parent route has no previous or next waypoints
        // The route has no stops, no previous or next waypoints
        $parentTrip = $relatedTrips->firstWhere('is_parent_route');
        if ($parentTrip == null || $targetTripId == $parentTrip->id) {
            return [
                'prev' => null,
                'next' => null,
            ];
        }

        // Remove the parent route
        $relatedTrips = $relatedTrips->reject(function ($trip) {
            return $trip['is_parent_route'];
        })->keyBy('id');
        $prevTrip = $relatedTrips->get($targetTripId - 1);
        $nextTrip = $relatedTrips->get($targetTripId + 1);

        return [
            'prev' => $this->getAddressOfTrip($prevTrip, 'departure'),
            'next' => $this->getAddressOfTrip($nextTrip, 'destination'),
        ];
    }

    /**
     * Get the related trips.
     */
    private function getRelatedTrips($routeId)
    {
        return Trip::query()
            ->select(
                'id',
                'is_parent_route',
                'price',
                'overview_polyline',
                'completion_time',
                'distance',
                'departure_address',
                'departure_city',
                'departure_latitude',
                'departure_longitude',
                'destination_address',
                'destination_city',
                'destination_latitude',
                'destination_longitude',
            )
            ->where('route_id', $routeId)
            ->orderBy('id')
            ->get();
    }

    /**
     * If not viewable from the trip search screen (NOT created, booked screen)
     */
    private function isNotEmptyLatLng($searchParams)
    {
        $keys = ['departure_latitude', 'departure_longitude', 'destination_latitude', 'destination_longitude'];
        foreach ($keys as $key) {
            if (! isset($searchParams[$key])) {
                return false;
            }
        }

        return true;
    }
}
