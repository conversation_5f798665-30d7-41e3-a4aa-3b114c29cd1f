<?php

namespace App\Services\Helpers;

use App\Helpers\Format;
use App\Models\Trip;
use App\Models\Vehicle;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class TripSearchService extends TripCommonService
{
    /**
     * Amenities: Allow pets, Allow smoking.
     */
    const ALLOW_PET = 10;

    const ALLOW_SMOKING = 7;

    // Filter
    const IS_ID_CARD_VERIFIED = 1;

    const TRUE_VALUE = 1;

    // Pagination
    const LIMIT = 5;

    const PER_PAGE = 20;

    /**
     * Apply search request.
     */
    public function filterSearchConditions($query, $searchParams)
    {
        $query = $this->filterTripConditions($query, $searchParams);
        $query = $this->filterDriverAmenities($query, $searchParams);

        return $query;
    }

    /**
     * Filter the similar trips.
     */
    public function filterSimilarTrip($query, $searchParams)
    {
        $query = $this->filterVehicleType($query, $searchParams);

        return $query
            ->take(self::LIMIT)
            ->get();
    }

    /**
     * Filter by vehicle type.
     */
    public function filterVehicleType($query, $searchParams)
    {
        if ($vehicleType = Arr::get($searchParams, 'vehicle_type')) {
            return $query->clone()->whereHas('vehicle', function ($q) use ($vehicleType) {
                $q->where('type', $vehicleType);
            });
        }

        return $query;
    }

    /**
     * get filter condition count.
     */
    public function getFilterConditionsCount($query, $searchParams)
    {
        $data = [
            'departure_time' => $this->formatDepartureTimeCounts($query),
            'verified_profile' => $this->countDriverVerifiedIDCard($query),
            'amenities' => [
                'auto_accept' => $this->getAutoAcceptTripCounts($query),
            ],
        ];

        // Return extra filters for the car
        if (Arr::get($searchParams, 'vehicle_type') == Vehicle::TYPE_CAR) {
            $data['amenities']['allow_smoking'] = $this->countDriverAllowSmoking($query);
            $data['amenities']['allow_pets'] = $this->countDriverAllowPets($query);
        }

        // Return extra filters for the motorbike
        if (Arr::get($searchParams, 'vehicle_type') == Vehicle::TYPE_MOTOR) {
            $data['amenities']['has_helmet_available'] = $this->countDriverSupportHelmet($query);
        }

        return $data;
    }

    /**
     * Create a query to get the trip list.
     */
    public function getBaseSearchTripQuery($searchParams)
    {
        $maxSeat = $searchParams['max_seat'] ?? Trip::MIN_SEAT;
        $departureDate = $searchParams['departure_date'] ?? date('Y-m-d');

        $query = Trip::query()
            ->select(
                'trips.id',
                'trips.user_id',
                'trips.vehicle_id',
                'trips.departure_address',
                'trips.departure_city',
                'trips.departure_latitude',
                'trips.departure_longitude',
                'trips.departure_date',
                // 'trips.departure_time',
                DB::raw("TIME_FORMAT(trips.departure_time, '%H:%i') AS departure_time"),
                'trips.destination_address',
                'trips.destination_city',
                'trips.destination_latitude',
                'trips.destination_longitude',
                'trips.completion_time',
                'trips.price',
                'trips.auto_accept',
                'trips.has_helmet_available',
                'trips.max_seat',
                'trips.created_at',
                'trips.status',
                'trips.remaining_seats',
                'trips.distance',
            )
            ->with([
                'driver' => function ($q) {
                    $q->select('id', 'full_name', 'avatar_url', 'is_avatar_verified', 'is_ID_card_verified', 'bio', 'birthday', 'preference', 'gender', 'email_verified_at', 'phone_verified_at')
                        ->withAvg('reviewReceived as rating_avg', 'rating');
                },
                'vehicle:id,type,number_of_seats',
            ])
            ->where([
                ['trips.status', Trip::STATUS_OPEN],
                ['trips.max_seat', '>=', $maxSeat],
                ['trips.departure_date', $departureDate],
            ]);

        if(!empty($searchParams['departure_city'])){
            $departureCityNormalized = Format::formatTextVN($searchParams['departure_city']);
            $query->where('trips.departure_city_normalized', 'like', "%{$departureCityNormalized}%");
        }

        if(!empty($searchParams['departure_city'])){
            $destinationCityNormalized = Format::formatTextVN($searchParams['destination_city']);
            $query->where('trips.destination_city_normalized', 'like', "%{$destinationCityNormalized}%");
        }

        if(!empty($searchParams['ignore_trip_id'])){
            $query->where('trips.id', '<>', $searchParams['ignore_trip_id']);
        }

        return $query;    
    }

    /**
     * Get the number of trips.
     */
    public function getTotalTrip($query)
    {
        return $query->get()->count();
    }

    /**
     * Get the number of trips by car.
     */
    public function getVehicleTripCount($query, $vehicleType)
    {
        return $query->clone()->whereHas('vehicle', function ($q) use ($vehicleType) {
            $q->where('type', $vehicleType);
        })
            ->get()
            ->count();
    }

    /**
     * Prepare data.
     */
    public function getTripListData($query, $request)
    {
        $tripQuery = clone $query;

        $searchParams = $request->get('cond');
        $tripQuery = $this->sortBy($tripQuery, $searchParams);

        $trips = $tripQuery->paginate(self::PER_PAGE);
        // Format trip list
        $trips = $this->formatTripList($trips, $searchParams);

        return $this->sortByProximity($trips, $searchParams);
    }

    /**
     * Format trip list.
     */
    public function formatTripList(&$trips, $searchParams = [])
    {
        $formattedTrips = $trips->map(function ($trip) use ($searchParams) {
            if ($searchParams) {
                $pickupDistance = $this->getDistanceBetweenLocations($trip, $searchParams, 'departure');
                $dropOffDistance = $this->getDistanceBetweenLocations($trip, $searchParams, 'destination');
                $pickupDistanceFormat = $this->formatDistance($pickupDistance);
                $dropOffDistanceFormat = $this->formatDistance($dropOffDistance);

                $trip->pickup_distance = $pickupDistanceFormat['value'];
                $trip->pickup_distance_unit = $pickupDistanceFormat['unit'];
                $trip->pickup_distance_level = $pickupDistanceFormat['level'];
                $trip->drop_off_distance = $dropOffDistanceFormat['value'];
                $trip->drop_off_distance_unit = $dropOffDistanceFormat['unit'];
                $trip->drop_off_distance_level = $dropOffDistanceFormat['level'];
            }

            $trip->price = (int) $trip->price;
            $trip->driver->title = $this->generateUserTitle($trip->driver->id);

            return $trip;
        });

        return $formattedTrips;
    }

    /**
     * Count drivers who support Helmets if it is a motorbike.
     */
    private function countDriverSupportHelmet($query)
    {
        return $query
            ->clone()
            ->where('has_helmet_available', self::TRUE_VALUE)
            ->count();
    }

    /**
     * Get the number of drivers whose allow pets.
     */
    private function countDriverAllowPets($query)
    {
        return $query->clone()->whereHas('driver', function ($q) {
            $q->whereRaw('FIND_IN_SET(?, preference)', [self::ALLOW_PET]);
        })->count();
    }

    /**
     * Get the number of drivers whose allow smoking.
     */
    private function countDriverAllowSmoking($query)
    {
        return $query->clone()->whereHas('driver', function ($q) {
            $q->whereRaw('FIND_IN_SET(?, preference)', [self::ALLOW_SMOKING]);
        })->count();
    }

    /**
     * Get the number of drivers whose ID cards have been verified.
     */
    private function countDriverVerifiedIDCard($query)
    {
        return $query->clone()->whereHas('driver', function ($q) {
            $q->where('is_ID_card_verified', self::TRUE_VALUE);
        })->count();
    }

    /**
     * Filter by trip condition.
     */
    private function filterTripConditions($query, $searchParams)
    {
        // Filter auto accept
        if ($autoAccept = Arr::get($searchParams, 'auto_accept')) {
            $query->where('auto_accept', $autoAccept);
        }

        // Filter departure time
        if ($timeFilters = Arr::get($searchParams, 'departure_time')) {
            $query->where(function ($query) use ($timeFilters) {
                foreach ($timeFilters as $filterKey) {
                    switch ($filterKey) {
                        case 'before-6':
                            $query->orWhereTime('departure_time', '<', '06:00:00');
                            break;
                        case '6-to-12':
                            $query->orWhereBetween('departure_time', ['06:00:00', '12:00:00']);
                            break;
                        case '12-to-18':
                            $query->orWhereBetween('departure_time', ['12:00:00', '18:00:00']);
                            break;
                        case 'after-18':
                            $query->orWhereTime('departure_time', '>', '18:00:00');
                            break;
                    }
                }
            });
        }

        return $query;
    }

    /**
     * Filter by user amenities.
     */
    private function filterDriverAmenities($query, $searchParams)
    {
        if (Arr::get($searchParams, 'is_ID_card_verified')) {
            $query = $query->whereHas('driver', function ($q) {
                $q->where('is_ID_card_verified', self::IS_ID_CARD_VERIFIED);
            });
        }

        if (Arr::get($searchParams, 'allow_smoking')) {
            $query = $query->whereHas('driver', function ($q) {
                $q->whereRaw('FIND_IN_SET(?, preference)', [self::ALLOW_SMOKING]);
            });
        }

        if (Arr::get($searchParams, 'allow_pets')) {
            $query = $query->whereHas('driver', function ($q) {
                $q->whereRaw('FIND_IN_SET(?, preference)', [self::ALLOW_PET]);
            });
        }

        if ($this->isMotorbikeAndHasHelmet($searchParams)) {
            $query = $query->where('has_helmet_available', self::TRUE_VALUE);
        }

        return $query;
    }

    /**
     * Format the number of departure time range.
     */
    private function formatDepartureTimeCounts($query)
    {
        $departureTimeCounts = $this->getDepartureTimeCounts($query);

        $ranges = ['before_6', '6_to_12', '12_to_18', 'after_18'];
        $formattedCounts = [];

        foreach ($ranges as $range) {
            $formattedCounts[$range] = $departureTimeCounts[$range] ?? 0;
        }

        return $formattedCounts;
    }

    /**
     * Get the number of trips automatically accepted.
     */
    private function getAutoAcceptTripCounts($query)
    {
        return $query->clone()->where('auto_accept', self::TRUE_VALUE)->count();
    }

    /**
     * Get conditions that satisfy the trips.
     */
    private function getDepartureTimeCounts($query)
    {
        $counts = $query->clone()
            ->selectRaw('CASE
                WHEN departure_time < "06:00:00" THEN "before_6"
                WHEN departure_time BETWEEN "06:00:00" AND "12:00:00" THEN "6_to_12"
                WHEN departure_time BETWEEN "12:00:00" AND "18:00:00" THEN "12_to_18"
                ELSE "after_18" END AS departure_time_range')
            ->get()
            ->pluck('departure_time_range');

        return $counts->countBy();
    }

    /**
     * Check the vehicle is a motorbike and support Helmet.
     */
    private function isMotorbikeAndHasHelmet($searchParams)
    {
        $vehicleType = Arr::get($searchParams, 'vehicle_type');
        if ($vehicleType != Vehicle::TYPE_MOTOR) {
            return false;
        }

        return Arr::get($searchParams, 'has_helmet_available') == self::TRUE_VALUE;
    }

    /**
     * Apply sort by.
     */
    private function sortBy($query, $searchParams)
    {
        switch ($searchParams['sort_by']) {
            case 'earliest-departure': // default
                $query->orderBy('departure_date')->orderBy('departure_time');
                break;
            case 'lowest-price':
                $query->orderBy('price', 'asc');
                break;
            case 'shortest-ride':
                $query->orderBy('distance', 'asc');
                break;

            default:
                $query->orderBy('departure_date')->orderBy('departure_time');
                break;
        }

        return $query;
    }

    /**
     * Apply sort by proximity.
     */
    private function sortByProximity($trips, $searchParams)
    {
        if (in_array('close-to-departure-point', $searchParams)) {
            return collect($trips)
                ->sortBy(['pickup_distance_level', 'pickup_distance'])
                ->values();
        }

        if (in_array('close-to-destination-point', $searchParams)) {
            return collect($trips)
                ->sortBy(['drop_off_distance_level', 'drop_off_distance'])
                ->values();
        }

        return $trips;
    }
}
