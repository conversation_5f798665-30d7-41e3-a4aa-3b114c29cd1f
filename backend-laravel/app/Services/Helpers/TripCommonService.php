<?php

namespace App\Services\Helpers;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class TripCommonService
{
    const DISTANCE_LONG_LEVEL = 3;

    const DISTANCE_LONG_RANGE = 10;

    const DISTANCE_MEDIUM_LEVEL = 2;

    const DISTANCE_SHORT_LEVEL = 1;

    const DISTANCE_SHORT_RANGE = 5;

    const EARTH_RADIUS = 6371; // Earth radius (km)

    const POSITIVE_REVIEW = 5; // 5 star

    const STATUS_OK = 'OK';

    /**
     * Format the distance.
     */
    public function formatDistance($distance)
    {
        if ($distance >= 1000) {
            $distance_value = round($distance / 1000, 1, PHP_ROUND_HALF_UP);
            $unit = 'km';
        } else {
            $distance_value = $distance;
            $unit = 'm';
        }
        $level = $this->getDistanceLevel($distance);

        return [
            'value' => $distance_value,
            'level' => $level,
            'unit' => $unit,
        ];
    }

    /**
     * Generate user title.
     */
    public function generateUserTitle($userId)
    {
        // Get user data.
        $user = $this->getUserTitleData($userId);

        // Get user level
        $userLevel = $this->getUserTitleLevel($user);

        return trans('messages.user.title.'.$userLevel);
    }

    /**
     * Get distance between locations.
     */
    public function getDistanceBetweenLocations($trip, $searchParams, $key)
    {
        $lat1 = $searchParams[$key.'_latitude'];
        $lng1 = $searchParams[$key.'_longitude'];
        $lat2 = $trip->{$key.'_latitude'};
        $lng2 = $trip->{$key.'_longitude'};

        // Get google api config
        $apiKey = config('google_api.key');
        $apiPath = config('google_api.distancematrix');

        // Generate API path
        $origins = urlencode("{$lat1},{$lng1}");
        $destinations = urlencode("{$lat2},{$lng2}");
        $apiKey = urlencode($apiKey);
        $apiPath .= "?origins={$origins}&destinations={$destinations}&key={$apiKey}";

        // Call API
        $result = Http::get($apiPath)->json();

        // If calling the api fails => use Haversine Formula
        $distance = ! empty($result['rows'])
            ? $result['rows'][0]['elements'][0]['distance']['value']
            : $this->calculateDistanceBetweenLocations($trip, $searchParams, $key);

        return $distance;
    }

    /**
     * Calculate distance.
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = self::EARTH_RADIUS;

        // Converting to radians
        $deltaLat = deg2rad($lat2 - $lat1);
        $deltaLng = deg2rad($lng2 - $lng1);

        // Haversine Formula
        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($deltaLng / 2) * sin($deltaLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        $distance = $earthRadius * $c;

        return ceil($distance); // km
    }

    /**
     * Calculate distance between locations.
     */
    private function calculateDistanceBetweenLocations($trip, $searchParams, $key)
    {
        $lat1 = $searchParams[$key.'_latitude'];
        $lng1 = $searchParams[$key.'_longitude'];
        $lat2 = $trip->{$key.'_latitude'};
        $lng2 = $trip->{$key.'_longitude'};

        return $this->calculateDistance($lat1, $lng1, $lat2, $lng2);
    }

    /**
     * Get the near and far levels for distance.
     */
    private function getDistanceLevel($distance)
    {
        $distance = round($distance / 1000, 1, PHP_ROUND_HALF_UP);
        switch (true) {
            case $distance >= 0 && $distance <= self::DISTANCE_SHORT_RANGE:
                return self::DISTANCE_SHORT_LEVEL;
            case $distance > self::DISTANCE_SHORT_RANGE && $distance <= self::DISTANCE_LONG_RANGE:
                return self::DISTANCE_MEDIUM_LEVEL;
            case $distance > self::DISTANCE_LONG_RANGE:
                return self::DISTANCE_LONG_LEVEL;

            default:
                return 0;
        }
    }

    /**
     * Get user data.
     */
    private function getUserTitleData($userId)
    {
        return User::select([
            'id',
            'email_verified_at',
            'phone_verified_at',
            'is_ID_card_verified',
            DB::raw('CAST(TIMESTAMPDIFF(MONTH, created_at, NOW()) AS UNSIGNED) as registration_time'),
        ])
            ->withCount('reviewReceived as total_review_received')
            ->withCount(['reviewReceived as positive_rating_count' => function ($query) {
                $query->where('rating', self::POSITIVE_REVIEW);
            }])
            ->find($userId);
    }

    /**
     * Get user title level.
     */
    private function getUserTitleLevel($user)
    {
        for ($i = 5; $i > 1; $i--) {
            $method = 'isUserLevel'.$i;
            if (method_exists($this, $method) && $this->$method($user)) {
                return $i;
            }
        }

        return (int) 1;
    }

    /**
     * Check if user is level 2.
     */
    private function isUserLevel2($user)
    {
        return
            $user->email_verified_at
            && $user->phone_verified_at
            && ($user->positive_rating_count >= 1)
            && (($user->positive_rating_count / $user->total_review_received * 100) > 60)
            && ($user->registration_time >= 1);
    }

    /**
     * Check if user is level 3.
     */
    private function isUserLevel3($user)
    {
        return
            $user->email_verified_at
            && $user->phone_verified_at
            && ($user->positive_rating_count >= 3)
            && (($user->positive_rating_count / $user->total_review_received * 100) > 70)
            && ($user->registration_time >= 3);
    }

    /**
     * Check if user is level 4.
     */
    private function isUserLevel4($user)
    {
        return
            $user->email_verified_at
            && $user->phone_verified_at
            && $user->is_ID_card_verified
            && ($user->positive_rating_count >= 6)
            && (($user->positive_rating_count / $user->total_review_received * 100) > 80)
            && ($user->registration_time >= 6);
    }

    /**
     * Check if user is level 5.
     */
    private function isUserLevel5($user)
    {
        return
            $user->email_verified_at
            && $user->phone_verified_at
            && $user->is_ID_card_verified
            && ($user->positive_rating_count >= 12)
            && (($user->positive_rating_count / $user->total_review_received * 100) > 90)
            && ($user->registration_time >= 12);
    }
}
