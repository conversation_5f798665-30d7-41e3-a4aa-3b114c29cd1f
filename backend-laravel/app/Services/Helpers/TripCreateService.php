<?php

namespace App\Services\Helpers;

use App\Helpers\Format;
use App\Models\Trip;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Support\Str;

class TripCreateService
{
    /**
     * Generate a unique route id.
     */
    public function generateRouteId($userId)
    {
        return Str::uuid()->toString().'-'.$userId;
    }

    /**
     * Generate list of trips.
     */
    public function generateTripList($request, $userId, $routeId)
    {
        $points = $request->points;
        $prices = $request->prices;
        $distances = $request->distances;
        $completionTimes = $request->completion_times;
        $overviewPolyline = $request->overview_polyline;

        $dateTimeOfTrip = $this->getDateTimeOfTrip($request, $completionTimes);
        $hasHelmetAvailable = Vehicle::find($request->vehicle_id)->type == Vehicle::TYPE_MOTOR
            ? ($request->has_helmet_available ?? 0)
            : 0;
        $tripList = [];

        $totalPoints = count($points);
        $data = [
            'user_id' => $userId,
            'route_id' => $routeId,
            'start_point' => $points[0],
            'end_point' => $points[$totalPoints - 1],
            'price' => $prices[0],
            'distance' => $distances[0],
            'departure_date' => $dateTimeOfTrip[0]['date'],
            'departure_time' => $dateTimeOfTrip[0]['time'],
            'completion_time' => $dateTimeOfTrip[0]['completion_time'],
            'overview_polyline' => $overviewPolyline[0],
            'is_parent_route' => true, // Mark a parent trip
            'max_seat' => $request->max_seat,
            'vehicle_id' => $request->vehicle_id,
            'auto_accept' => $request->auto_accept,
            'has_helmet_available' => $hasHelmetAvailable,
            'remaining_seats' => $request->max_seat,
            'notes' => $request->notes,
        ];
        // Create parent trip
        $tripList[] = $this->createTripData($data);

        // Create child trips when the total points is greater than 2
        if ($totalPoints > 2) {
            for ($i = 0; $i < $totalPoints - 1; $i++) {
                $index = $i + 1;
                $data = array_merge($data, [
                    'start_point' => $points[$i],
                    'end_point' => $points[$index],
                    'price' => $prices[$index],
                    'distance' => $distances[$index],
                    'departure_date' => $dateTimeOfTrip[$index]['date'],
                    'departure_time' => $dateTimeOfTrip[$index]['time'],
                    'completion_time' => $dateTimeOfTrip[$index]['completion_time'],
                    'overview_polyline' => $overviewPolyline[$index],
                    'is_parent_route' => false, // Mark a child trip
                ]);

                $tripList[] = $this->createTripData($data);
            }
        }

        return $tripList;
    }

    /**
     * Retrieve trips from the database based on route id.
     */
    public function getTripsByRouteId($routeId)
    {
        $trips = Trip::where('route_id', $routeId)->get();

        return $trips->transform(function ($trip) {
            $trip->price = (int) $trip->price;

            return $trip;
        });
    }

    /**
     * Save trips to the database.
     */
    public function saveTrips($tripList)
    {
        foreach ($tripList as $tripData) {
            Trip::create($tripData);
        }
    }

    /**
     * Create a base trip data.
     */
    private function createTripData($data)
    {
        $startPoint = $data['start_point'];
        $endPoint = $data['end_point'];
        $departureCity = $startPoint['city_name'];
        $destinationCity = $endPoint['city_name'];
        $departureCityNormalized = Format::formatTextVN($departureCity);
        $destinationCityNormalized = Format::formatTextVN($destinationCity);

        return [
            'user_id' => $data['user_id'],
            'route_id' => $data['route_id'],
            'vehicle_id' => $data['vehicle_id'],
            'max_seat' => $data['max_seat'],
            'remaining_seats' => $data['max_seat'],
            'auto_accept' => $data['auto_accept'],
            'has_helmet_available' => $data['has_helmet_available'],
            'notes' => $data['notes'],
            'price' => $data['price'],
            'distance' => $data['distance'],
            'overview_polyline' => $data['overview_polyline'],
            'is_parent_route' => $data['is_parent_route'],
            'completion_time' => $data['completion_time'],
            'departure_date' => $data['departure_date'],
            'departure_time' => $data['departure_time'],
            'departure_address' => $startPoint['address_name'],
            'departure_city' => $departureCity,
            'departure_city_normalized' => $departureCityNormalized,
            'departure_latitude' => $startPoint['latitude'],
            'departure_longitude' => $startPoint['longitude'],
            'destination_address' => $endPoint['address_name'],
            'destination_city' => $destinationCity,
            'destination_city_normalized' => $destinationCityNormalized,
            'destination_latitude' => $endPoint['latitude'],
            'destination_longitude' => $endPoint['longitude'],
        ];
    }

    /**
     * Get the trip departure time.
     */
    private function getDateTimeOfTrip($request, $completionTimes)
    {
        $date = $request->get('date');
        $time = $request->get('time');

        $data = [];
        $totalPoints = count($completionTimes);
        $dateTime = Carbon::createFromFormat('Y-m-d H:i', $date.' '.$time);

        for ($i = 0; $i < $totalPoints; $i++) {
            if ($i > 1) {
                $dateTime->addMinutes($completionTimes[$i]);
            }

            $data[] = [
                'date' => $dateTime->format('Y-m-d'),
                'time' => $dateTime->format('H:i'),
                'completion_time' => $completionTimes[$i],
            ];
        }

        return $data;
    }
}
