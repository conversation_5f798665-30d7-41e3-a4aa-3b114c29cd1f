<?php

namespace App\Services\Helpers;

use App\Models\UserTemp;
use App\Services\BaseService;
use App\Services\UserTempService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;

class OtpService extends BaseService
{
    protected $userTempService;

    protected $mailService;

    protected $smsService;

    const OTP_EXPIRATION_TIME = 180; // 180 seconds

    public function __construct(UserTempService $userTempService, MailService $mailService, SmsService $smsService)
    {
        $this->userTempService = $userTempService;
        $this->mailService = $mailService;
        $this->smsService = $smsService;
    }

    /**
     * Create a new temporary User record and store it in the database.
     *
     * @return array The result of OTP verification
     * @return \App\Models\userTemp
     */
    public function sendOtp($request)
    {
        $emailOrPhoneKey = array_key_exists('email', $request) ? 'email' : 'phone';
        $emailOrPhoneValue = Arr::get($request, $emailOrPhoneKey);

        // Get formatted OTP information
        $otpInfo = $this->generateOtpInfo();
        extract($otpInfo); // Includes $code, $expiresAt

        // Add new values to request
        $request['otp'] = $code;
        $request['expires_at'] = $expiresAt;

        // Get the temporary user
        $userTemp = UserTemp::where($emailOrPhoneKey, $emailOrPhoneValue)->first();
        if (! $userTemp) {
            $this->userTempService->create($request);
        } else {
            // Re-sent OTP
            $request['count'] = $userTemp->count + 1;
            $request['key'] = $emailOrPhoneKey;
            $this->userTempService->update($request);
        }

        $service = ($emailOrPhoneKey === 'email') ? $this->mailService : $this->smsService;
        $service->sendOtp($emailOrPhoneValue, $code);

        return $this->responseSuccess(
            trans('messages.otp.success.sent.'.$emailOrPhoneKey),
            ['expires_at' => $expiresAt]
        );
    }

    /**
     * Verify the OTP code.
     *
     * @param  array  $request  The request information.
     * @return array The result of OTP verification
     */
    public function verifyOtp($request)
    {
        $otp = Arr::get($request, 'otp');
        $emailOrPhoneKey = array_key_exists('email', $request) ? 'email' : 'phone';
        $emailOrPhoneValue = Arr::get($request, $emailOrPhoneKey);

        // Get the temporary user in database
        $userTemp = UserTemp::where($emailOrPhoneKey, $emailOrPhoneValue)->first();
        if (! $userTemp) {
            return $this->responseError(trans('messages.otp.error.verify.not_yet_send'));
        }

        if (! Hash::check($otp, $userTemp->otp)) {
            $isVerifySuccess = false;
            $message = trans('messages.otp.error.verify.incorrect');
        } elseif ($userTemp->expires_at < Carbon::now()) {
            $isVerifySuccess = false;
            $message = trans('messages.otp.error.verify.expired');
        } elseif ($userTemp->is_verified) {
            $isVerifySuccess = false;
            $message = trans('messages.otp.error.verify.'.$emailOrPhoneKey);
        } else {
            $isVerifySuccess = true;
            $message = trans('messages.otp.success.verify.correct');
        }

        // Whether registration is allowed or not
        if (! $isVerifySuccess) {
            return $this->responseError($message);
        }

        // Flag verified and update to database
        $userTemp->update(['is_verified' => true]);

        return $this->responseSuccess($message);
    }

    /**
     * Generate the OTP data.
     *
     * @return array
     */
    private function generateOtpInfo()
    {
        $code = random_int(100000, 999999);
        $expiresAt = Carbon::now()->addSeconds(self::OTP_EXPIRATION_TIME);

        return compact('code', 'expiresAt');
    }
}
