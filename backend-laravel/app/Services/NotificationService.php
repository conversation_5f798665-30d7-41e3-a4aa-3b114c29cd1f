<?php

namespace App\Services;

use App\Models\Notification;

class NotificationService
{
    /**
     * Get list of notifications
     */
    public function list()
    {
        return Notification::where('notifiable_id', auth()->user()->id)
            ->orderBy('created_at', 'DESC')
            ->paginate(20);
    }

    /**
     * Mark notifications as read
     */
    public function markAsRead()
    {
        return Notification::where([
            'notifiable_id' => auth()->user()->id,
        ])->update([
            'read_at' => now(),
        ]);
    }
}
