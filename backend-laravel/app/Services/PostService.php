<?php

namespace App\Services;

use App\Models\Post;

class PostService extends BaseService
{
    /**
     * Get detail post
     *
     * @param  mixed  $request
     * @return void
     */
    public function getDetailPost($id)
    {
        $post = Post::where(['id' => $id, 'status' => 1])->first();
        if (! $post) {
            abort(404, 'Not Found');
        }

        return $post;
    }

    /**
     * Get posts
     *
     * @param  mixed  $request
     * @return void
     */
    public function getPosts($request)
    {
        return Post::where([
            'type' => $request['type'] ?? 1,
            'status' => 1,
        ])->limit($request['limit'] ?? 5)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
