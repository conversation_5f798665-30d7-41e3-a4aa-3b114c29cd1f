<?php

namespace App\Services;

use App\Jobs\SendBookingAcceptEmail;
use App\Jobs\SendBookingCancelEmail;
use App\Jobs\SendFirebaseNotification;
use App\Models\Reservation;
use App\Models\Trip;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ReservationService extends BaseService
{
    const ACTION_TYPE_ACCEPT_BOOKING = 'accept_booking';

    const ACTION_TYPE_CANCEL_BOOKING = 'cancel_booking';

    const ACTION_TYPE_CREATE_BOOKING = 'create_booking';

    const ACTION_TYPE_REJECT_BOOKING = 'reject_booking';

    const ACTION_TYPE_REJECT_AFTER_ACCEPTED_BOOKING = 'reject_after_accepted_booking';

    protected $reservation;

    protected $message_err;

    public function __construct(Reservation $reservation)
    {
        $this->reservation = $reservation;
        $this->message_err = '';
    }

    /**
     * Accept Booking - Driver Action.
     */
    public function acceptReservation($id)
    {
        $reservation = $this->reservation->find($id);
        if (! $reservation) {
            return $this->responseError(trans('messages.reservation.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        if (! $this->isAllowedAcceptorReject($reservation)) {
            return $this->responseError($this->message_err);
        }

        $reservation->update(['status' => Reservation::STATUS_ACCEPTED]);

        $sendMessageNotification = new SendFirebaseNotification(
            $reservation->user_id,
            self::ACTION_TYPE_ACCEPT_BOOKING,
            [
                'title' => trans('messages.reservation.notification.accepted'),
                'body' => 'Chuyến đi từ '.$reservation->trip->departure_address.' đến '.$reservation->trip->destination_address.' đã được chấp bời tài xế.',
                'type' => self::ACTION_TYPE_ACCEPT_BOOKING,
                'trip_id' => $reservation->trip_id,
                'reservation_id' => $reservation->id,
                'sound' => config('firebase.sound'),
            ],
        );

        dispatch($sendMessageNotification)->onConnection('sync');

        dispatch(new SendBookingAcceptEmail($reservation));

        return $this->responseSuccess(trans('messages.reservation.success.accepted'), $reservation);
    }

    /**
     * Cancel booking - User action.
     */
    public function cancelReservation($id, $request)
    {
        try {
            DB::beginTransaction();

            $reservation = $this->reservation->find($id);
            if (! $reservation) {
                return $this->responseError(trans('messages.reservation.error.not_found'), Response::HTTP_NOT_FOUND);
            }

            if (! $this->isAllowedCancel($reservation)) {
                return $this->responseError($this->message_err);
            }

            // Cancel booking
            $reservation->update([
                'status' => Reservation::STATUS_CANCELLED,
                'cancel_reason' => $request['cancel_reason'],
            ]);

            // Update remaining_seats
            $this->updateRemainingSeats($reservation);

            // Increase cancel count
            $this->increaseCancelCount();

            // Send Firebase notifications
            $sendMessageNotification = new SendFirebaseNotification(
                $reservation->user_id,
                self::ACTION_TYPE_CANCEL_BOOKING,
                $this->makeCancelNotiPayload($reservation),
            );
            dispatch($sendMessageNotification)->onConnection('sync');

            dispatch(new SendBookingCancelEmail($reservation));

            DB::commit();

            return $this->responseSuccess(trans('messages.reservation.success.cancelled'), $reservation);
        } catch (\Exception $e) {
            DB::rollback();

            return $this->responseError($e->getMessage());
        }
    }

    /**
     * Cancel reservations automatically.
     */
    public function cancelReservationsAuto($reservationIds)
    {
        try {
            DB::beginTransaction();

            foreach ($reservationIds as $id) {
                $reservation = $this->reservation->find($id);
                if (! $reservation) {
                    return $this->responseError(trans('messages.reservation.error.not_found'), Response::HTTP_NOT_FOUND);
                }

                if (! $this->isAllowedCancelAuto($reservation)) {
                    return $this->responseError($this->message_err);
                }

                // Cancel booking
                $reservation->update([
                    'status' => Reservation::STATUS_OUTDATED,
                    'cancel_reason' => trans('messages.reservation.jobs.default_cancelled'),
                ]);

                // Update remaining_seats
                $this->updateRemainingSeats($reservation);

                // Send Firebase notifications
                $sendMessageNotification = new SendFirebaseNotification(
                    $reservation->user_id,
                    self::ACTION_TYPE_CANCEL_BOOKING,
                    $this->makeCancelAutoNotiPayload($reservation),
                );
                dispatch($sendMessageNotification)->onConnection('sync');

                dispatch(new SendBookingCancelEmail($reservation));
            }

            DB::commit();

            return $this->responseSuccess(trans('messages.reservation.success.cancelled_auto'), $reservation);
        } catch (\Exception $e) {
            DB::rollback();

            return $this->responseError($e->getMessage());
        }
    }

    /**
     * Get the booked trip list.
     */
    public function getReservations($request)
    {
        $trips = $this->getReservationsData($request->status);
        $this->formatReservations($trips);

        return $this->responseSuccess(trans('messages.trip.success.reservations'), $trips);
    }

    /**
     * Make a reservation.
     */
    public function makeReservation($tripId, $request)
    {
        try {
            DB::beginTransaction();

            $trip = Trip::find($tripId);
            if (! $trip) {
                return $this->responseError(trans('messages.trip.error.not_found'), Response::HTTP_NOT_FOUND);
            }

            // update remaining_seats if necessary
            $this->refreshTripAndReservation($trip);

            if (! $this->isValidForReservation($trip, $request)) {
                return $this->responseError($this->message_err);
            }

            $request->merge([
                'user_id' => Auth::id(),
                'trip_id' => $trip->id,
                'actual_amount_paid' => $this->calculateTripPrice($request, $trip->price),
                'status' => $this->getStatusBaseTrip($trip),
                'accept_deadline' => $this->calculateAcceptDeadline($trip),
            ]);
            $reservation = $this->create($request);

            // Send Firebase notifications
            // $bookingNotiPayload = $this->makeBookingNotiPayload($reservation);
            // foreach ($bookingNotiPayload as $payload) {
            //     $this->sendFirebaseNotification($payload);
            // }

            // Update remaining seats of the trip
            // $remainingSeats = $this->getRemainingSeats($request, $trip);
            // $trip->update(['remaining_seats' => $remainingSeats]);

            // $reservation->refresh();

            DB::commit();

            return $this->responseSuccess(trans('messages.reservation.success.created'), $reservation);
        } catch (\Exception $e) {
            DB::rollback();

            return $this->responseError($e->getMessage());
        }
    }

    /**
     * Booking Rejection - Driver Action.
     */
    public function rejectReservation($id)
    {
        $reservation = $this->reservation->find($id);
        if (! $reservation) {
            return $this->responseError(trans('messages.reservation.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        if (! $this->isAllowedAcceptorReject($reservation)) {
            return $this->responseError($this->message_err);
        }

        // Reject booking
        $reservation->update(['status' => Reservation::STATUS_REJECTED]);

        // Update remaining_seats
        $this->updateRemainingSeats($reservation);

        // Send Firebase notifications
        $sendMessageNotification = new SendFirebaseNotification(
            $reservation->user_id,
            self::ACTION_TYPE_REJECT_BOOKING,
            $this->makeRejectNotiPayload($reservation),
        );
        dispatch($sendMessageNotification)->onConnection('sync');

        return $this->responseSuccess(trans('messages.reservation.success.rejected'), $reservation);
    }

    /**
     * Booking Rejection - Driver Action.
     */
    public function rejectAfterAcceptReservation($id, $request)
    {
        try {
            DB::beginTransaction();

            $reservation = $this->reservation->find($id);
            if (! $reservation) {
                return $this->responseError(trans('messages.reservation.error.not_found'), Response::HTTP_NOT_FOUND);
            }

            if (! $this->isAllowedRejectAfterAccept($reservation)) {
                return $this->responseError($this->message_err);
            }

            // Cancel booking
            $reservation->update([
                'status' => Reservation::STATUS_REJECTED_AFTER_ACCEPTED,
                'cancel_reason' => $request['cancel_reason'],
            ]);

            // Update remaining_seats
            $this->updateRemainingSeats($reservation);

            // Increase cancel count
            $this->increaseCancelCount();

            // Send Firebase notifications
            $sendMessageNotification = new SendFirebaseNotification(
                $reservation->user_id,
                self::ACTION_TYPE_REJECT_AFTER_ACCEPTED_BOOKING,
                $this->makeRejectAfterAceptNotiPayload($reservation),
            );
            dispatch($sendMessageNotification)->onConnection('sync');

            dispatch(new SendBookingCancelEmail($reservation));

            DB::commit();

            return $this->responseSuccess(trans('messages.reservation.success.cancelled'), $reservation);
        } catch (\Exception $e) {
            DB::rollback();

            return $this->responseError($e->getMessage());
        }
    }

    /**
     * Calculate the booking accept deadline.
     */
    private function calculateAcceptDeadline($trip)
    {
        $now = now();
        $bookingDeadline = $this->calculateBookingDeadline($trip);

        // When passengers book trips close to departure time (< 3 hours does not apply time logic)
        if ($now->diffInHours($bookingDeadline, false) < 3) {
            return $bookingDeadline;
        }

        $currentHour = (int) $now->format('H');
        if ($currentHour >= 5 && $currentHour <= 19) {
            // From 05:00 to 19:59 -> +3 hours
            return $now->addHours(3);
        } else {
            // From 20:00 to 04:59 -> +12 hours
            return $now->addHours(12);
        }
    }

    /**
     * Calculate the trip price.
     */
    private function calculateTripPrice($request, $price)
    {
        return round($request->number_of_seats * $price, 2, PHP_ROUND_HALF_UP);
    }

    /**
     * Store the reservation.
     */
    private function create($request)
    {
        return $this->reservation->create(
            $request->only([
                'user_id',
                'trip_id',
                'number_of_seats',
                'payment_method',
                'actual_amount_paid',
                'status',
                'accept_deadline',
            ])
        );
    }

    /**
     * Format the booked trip.
     */
    private function formatReservations(&$trips)
    {
        return $trips->transform(function ($trip) {
            $trip->status_text = trans('messages.reservation.status.'.$trip->status);

            return $trip;
        });
    }

    /**
     * Get the trip history list.
     */
    private function getReservationsData($status)
    {
        return Reservation::query()
            ->select(
                'reservations.id as booking_id',
                'reservations.user_id',
                'reservations.trip_id',
                'reservations.status',
                'reservations.number_of_seats',
                'reservations.created_at',
                'reservations.accept_deadline',
            )
            ->with([
                'trip' => function ($query) {
                    $query->select(
                        'trips.id',
                        'trips.user_id',
                        'trips.vehicle_id',
                        'trips.departure_address',
                        'trips.destination_address',
                        'trips.departure_date',
                        // 'trips.departure_time',
                        DB::raw("TIME_FORMAT(trips.departure_time, '%H:%i') AS departure_time"),
                        'trips.destination_city',
                        'trips.departure_city',
                        'trips.completion_time',
                        'trips.price',
                        'trips.status',
                    )
                        ->with([
                            'driver' => function ($q) {
                                $q->select('id', 'full_name', 'avatar_url', 'is_avatar_verified', 'is_ID_card_verified', 'bio', 'birthday', 'preference', 'gender', 'email_verified_at', 'phone_verified_at')
                                    ->withCount('reviewGiven as rating_count')
                                    ->withAvg('reviewReceived as rating_avg', 'rating');
                            },
                            'vehicle:id,type',
                        ]);
                },
            ])
            ->whereHas('payment_paid')
            ->where('reservations.user_id', Auth::id())
            ->when($status, function ($query) use ($status) {
                return $query->where('reservations.status', $status);
            })
            ->orderByDesc('reservations.created_at')
            ->paginate(self::PER_PAGE);
    }

    /**
     * Get the remaining seats.
     */
    private function getRemainingSeats($request, $trip)
    {
        $numberOfSeatsReserved = (int) $request->number_of_seats;

        return $trip->remaining_seats - $numberOfSeatsReserved;
    }

    /**
     * Check and return the status for trip.
     */
    private function getStatusBaseTrip($trip)
    {
        // if ($trip->auto_accept) {
        //     return Reservation::STATUS_ACCEPTED;
        // }

        // return Reservation::STATUS_PENDING_CONFIRM;
        // if ($trip->auto_accept) {
        //     return Reservation::STATUS_PAID;
        // }
        return Reservation::STATUS_WAITING;
    }

    /**
     * Verify if the booking allows the driver to accept or reject.
     */
    private function isAllowedAcceptorReject($reservation)
    {
        // if ($reservation->trip->user_id != Auth::id()) {
        //     $this->message_err = trans('messages.reservation.error.owner_trip');

        //     return false;
        // }

        if ($reservation->status != Reservation::STATUS_PENDING_CONFIRM) {
            $this->message_err = trans('messages.reservation.error.not_allowed');

            return false;
        }

        return true;
    }

    /**
     * Verify if the booking allows the driver to reject.
     */
    private function isAllowedRejectAfterAccept($reservation)
    {
        if ($reservation->trip->user_id != Auth::id()) {
            $this->message_err = trans('messages.reservation.error.owner_trip');

            return false;
        }

        if ($reservation->status != Reservation::STATUS_ACCEPTED && $this->calculateBookingDeadline($reservation->trip)->lessThan(now())) {
            $this->message_err = trans('messages.reservation.error.not_allowed');

            return false;
        }

        return true;
    }

    /**
     * Validate if the booking allows the user to cancel.
     */
    private function isAllowedCancel($reservation)
    {
        if ($reservation->user_id != Auth::id()) {
            $this->message_err = trans('messages.reservation.error.owner_reservation');

            return false;
        }

        $trip = $reservation->trip;
        if ($trip->status !== Trip::STATUS_OPEN) {
            $this->message_err = trans('messages.reservation.error.not_allowed');

            return false;
        } elseif ($this->calculateBookingDeadline($trip)->lessThan(now())) {
            $this->message_err = trans('messages.reservation.error.not_allowed');

            return false;
        }

        return true;
    }

    /**
     * Verify that the reservation allows automatic cancellation.
     */
    private function isAllowedCancelAuto($reservation)
    {
        if ($reservation->status !== Reservation::STATUS_PENDING_CONFIRM) {
            $this->message_err = trans('messages.reservation.error.not_allowed');

            return false;
        }

        return true;
    }

    /**
     * Is valid for reservation.
     */
    private function isValidForReservation($trip, $request)
    {
        // Cannot book your own trip
        if ($trip->user_id == Auth::id()) {
            $this->message_err = trans('messages.reservation.error.create.owner');

            return false;
        }

        // Trip is finished or cancel
        if ($trip->status != Trip::STATUS_OPEN) {
            $this->message_err = trans('messages.trip.error.status.'.$trip->status);

            return false;
        }

        // The user has not verified their phone number
        $user = Auth::user();
        if (! $user->phone_verified_at) {
            $this->message_err = trans('messages.reservation.error.create.phone_verified_at');

            return false;
        }

        // Booking Deadline Expired - Departed
        if ($this->calculateBookingDeadline($trip)->lessThan(now())) {
            $this->message_err = trans('messages.reservation.error.create.booking_deadline');

            return false;
        }

        // The trip has available seats
        if ($trip->remaining_seats < $request->number_of_seats) {
            $this->message_err = trans('messages.reservation.error.create.not_available');

            return false;
        }

        return true;
    }

    /**
     *  Generates the notification payload for a booking.
     */
    private function makeBookingNotiPayload($reservation)
    {
        return [
            [
                'title' => 'Đặt chỗ thành công',
                'body' => 'Đặt chỗ thành công. Chuyến đi từ '.$reservation->trip->departure_city.' đến '.$reservation->trip->destination_city,
                'type' => self::ACTION_TYPE_CREATE_BOOKING,
                'user_id' => $reservation->user_id,
                'trip_id' => $reservation->trip_id,
                'reservation_id' => $reservation->id,
                'sound' => config('firebase.sound'),
            ],
            [
                'title' => 'Có hành khách đã đặt chỗ',
                'body' => 'Có hành khách đã đặt chỗ. Chuyến đi từ '.$reservation->trip->departure_city.' đến '.$reservation->trip->destination_city,
                'type' => self::ACTION_TYPE_CREATE_BOOKING,
                'user_id' => $reservation->trip->user_id,
                'trip_id' => $reservation->trip_id,
                'reservation_id' => $reservation->id,
                'sound' => config('firebase.sound'),
            ],
        ];
    }

    /**
     *  Generates the notification payload for a cancelled reservation.
     */
    private function makeCancelNotiPayload($reservation)
    {
        return [
            'title' => 'Yêu cầu đặt xe bị hủy',
            'body' => 'Chuyến đi của bạn đã bị hủy do quá hạn chấp nhận. Chuyến đi từ '.$reservation->trip->departure_city.' đến '.$reservation->trip->destination_city,
            'type' => self::ACTION_TYPE_CANCEL_BOOKING,
            'user_id' => $reservation->user_id,
            'trip_id' => $reservation->trip_id,
            'reservation_id' => $reservation->id,
            'sound' => config('firebase.sound'),
        ];
    }

    /**
     *  Generates the notification payload for a automatically cancelled reservation.
     */
    private function makeCancelAutoNotiPayload($reservation)
    {
        return [
            'title' => 'Quá thời gian chấp nhận yêu cầu đặt xe',
            'body' => 'Chuyến đi của bạn đã bị hủy do quá hạn chấp nhận. Chuyến đi từ '.$reservation->trip->departure_city.' đến '.$reservation->trip->destination_city,
            'type' => self::ACTION_TYPE_CANCEL_BOOKING,
            'user_id' => $reservation->user_id,
            'trip_id' => $reservation->trip_id,
            'reservation_id' => $reservation->id,
            'sound' => config('firebase.sound'),
        ];
    }

    /**
     *  Generates the notification payload for a rejected reservation.
     */
    private function makeRejectNotiPayload($reservation)
    {
        return [
            'title' => 'Yêu cầu đặt xe không được chấp nhận',
            'body' => 'Chuyến đi từ '.$reservation->trip->departure_city.' đến '.$reservation->trip->destination_city.'. Đã từ chối đặt chuyến của hành khách '.$reservation->user->full_name,
            'type' => self::ACTION_TYPE_REJECT_BOOKING,
            'user_id' => $reservation->trip->user_id,
            'trip_id' => $reservation->trip_id,
            'reservation_id' => $reservation->id,
            'sound' => config('firebase.sound'),
        ];
    }

     /**
     *  Generates the notification payload for a rejected reservation.
     */
    private function makeRejectAfterAceptNotiPayload($reservation)
    {
        return [
            'title' => 'Chuyến xe đã bị huỷ bởi tài xế',
            'body' => 'Chuyến đi từ '.$reservation->trip->departure_city.' đến '.$reservation->trip->destination_city.'. Đã từ chối đặt chuyến của hành khách '.$reservation->user->full_name,
            'type' => self::ACTION_TYPE_REJECT_AFTER_ACCEPTED_BOOKING,
            'user_id' => $reservation->trip->user_id,
            'trip_id' => $reservation->trip_id,
            'reservation_id' => $reservation->id,
            'sound' => config('firebase.sound'),
        ];
    }

    /**
     * Update remaining_seats after cancel or reject booking
     */
    private function updateRemainingSeats($reservation)
    {
        if (! $reservation->trip) {
            return;
        }

        $reservation->trip->increment('remaining_seats', $reservation->number_of_seats);
    }
}
