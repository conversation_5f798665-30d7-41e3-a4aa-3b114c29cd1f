<?php

namespace App\Services;

use App\Jobs\SendMessageNotification;
use App\Models\Message;
use App\Models\RoomChat;
use App\Models\Trip;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class ChatService extends BaseService
{
    /**
     * Get rooms
     *
     * @param  mixed  $request
     * @return void
     */
    public function getRooms($request = [])
    {
        $roomChats = RoomChat::with(
            'trip',
            'user_driver:id,full_name,gender,avatar_url',
            'user_passenger:id,full_name,gender,avatar_url',
            'last_message'
        )
            ->withCount('messagesUnread')
            ->whereHas('trip', function ($q) {
                $q->where('trips.status', Trip::STATUS_OPEN);
            })
            ->where(function ($q) {
                $q->where('user_id_driver', Auth::user()->id)
                    ->orWhere('user_id_passenger', Auth::user()->id);
            });

        if (isset($request['room_id']) && $request['room_id']) {
            return $roomChats->where('id', $request['room_id'])->first();
        } else {
            return $roomChats->orderBy('updated_at', 'desc')->paginate(self::PER_PAGE);
        }
    }

    /**
     * Store room
     *
     * @param  mixed  $request
     * @return void
     */
    public function storeRoom($request)
    {
        if (Auth::user()->id == $request['user_id_driver'] || $request['user_id_passenger'] == Auth::user()->id) {
            $roomChat = RoomChat::updateOrCreate([
                'trip_id' => $request['trip_id'],
                'user_id_driver' => $request['user_id_driver'],
                'user_id_passenger' => $request['user_id_passenger'],
            ]);

            return RoomChat::with(
                'trip',
                'user_driver:id,full_name,gender,avatar_url',
                'user_passenger:id,full_name,gender,avatar_url',
                'last_message'
            )->find($roomChat->id);
        } else {
            abort(403);
        }
    }

    /**
     * Store message
     *
     * @param  mixed  $request
     * @return void
     */
    public function storeMessage($request)
    {
        $room = RoomChat::where([
            'id' => $request['room_chat_id'],
        ])->where(function ($q) {
            $q->where('user_id_driver', Auth::user()->id)
                ->orWhere('user_id_passenger', Auth::user()->id);
        })->first();
        if (! $room) {
            abort(403);
        }
        $user_send = Auth::user()->id;
        $user_receiver = '';
        if ($user_send != $room->user_id_driver) {
            $user_receiver = $room->user_id_driver;
        } else {
            $user_receiver = $room->user_id_passenger;
        }

        if ($user_send && $user_receiver) {
            $message = Message::create([
                'room_chat_id' => $request['room_chat_id'],
                'messages' => $request['messages'],
                'user_send' => $user_send,
                'user_receiver' => $user_receiver,
                'type' => $this->checkRuleMessage($request['messages']),
            ]);
            $room->updated_at = Carbon::now();
            $room->save();
            if ($message->type == Message::MESSAGE_TYPE_CHECK) {
                $sendMessageNotification = new SendMessageNotification(
                    $user_send,
                    $user_receiver,
                    $message->id,
                    $request['messages'],
                    $request['room_chat_id'],
                    $request['message_identify'] ?? '',
                );
                dispatch($sendMessageNotification)->onConnection('sync');
            }

            return Message::with('send:id,full_name,gender,avatar_url', 'receiver:id,full_name,gender,avatar_url')->find($message->id);
        } else {
            abort(403);
        }
    }

    /**
     * Check rule message
     *
     * @param  mixed  $message
     * @return void
     */
    public function checkRuleMessage($messages)
    {
        $cleanedPhoneNumber = preg_replace('/\D/', '', $messages);
        $patternPhone = '/(0[1-9][0-9]{8}|84[1-9][0-9]{7})/';
        $patternEmail = '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/';
        $patternLink1 = '/\b(?:https?|ftp):\/\/[^\s]+\b/';
        $patternLink2 = '/(?:^|\s)([a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,})(?:$|\s)/';
        $checkPhone1 = preg_match($patternPhone, $cleanedPhoneNumber);
        $checkPhone2 = preg_match($patternPhone, $messages);
        $checkEmail = preg_match($patternEmail, $messages);
        $patternLink1 = preg_match($patternLink1, $messages);
        $patternLink2 = preg_match($patternLink2, $messages);
        $checkBadWord = $this->containsProfanity($messages);
        if (
            $checkPhone1 ||
            $checkPhone2 ||
            $checkEmail ||
            $patternLink1 ||
            $patternLink2 ||
            $checkBadWord
        ) {
            return 0;
        }

        return 1;
    }

    /**
     * Check if the text contains profanity
     *
     * @param  mixed  $text
     * @return bool
     */
    public function containsProfanity($text)
    {
        $profanities = trans('bad_words');

        $text = mb_strtolower($text, 'UTF-8');

        foreach ($profanities as $profanity) {
            $profanity = mb_strtolower($profanity, 'UTF-8');
            if (strpos($text, $profanity) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get messages
     *
     * @param  mixed  $id
     * @return void
     */
    public function getMessages($id, $request = [])
    {
        $messages = Message::with('send:id,full_name,gender,avatar_url', 'receiver:id,full_name,gender,avatar_url')
            ->where('room_chat_id', $id)
            ->where('created_at', '>=', Carbon::now()->subDay(1))
            ->where(function ($q) {
                $q->where('user_send', Auth::user()->id)
                    ->orWhere(function ($q1) {
                        $q1->where('user_receiver', Auth::user()->id);
                        $q1->where('type', Message::MESSAGE_TYPE_CHECK);
                    });
            });
        if (isset($request['message_id']) && $request['message_id']) {
            return $messages->where('id', $request['message_id'])->first();
        } else {
            return $messages->orderBy('id', 'desc')->paginate(self::PER_PAGE);
        }
    }

    /**
     * Update messages
     *
     * @param  mixed  $id
     * @return void
     */
    public function updateMessage($id)
    {
        return Message::where('id', $id)
            ->where('message_status', 0)
            ->where(function ($q) {
                $q->where('user_send', Auth::user()->id)
                    ->orWhere('user_receiver', Auth::user()->id);
            })
            ->update([
                'message_status' => 1,
            ]);
    }

    /**
     * Update messages unread
     *
     * @param  mixed  $id
     * @return void
     */
    public function updateUnreadMessage($id)
    {
        return Message::where('room_chat_id', $id)
            ->where('message_status', 0)
            ->where('user_receiver', Auth::user()->id)
            ->update([
                'message_status' => 1,
            ]);
    }

    public function getCountMessageUnread()
    {
        return Message::where('message_status', 0)
            ->where('user_receiver', Auth::user()->id)
            ->count();
    }
}
