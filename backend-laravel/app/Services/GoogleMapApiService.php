<?php

namespace App\Services;

use App\Helpers\Format;
use Illuminate\Support\Facades\Http;

class GoogleMapApiService extends BaseService
{
    protected $requiredParams;

    public function __construct()
    {
        $this->requiredParams = [
            'key' => config('google_api.key'),
            'components' => 'country:VN',
            'language' => 'vi',
        ];
    }

    /**
     * Calculate distance
     *
     * @param  mixed  $origin
     * @param  mixed  $destination
     * @param  mixed  $type_vehicle
     * @param  bool  $stop_point
     * @return array
     */
    public function calculateDistance($origin, $destination, $type_vehicle, $stop_point = false)
    {
        $data = [];
        $params = [
            'origin' => $origin,
            'destination' => $destination,
            'alternatives' => true,
        ];
        $directions = $this->directions($params);

        if (isset($directions['routes'][0]['legs'][0]['distance']['value'])) {
            $distance = $directions['routes'][0]['legs'][0]['distance']['value'];
            $duration = $directions['routes'][0]['legs'][0]['duration']['value'] ?? [];
            $start_address = $directions['routes'][0]['legs'][0]['start_address'] ?? '';
            $end_address = $directions['routes'][0]['legs'][0]['end_address'] ?? '';
            $type_vehicle = $type_vehicle;
            $price = config('price')[$type_vehicle];
            $distance = $distance / 1000;
            if ($distance <= $price['distance_min']) {
                $data['price_max'] = $price['price_min'];
                $data['price_avg'] = $price['price_min'];
                $data['price_suggest_from'] = $price['price_min'];
                $data['price_suggest_to'] = $price['price_min'];
                $data['price_min'] = $price['price_min'];
            } else {
                $data['price_max'] = Format::formatVnd($price['price_min'] + ($distance - $price['distance_min']) * $price['price_avg']);

                $price_avg = Format::formatVnd($data['price_max'] / 2);
                $data['price_avg'] = $price_avg > $price['price_min'] ? $price_avg : $price['price_min'];

                $amplitude_suggest = ($data['price_avg'] < $price['price_suggest_max']) ? $price['amplitude_suggest_min'] : $price['amplitude_suggest_max'];
                $price_suggest_from = $data['price_avg'] - $amplitude_suggest;
                $data['price_suggest_from'] = $price_suggest_from > $price['price_min'] ? $price_suggest_from : $price['price_min'];
                $data['price_suggest_to'] = $data['price_avg'] + $amplitude_suggest;

                $price_min = Format::formatVnd($data['price_max'] / 3);
                $data['price_min'] = $price_min > $price['price_min'] ? $price_min : $price['price_min'];
            }
            $data['percent_service_and_vat'] = $price['percent_service_and_vat'];
            $data['distance'] = $distance;
            $data['duration'] = $duration;
            $data['start_address'] = $start_address;
            $data['start_location'] = $origin;
            $data['end_address'] = $end_address;
            $data['end_location'] = $destination;
        }

        return $data;
    }

    /**
     * Convert lat, lng to address.
     */
    public function distanceMatrix($request)
    {
        $apiPath = config('google_api.distancematrix');
        $params = array_merge($request, $this->requiredParams);

        return Http::get($apiPath, $params)->json();
    }

    /**
     * Get directions location
     */
    public function directions($request)
    {
        $apiPath = config('google_api.directions');
        $params = array_merge($request, $this->requiredParams);

        return Http::get($apiPath, $params)->json();
    }

    /**
     * Convert lat, lng to address.
     */
    public function geoCode($request)
    {
        $apiPath = config('google_api.geocode');
        $params = array_merge($request, $this->requiredParams);

        return Http::get($apiPath, $params)->json();
    }

    /**
     * Get price distance
     *
     * @param  mixed  $request
     * @return array
     */
    public function getPriceDistance($request)
    {
        $prices = [];
        $origin = $request['origin'];
        $destination = $request['destination'];
        $type_vehicle = $request['type_vehicle'] ?? 1;
        if (isset($request['stop_points']) && count($request['stop_points']) > 0) {
            foreach ($request['stop_points'] as $stop_point) {
                $prices[] = $this->calculateDistance($origin, $stop_point, $type_vehicle, true);
                $origin = $stop_point;
            }
            $prices[] = $this->calculateDistance($origin, $destination, $type_vehicle, true);
            $prices[] = [
                'price_max' => (int) array_sum(array_column($prices, 'price_max')),
                'price_avg' => (int) array_sum(array_column($prices, 'price_avg')),
                'price_suggest_from' => (int) array_sum(array_column($prices, 'price_suggest_from')),
                'price_suggest_to' => (int) array_sum(array_column($prices, 'price_suggest_to')),
                'price_min' => (int) array_sum(array_column($prices, 'price_min')),
                'percent_service_and_vat' => $prices[0]['percent_service_and_vat'] ?? 0,
                'distance' => array_sum(array_column($prices, 'distance')),
                'start_address' => $prices[0]['start_address'],
                'start_location' => $request['origin'],
                'end_address' => $prices[count($prices) - 1]['end_address'],
                'end_location' => $destination,
            ];
        } else {
            $prices[] = $this->calculateDistance($origin, $destination, $type_vehicle);
        }

        return $prices;
    }

    /**
     * Place Autocomplete requests
     */
    public function placeAutoComplete($request)
    {
        $apiPath = config('google_api.place_autocomplete');
        $params = array_merge($request, $this->requiredParams);

        return Http::get($apiPath, $params)->json();
    }

    /**
     * Convert lat, lng to address.
     */
    public function placeDetails($request)
    {
        $apiPath = config('google_api.place_details');
        $params = array_merge($request, $this->requiredParams);
        $params['fields'] = 'name,geometry,address_components,formatted_address';

        return Http::get($apiPath, $params)->json();
    }

    /**
     * Create base params for Place API (NEW)
     * API: https://places.googleapis.com/v1/places:autocomplete
     * Method: POST
     * Documentation: https://developers.google.com/maps/documentation/places/web-service/place-autocomplete
     */
    // private function createBaseParams(): array
    // {
    //     return [
    //         'key' => config('google_api.key'),
    //         'languageCode' => 'vi',
    //         'sessionToken' => session()->getId(),
    //         'locationBias' => [
    //             'circle' => [
    //                 'center' => ['latitude' => 14.0583, 'longitude' => 108.2772],
    //                 'radius' => 500000, // Radius in meters (500km)
    //             ],
    //         ],
    //     ];
    // }
}
