<?php

namespace App\Services;

use App\Jobs\SendBookingCancelEmail;
use App\Jobs\SendFirebaseNotification;
use App\Models\Reservation;
use App\Models\Trip;
use App\Models\Vehicle;
use App\Services\Helpers\TripCreateService;
use App\Services\Helpers\TripDetailService;
use App\Services\Helpers\TripSearchService;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TripService extends BaseService
{
    protected $trip;

    protected $tripSearchService;

    protected $tripCreateService;

    protected $tripDetailService;

    protected $message_err;

    const MULTIPLE_TRIP = 3;

    const SINGLE_TRIP = 1;

    const ACTION_TYPE_UPDATE_TRIP = 'update_trip';

    public function __construct(
        TripCreateService $tripCreateService,
        TripSearchService $tripSearchService,
        TripDetailService $tripDetailService
    ) {
        $this->tripCreateService = $tripCreateService;
        $this->tripSearchService = $tripSearchService;
        $this->tripDetailService = $tripDetailService;
        $this->message_err = '';
    }

    /**
     * Cancel trip.
     */
    public function cancelTrip($id, $request)
    {
        try {
            DB::beginTransaction();

            $trip = Trip::where('id', $id)
                ->where('user_id', Auth::id())
                ->firstOrFail();

            if (! $this->isValidForCancelTrip($trip)) {
                return $this->responseError($this->message_err);
            }

            $reservations = collect();
            // Cancel trip child
            if ($trip->is_parent_route) {
                $tripChilds = Trip::where('route_id', $trip->route_id)->where('user_id', Auth::id())->get();
                foreach ($tripChilds as $tripChild) {
                    if ($tripChild->reservations) {
                        $reservations = $reservations->merge($tripChild->reservations);
                    }
                    $tripChild->status = Trip::STATUS_CANCELLED;
                    $tripChild->cancel_reason = $request->cancel_reason;
                    $tripChild->save();
                    // $tripChild->update(['status' => Trip::STATUS_CANCELLED, 'cancel_reason' => $request->cancel_reason]);
                }
            }

            if ($trip->reservations) {
                $reservations = $reservations->merge($trip->reservations);
            }
            // Cancel trip
            $trip->update(['status' => Trip::STATUS_CANCELLED, 'cancel_reason' => $request->cancel_reason]);

            // Increase cancel count
            $this->increaseCancelCount();

            // Cancel reservations
            if ($reservations) {
                foreach ($reservations as $reservation) {
                    if ($reservation->status == Reservation::STATUS_ACCEPTED) {
                        $reservation->status = Reservation::STATUS_REJECTED_AFTER_ACCEPTED;
                        $type = ReservationService::ACTION_TYPE_CANCEL_BOOKING;
                    } else {
                        $reservation->status = Reservation::STATUS_CANCELLED;
                        $type = ReservationService::ACTION_TYPE_REJECT_AFTER_ACCEPTED_BOOKING;
                    }
                    $reservation->cancel_reason = $request->cancel_reason;
                    $reservation->save();
                    $sendMessageNotification = new SendFirebaseNotification(
                        $reservation->user_id,
                        $type,
                        [
                            'title' => trans('messages.trip.notification.cancelled'),
                            'body' => 'Chuyến đi từ ' . $trip->departure_address . ' đến ' . $trip->destination_address . ' đã bị hủy do lý do: ' . $trip->cancel_reason,
                            'type' => $type,
                            'trip_id' => $reservation->trip_id,
                            'reservation_id' => $reservation->id,
                            'sound' => config('firebase.sound'),
                        ],
                    );
                    dispatch($sendMessageNotification)->onConnection('sync');

                    dispatch(new SendBookingCancelEmail($reservation));
                }
            }
            DB::commit();

            return $this->responseSuccess(trans('messages.trip.success.cancelled'), $trip);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->responseError(trans('messages.trip.error.cancel.default'));
        }
    }

    /**
     * Create a trip.
     */
    public function create($request, $routeId = null)
    {
        // Validation before create trips
        if (! $this->isTripCreatable($request)) {
            return $this->responseError($this->message_err);
        }

        DB::beginTransaction();
        try {
            // Generate a trip list
            $userId = Auth::id();
            $routeId = $routeId ?: $this->tripCreateService->generateRouteId($userId);
            $tripList = $this->tripCreateService->generateTripList($request, $userId, $routeId);

            // Storage trips to database
            $this->tripCreateService->saveTrips($tripList);
            DB::commit();

            // Retrieve trips from the database based on route id
            $data = $this->tripCreateService->getTripsByRouteId($routeId);

            $message = $routeId ? trans('messages.trip.success.create') : trans('messages.trip.success.update');

            return $this->responseSuccess($message, $data);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->responseError($e->getMessage());
        }
    }

    /**
     * Get the created trip list.
     */
    public function getOwnTrips()
    {
        $trips = $this->getOwnTripData();
        $this->formatOwnTrip($trips);

        return $this->responseSuccess(trans('messages.trip.success.created'), $trips);
    }

    /**
     * Get similar trips.
     */
    public function getSimilarTrips($request)
    {
        $query = $this->tripSearchService->getBaseSearchTripQuery($request);
        $trips = $this->tripSearchService->filterSimilarTrip($query, $request);
        $trips = $this->tripSearchService->formatTripList($trips);

        return $this->responseSuccess('', $trips);
    }

    /**
     * Get trip detail.
     */
    public function getTripDetail($tripId, $request, $userId = null)
    {
        $trip = Trip::find($tripId);
        if (! $trip) {
            return $this->responseError(trans('messages.trip.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        // update remaining_seats if necessary
        $this->refreshTripAndReservation($trip);

        // Get trip detail information
        $tripDetail = $this->tripDetailService->getTripDetailData($tripId, $userId);

        // Format the trip data
        $this->tripDetailService->formatTripDetail($tripDetail, $request);

        return $this->responseSuccess(trans('messages.trip.success.get'), $tripDetail);
    }

    /**
     * Mark trip completed.
     */
    public function markTripCompleted($tripId)
    {
        $trip = Trip::find($tripId);
        if (! $trip) {
            return $this->responseError(trans('messages.trip.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        if (! $this->isValidMarkTripCompleted($trip)) {
            return $this->responseError($this->message_err);
        }

        DB::beginTransaction();
        try {
            $reservations = $trip->reservations();
            $reservations->each(function ($reservation) {
                $reservation->update(['status' => Reservation::STATUS_FINISHED]);

                $sendMessageNotification = new SendFirebaseNotification(
                    $reservation->user_id,
                    'completed_trip',
                    [
                        'title' => 'chuyến đi của bạn đã hoàn thành.',
                        'body' => 'Bạn có thể để lại đánh giá cho trải nghiệm của chuyến đi này. Chuyến đi từ ' . $reservation->trip->departure_city . ' đến ' . $reservation->trip->destination_city,
                        'type' => 'completed_trip',
                        'trip_id' => $reservation->trip_id,
                        'reservation_id' => $reservation->id,
                        'sound' => config('firebase.sound'),
                    ],
                );
                dispatch($sendMessageNotification)->onConnection('sync');
            });

            $trip->update(['status' => Trip::STATUS_FINISHED]);
            DB::commit();

            $data = $this->getTripDetail($tripId, [])['data'];

            return $this->responseSuccess(trans('messages.trip.success.mark_completed'), $data);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->responseError(trans('messages.error.undefined'));
        }
    }

    /**
     * Search trips.
     */
    public function searchTrips($request)
    {
        // Get list of trips
        $searchParams = $request->get('cond');
        $query = $this->tripSearchService->getBaseSearchTripQuery($searchParams);

        $filterConditionsCount = $this->tripSearchService->getFilterConditionsCount($query, $searchParams);
        $carTripCount = $this->tripSearchService->getVehicleTripCount($query, Vehicle::TYPE_CAR);
        $motorTripCount = $this->tripSearchService->getVehicleTripCount($query, Vehicle::TYPE_MOTOR);

        $query = $this->tripSearchService->filterVehicleType($query, $searchParams);
        $query = $this->tripSearchService->filterSearchConditions($query, $searchParams);

        $data = [
            'trip_list' => $this->tripSearchService->getTripListData($query, $request),
            'total_trips' => $this->tripSearchService->getTotalTrip($query),
            'vehicle_count' => [
                'car' => $carTripCount,
                'motor' => $motorTripCount,
            ],
            'filter_conditions_list' => $filterConditionsCount,
        ];

        return $this->responseSuccess('', $data);
    }

    /**
     * Update a trip for user and store it in the database.
     */
    public function updateTrip($tripId, $request)
    {
        DB::beginTransaction();
        try {
            $trip = Trip::find($tripId);
            if (! $trip) {
                return $this->responseError(trans('messages.trip.error.not_found'), Response::HTTP_NOT_FOUND);
            }

            // Validation before Update trips
            if (! $this->isTripUpdatable($trip, $request)) {
                return $this->responseError($this->message_err);
            }

            // Get the update type based on the reservation status
            $updateType = $this->tripDetailService->getUpdateType($trip->route_id);
            if ($updateType == Trip::UPDATE_TYPE_UNDEFINED) {
                return $this->responseError(trans('messages.trip.error.update.undefined'));
            }

            // Get updatable fields and save to the database
            $updatableFields = $this->getUpdatableFields($trip, $updateType, $request);

            if ($updateType == Trip::UPDATE_TYPE_ALL) {
                $this->updateTrips($trip, $updatableFields, $request);
            } else {
                $this->update($trip, $updatableFields, $request);
            }

            DB::commit();

            if ($updateType == Trip::UPDATE_TYPE_ACCEPTED) {
                $reservations = $trip->reservations()
                    ->where('status', Reservation::STATUS_ACCEPTED)
                    ->get();

                foreach ($reservations as $reservation) {
                    $updateTripNotiPayload = $this->makeUpdateTripNotiPayload($trip, $reservation);
                    $this->sendFirebaseNotification($updateTripNotiPayload);
                }
            }

            return $this->responseSuccess(trans('messages.trip.success.update'), $trip);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->responseError($e->getMessage());
        }
    }

    /**
     * Check the number of parameters in the valid array.
     */
    private function isValidNumberOfParamInArray($request)
    {
        $points = $request->get('points');
        if (is_null($points) || ! is_array($points)) {
            return false;
        }

        $totalPoint = count($points);
        $numberOfParamsInArray = $totalPoint >= self::MULTIPLE_TRIP ? $totalPoint : self::SINGLE_TRIP;

        $keys = ['prices', 'completion_times', 'overview_polyline', 'distances'];
        foreach ($keys as $key) {
            $array = $request->get($key);
            if (count($array) != $numberOfParamsInArray) {
                return false;
            }
        }

        return true;
    }

    /**
     * Format the created trip.
     */
    private function formatOwnTrip(&$trips)
    {
        return $trips->transform(function ($trip) {
            $trip->status_text = trans('messages.trip.status.' . $trip->status);

            return $trip;
        });
    }

    /**
     * Get the created trip list.
     */
    private function getOwnTripData()
    {
        return Trip::query()
            ->select(
                'trips.id',
                'trips.user_id',
                'trips.vehicle_id',
                'trips.departure_address',
                'trips.destination_address',
                'trips.departure_city',
                'trips.destination_city',
                'trips.departure_date',
                // 'trips.departure_time',
                DB::raw("TIME_FORMAT(trips.departure_time, '%H:%i') AS departure_time"),
                'trips.completion_time',
                'trips.price',
                'trips.status',
                'trips.created_at'
            )
            ->with([
                'driver' => fn($q) => $q
                    ->select(
                        'id',
                        'full_name',
                        'avatar_url',
                        'is_avatar_verified',
                        'is_ID_card_verified',
                        'bio',
                        'birthday',
                        'preference',
                        'gender',
                        'email_verified_at',
                        'phone_verified_at'
                    )
                    ->withCount('reviewGiven as rating_count')
                    ->withAvg('reviewReceived as rating_avg', 'rating'),
                'vehicle:id,type',
                'reservations' => fn($q) => $q
                    ->select('id', 'user_id', 'trip_id', 'status', 'created_at')
                    ->with('user:id,full_name')
                    ->wherePending(), // app/Models/Reservation.php > scopeWherePending
            ])
            ->where('trips.user_id', Auth::id())
            ->orderByDesc('trips.departure_date')
            ->orderBy('trips.departure_time')
            ->paginate(self::PER_PAGE);
    }

    /**
     * Get remaining seat to update.
     */
    private function getRemainingSeat($request, $trip)
    {
        $vehicle = $trip->vehicle()->first();
        if ($vehicle && $vehicle->type == Vehicle::TYPE_MOTOR) {
            return $trip->remaining_seats;
        }

        return $request->get('max_seat') - $trip->max_seat + $trip->remaining_seats;
    }

    /**
     * Determine the updatable fields based on reservation status
     */
    private function getUpdatableFields($trip, $updateType, $request)
    {
        // Calculate remaining seats
        if (! is_null($request->get('max_seat'))) {
            $request->merge(['remaining_seats' => $this->getRemainingSeat($request, $trip)]);
        }

        // Determine updatable fields based on reservation status
        switch ($updateType) {
            case Trip::UPDATE_TYPE_ALL:
                $updatableFields = ['points', 'prices', 'auto_accept', 'max_seat', 'remaining_seats', 'notes', 'overview_polyline'];
                break;
            case Trip::UPDATE_TYPE_ACCEPTED:
                $updatableFields = ['prices', 'auto_accept', 'max_seat', 'remaining_seats', 'notes'];
                break;
            case Trip::UPDATE_TYPE_PENDING:
                $updatableFields = ['prices'];
                break;

            default:
                $updatableFields = [];
                break;
        }

        return $updatableFields;
    }

    /**
     * Get the update data based on the request.
     */
    private function getUpdateData($updatableFields, $request)
    {
        return collect($updatableFields)
            ->mapWithKeys(function ($field) use ($request) {
                $key = $field === 'prices' ? 'price' : $field;
                $value = $request->get($field);
                $finalValue = is_array($value) ? $value[0] : $value;

                return [$key => $finalValue];
            })
            ->filter(fn($value) => ! is_null($value))
            ->toArray();
    }

    /**
     * Is valid for cancel trip.
     */
    private function isValidForCancelTrip($trip)
    {
        // The trip is not cancelled or finished yet.
        if ($trip->status == Trip::STATUS_CANCELLED || $trip->status == Trip::STATUS_FINISHED) {
            $this->message_err = trans('messages.trip.error.status.' . $trip->status);

            return false;
        }

        return true;
    }

    /**
     * Is valid for mark trip completed.
     */
    private function isValidMarkTripCompleted($trip)
    {
        if ($trip->user_id != Auth::id()) {
            $this->message_err = trans('messages.trip.error.not_owner');

            return false;
        }

        $completionTime = $trip->departure_datetime->addMinutes($trip->completion_time);
        if ($completionTime->greaterThan(now())) {
            $this->message_err = trans('messages.trip.error.completion_time');

            return false;
        }

        return true;
    }

    /**
     * Check for valid seats.
     */
    private function isValidSeat($request, $user)
    {
        return $user->vehicles()
            ->where('id', $request->vehicle_id)
            ->where('number_of_seats', '>=', $request->max_seat)
            ->exists();
    }

    /**
     * Check for valid seat for update.
     */
    private function isValidUpdateSeat($request, $trip)
    {
        if (is_null($request->get('max_seat'))) {
            return true;
        }

        $seatNumberUpdate = $request->get('max_seat');
        $vehicle = $trip->vehicle()->first();
        if ($vehicle && $vehicle->type == Vehicle::TYPE_MOTOR) {
            return $seatNumberUpdate == Vehicle::NUMBER_OF_SEATS_MOTOR;
        }

        return true;
    }

    /**
     * Validate the request before create trips.
     */
    private function isTripCreatable($request)
    {
        $user = Auth::user();

        // Check the user has vehicle.
        if (! $this->isValidVehicle($request, $user)) {
            $this->message_err = trans('messages.trip.error.create.vehicle');

            return false;
        }

        // Check for valid seats
        if (! $this->isValidSeat($request, $user)) {
            $this->message_err = trans('messages.trip.error.create.seat');

            return false;
        }

        // Check the number of parameters in the valid array
        if (! $this->isValidNumberOfParamInArray($request)) {
            $this->message_err = trans('messages.trip.error.create.number_of_params');

            return false;
        }

        // Check the user had verified phone number.
        if (! $user->phone_verified_at) {
            $this->message_err = trans('messages.trip.error.create.phone_verified_at');

            return false;
        }

        return true;
    }

    /**
     * Validate before update trips.
     */
    private function isTripUpdatable($trip, $request)
    {
        if ($trip->status != Trip::STATUS_OPEN) {
            $this->message_err = trans('messages.trip.error.expired');

            return false;
        }

        if ($trip->user_id != Auth::id()) {
            $this->message_err = trans('messages.trip.error.auth');

            return false;
        }

        if (! $this->isValidUpdateSeat($request, $trip)) {
            $this->message_err = trans('messages.trip.error.update.seat');

            return false;
        }

        if ($this->calculateBookingDeadline($trip) <= now()) {
            $this->message_err = trans('messages.trip.error.timeout');

            return false;
        }

        return true;
    }

    /**
     * Check the user has vehicle.
     */
    private function isValidVehicle($request, $user)
    {
        return $user->vehicles()
            ->where('id', $request->get('vehicle_id'))
            ->exists();
    }

    /**
     * Generates the notification payload when update trip.
     */
    private function makeUpdateTripNotiPayload($trip, $reservation)
    {
        return [
            'title' => 'chuyến đi của bạn đã được cập nhật.',
            'body' => 'Chuyến đi từ ' . $trip->departure_city . ' đến ' . $trip->destination_city . ' đã được cập nhật.',
            'type' => self::ACTION_TYPE_UPDATE_TRIP,
            'user_id' => $reservation->user_id,
            'trip_id' => $trip->id,
            'reservation_id' => $reservation->id,
            'sound' => config('firebase.sound'),
        ];
    }

    /**
     * Updates the trip details based on the request data.
     * - If the route remains unchanged, it updates the existing trip.
     * - Otherwise, it deletes the old trips and creates new ones.
     */
    private function updateTrips($trip, $updatableFields, $request)
    {
        $routeId = $trip->route_id;
        $requestedPolyline = $request->get('overview_polyline');

        // If the route is unchanged, update the trip
        if (is_null($requestedPolyline)) {
            $this->update($trip, $updatableFields, $request);

            return;
        }

        // Retrieve existing polyline for the same route_id
        $existingPolyline = $trip->where('route_id', $routeId)->pluck('overview_polyline')->toArray();

        // Hash each polyline in the requested and existing arrays for efficient comparison
        $hashedRequestedPolyline = $this->hashPolyline($requestedPolyline);
        $hashedExistingPolyline = $this->hashPolyline($existingPolyline);

        // If the route is unchanged, update the trip
        if ($this->isSameRoute($hashedRequestedPolyline, $hashedExistingPolyline)) {
            $this->update($trip, $updatableFields, $request);

            return;
        }

        // Otherwise, replace the old trips with new ones
        $this->replaceTrips($trip, $routeId, $request);
    }

    /**
     * Hashes an array of polyline using crc32
     */
    private function hashPolyline($polyline)
    {
        // Check if the input array is empty before hashing
        if (empty($polyline)) {
            return [];
        }

        return array_map('crc32', $polyline);
    }

    /**
     * Checks if the new route is the same as the existing one.
     */
    private function isSameRoute($hashedRequestedPolyline, $hashedExistingPolyline)
    {
        // Return false if either array is empty or the lengths do not match
        if (count($hashedRequestedPolyline) !== count($hashedExistingPolyline)) {
            return false;
        }

        // Check if the polyline are the same
        return empty(array_diff($hashedRequestedPolyline, $hashedExistingPolyline));
    }

    /**
     * Updates the trip details based on the request data.
     */
    private function update($trip, $updatableFields, $request)
    {
        $updateData = $this->getUpdateData($updatableFields, $request);
        $trip->fill($updateData)->save();
    }

    /**
     * Deletes existing trips for a given route and creates new ones.
     */
    private function replaceTrips($trip, $routeId, $request)
    {
        $trip->where('route_id', $routeId)->delete();
        $this->create($request, $routeId);
    }
}
