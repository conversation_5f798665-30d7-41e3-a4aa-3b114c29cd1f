<?php

namespace App\Services;

use App\Helpers\Common;
use App\Models\Review;
use App\Models\Trip;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserService extends BaseService
{
    protected $user;

    const TOKEN_EXPIRATION_TIME = 15;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Close account.
     */
    public function closeAccount($request)
    {
        DB::beginTransaction();

        $user = Auth::user();

        if (! $user) {
            return $this->responseError(trans('messages.user.error.not_found'));
        }

        try {
            $data = $request->only(['reason_close_account', 'is_recommend_app', 'feedback']);
            $user->update($data);

            // Logout all devices
            $user->tokens()->delete();

            // Soft delete
            $user->delete();

            DB::commit();

            return $this->responseSuccess(trans('messages.user.success.account.closed'), true);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->responseError(trans('messages.user.error.account.close'));
        }
    }

    /**
     * Create a new User record and store it in the database.
     */
    public function create($request)
    {
        $firstName = Arr::get($request, 'first_name');
        $middleName = Arr::get($request, 'middle_name');
        $lastName = Arr::get($request, 'last_name');
        $emailOrPhoneKey = array_key_exists('email', $request) ? 'email' : 'phone';
        $emailOrPhoneValue = Arr::get($request, $emailOrPhoneKey);
        $statusReceiveNoti = Arr::get($request, 'is_receive_noti') ? User::RECEIVE_ALL_NOTI : User::NOT_RECEIVE_NOTI;

        $user = $this->user->create([
            'first_name' => $firstName,
            'middle_name' => $middleName,
            'last_name' => $lastName,
            'full_name' => $this->generateFullName($lastName, $middleName, $firstName),
            $emailOrPhoneKey => $emailOrPhoneValue,
            'birthday' => Arr::get($request, 'birthday'),
            'register_from' => Arr::get($request, 'register_from'),
            'gender' => Arr::get($request, 'gender'),
            'is_noti_promo_email' => $statusReceiveNoti,
            'is_noti_message_email' => $statusReceiveNoti,
            'password' => Hash::make(Arr::get($request, 'password')),
            $emailOrPhoneKey.'_verified_at' => Carbon::now(),
        ]);

        return $user->refresh();
    }

    /**
     * Forget password.
     */
    public function forgotPassword($request)
    {
        $emailOrPhoneKey = array_key_exists('email', $request) ? 'email' : 'phone';
        $emailOrPhoneValue = Arr::get($request, $emailOrPhoneKey);

        // Get the user
        $user = $this->user
            ->where($emailOrPhoneKey, $emailOrPhoneValue)
            ->first();
        if (! $user) {
            return $this->responseError(trans('messages.user.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        // Get the OTP of the user
        $otpCode = $user->otpCode()->where('is_verified', true)->first();
        if (! $otpCode || ($emailOrPhoneValue != $otpCode->$emailOrPhoneKey)) {
            return $this->responseError(trans('messages.user.error.update_password.'.$emailOrPhoneKey));
        }

        // Change password
        $user->update(['password' => Hash::make(Arr::get($request, 'password'))]);

        // Delete the OTP code
        $otpCode->delete();

        return $this->responseSuccess(trans('messages.user.success.forget_password'));
    }

    /**
     * Get reviews.
     */
    public function getReviews($request, $userId)
    {
        $type = $request->get('type');
        $data = [];

        switch ($type) {
            case Review::REVIEW_GIVEN:
                $data = $this->getReviewGivenList($userId);
                break;
            case Review::REVIEW_RECEIVED:
                $data = $this->getReviewReceivedList($userId);
                break;
        }

        return $this->responseSuccess(trans('messages.review.success.get_list'), $data);
    }

    /**
     * Get user information.
     */
    public function getUserInfo($userId)
    {
        $user = User::query()
            ->withCount('reviewGiven as rating_count')
            ->withAvg('reviewReceived as rating_avg', 'rating')
            ->withCount(['trips as completed_trip_count' => function ($q) {
                $q->where('trips.status', Trip::STATUS_FINISHED);
            }])
            ->find($userId);

        if (! $user) {
            return $this->responseError(trans('messages.user.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        // When the user is not the authenticated user, hide some personal information
        if ($userId != Auth::id()) {
            $user->makeHidden(['email', 'phone', 'is_recommend_app', 'reason_close_account', 'feedback', 'deleted_at']);
        }

        return $this->responseSuccess(trans('messages.user.success.get'), $user);
    }

    /**
     * Update password.
     */
    public function updatePassword($request, $user)
    {
        // Check the current password
        if (! Hash::check($request->current_password, $user->password)) {
            return $this->responseError(trans('messages.user.error.update_password.current_password'));
        }

        // Change password
        $password = Hash::make($request->password);
        $this->updatePersonalInfo($request, $user, 'password', $password);

        return $this->responseSuccess(trans('messages.user.success.update.password'));
    }

    /**
     * Update profile.
     */
    public function updateProfile($request)
    {
        $user = Auth::user();

        $keys = [
            'avatar_url' => 'updateAvatarUrl',
            'identity_card' => 'addIdentityCard',
            'preference' => 'updatePreference',
            'first_name' => 'updateUserName',
            'birthday' => 'updatePersonalInfo',
            'bio' => 'updatePersonalInfo',
            'is_noti_promo_email' => 'updateNotiViaEmail',
        ];

        foreach ($keys as $key => $function) {
            if ($request->has($key) && method_exists($this, $function)) {
                $user = $this->$function($request, $user, $key);
                break;
            }
        }

        return $this->responseSuccess(trans('messages.user.success.update.profile'), $user);
    }

    /**
     * Add identity card.
     */
    private function addIdentityCard($request, $user, $key)
    {
        // Only verify one time
        if ($user->is_ID_card_verified == 1) {
            return $this->responseError(trans('messages.user.error.update.identity_card_verified'));
        }

        // Upload the user identity card
        $identityCardUrl = Common::uploadImages($request, $user->id, $key);
        $user->is_ID_card_verified = 0;

        return $this->updatePersonalInfo($request, $user, $key, $identityCardUrl);
    }

    /**
     * Generate full name.
     */
    private function generateFullName(string $lastName, ?string $middleName, string $firstName)
    {
        $name_parts = [$lastName, $middleName ? $middleName : null, $firstName];

        return implode(' ', array_filter($name_parts));
    }

    /**
     * Get review given list.
     */
    private function getReviewGivenList($userId)
    {
        $reviewerInfo = User::select('id', 'full_name', 'avatar_url', 'is_avatar_verified', 'is_ID_card_verified', 'bio', 'birthday', 'preference', 'gender', 'email_verified_at', 'phone_verified_at')
            ->find($userId);

        if (! $reviewerInfo) {
            return $this->responseError(trans('messages.user.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        $reviewList = $reviewerInfo
            ->reviewGiven()
            ->with('reviewee:id,full_name,avatar_url,gender,is_ID_card_verified')
            ->orderByDesc('created_at')
            ->get();

        $totalReviews = $reviewList->count();
        $ratingCount = $reviewList->countBy('rating')->toArray();
        $ratingStars = [];
        for ($i = 5; $i >= 1; $i--) {
            $ratingStars[$i] = $ratingCount[$i] ?? 0;
        }

        $ratingAvg = $reviewList->avg('rating') ?: 0;

        return [
            'reviewer_info' => $reviewerInfo,
            'total_reviews' => $totalReviews,
            'rating_star' => $ratingStars,
            'rating_avg' => $ratingAvg,
            'review_list' => $reviewList,
        ];
    }

    /**
     * Get review received list.
     */
    private function getReviewReceivedList($userId)
    {
        $revieweeInfo = User::select('id', 'full_name', 'avatar_url', 'is_avatar_verified', 'is_ID_card_verified', 'bio', 'birthday', 'preference', 'gender', 'email_verified_at', 'phone_verified_at')
            ->find($userId);

        if (! $revieweeInfo) {
            return $this->responseError(trans('messages.user.error.not_found'), Response::HTTP_NOT_FOUND);
        }

        $reviewList = $revieweeInfo
            ->reviewReceived()
            ->with('reviewer:id,full_name,avatar_url,gender,is_ID_card_verified')
            ->orderByDesc('created_at')
            ->get();

        $totalReviews = $reviewList->count();
        $ratingCount = $reviewList->countBy('rating')->toArray();
        $ratingStars = [];
        for ($i = 5; $i >= 1; $i--) {
            $ratingStars[$i] = $ratingCount[$i] ?? 0;
        }

        $ratingAvg = $reviewList->avg('rating') ?: 0;

        return [
            'reviewee_info' => $revieweeInfo,
            'total_reviews' => $totalReviews,
            'rating_star' => $ratingStars,
            'rating_avg' => $ratingAvg,
            'review_list' => $reviewList,
        ];
    }

    /**
     * Update avatar url.
     */
    private function updateAvatarUrl($request, $user, $key)
    {
        $avatarUrl = Common::uploadImages($request, $user->id, $key);
        $user->is_avatar_verified = 0;

        return $this->updatePersonalInfo($request, $user, $key, $avatarUrl[0]);
    }

    /**
     * Update notification via email.
     */
    private function updateNotiViaEmail($request, $user, $key)
    {
        if (! $user->email_verified_at) {
            return $this->responseError(trans('messages.user.error.update.email_verified_at'));
        }

        // Keys can be updated in this function
        $updateFields = [
            'is_noti_promo_email',
            'is_noti_message_email',
        ];

        if (! $request->has($updateFields)) {
            return $this->responseError(trans('messages.user.error.update.noti'));
        }

        $filteredData = $request->only($updateFields);
        $user->update($filteredData);

        return $user;
    }

    /**
     * Update personal info.
     */
    private function updatePersonalInfo($request, $user, $key, $value = null)
    {
        $user->$key = is_null($value) ? $request->$key : $value;
        $user->save();
        $user->refresh();

        return $user;
    }

    /**
     * Update preference.
     */
    private function updatePreference($request, $user, $key)
    {
        $preference = $request->$key;
        $preferenceKeys = User::PREFERENCE_KEY;

        $data = [];
        foreach ($preferenceKeys as $preferenceKey) {
            $data[] = $preference[$preferenceKey];
        }

        // validate data
        $value = implode(',', $data);
        $pattern = '/^[1-3],[4-6],[7-9],[1-9][0-2]$/';
        if (! preg_match($pattern, $value)) {
            return $this->responseError(
                trans('messages.user.error.update.preference'),
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        return $this->updatePersonalInfo($request, $user, $key, $value);
    }

    /**
     * Update user name.
     */
    private function updateUserName($request, $user, $key)
    {
        // Keys can be updated in this function
        $updateFields = ['last_name', 'middle_name', 'first_name'];
        if (! $request->has($updateFields)) {
            return $this->responseError(trans('messages.user.error.update.name'));
        }
        $updateFields = [...$updateFields, 'full_name'];

        $request->merge([
            'full_name' => $this->generateFullName(
                $request->last_name,
                $request->middle_name,
                $request->first_name
            ),
        ]);

        $filteredData = $request->only($updateFields);
        $user->update($filteredData);

        return $user;
    }
}
