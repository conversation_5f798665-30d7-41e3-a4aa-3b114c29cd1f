### 📌 Naming Conventions for Service Classes

#### ✅ Structure

Service class names should follow this directory and naming structure:

```
|---- Services
|----|---- ${Model}
|----|----|---- ${ActionName}.php
```

#### 📖 Example

For a `User` model, service classes should be structured as follows:

```
|---- Services
|----|---- User
|----|----|---- RegisterUser.php
|----|----|---- UpdateUserProfile.php
|----|----|---- DeleteUser.php
```

#### 🔹 Naming Guidelines

-   Use **PascalCase** for service class names.
-   The class name should clearly indicate the **action** it performs.
-   Group service classes inside a folder named after the **model** they relate to.

#### 🔍 Best Practices

-   **Do**: Keep class names **short, clear, and action-oriented**.
-   **Avoid**: Generic names like `UserService.php`. Instead, break it down into multi
