<?php

namespace App\Services;

use App\Models\Trip;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Support\Facades\DB;

class VehicleService extends BaseService
{
    protected $vehicle;

    public function __construct(Vehicle $vehicle)
    {
        $this->vehicle = $vehicle;
    }

    /**
     * Create a new vehicle for user and store it in the database.
     */
    public function create($request, $userId)
    {
        $seatNumber = $request->get('type') == Vehicle::TYPE_MOTOR
            ? Vehicle::NUMBER_OF_SEATS_MOTOR
            : Vehicle::NUMBER_OF_SEATS_CAR;

        $vehicle = $this->vehicle->create([
            'type' => $request->get('type'),
            'brand' => $request->get('brand'),
            'model' => $request->get('model'),
            'color' => $request->get('color'),
            'user_id' => $userId,
            'number_of_seats' => $seatNumber,
            'license_plate' => $request->get('license_plate'),
        ]);

        return Vehicle::find($vehicle->id);
    }

    /**
     * Create a new vehicle for user and store it in the database.
     */
    public function createVehicle($request, $user)
    {
        // Create a motorbike or car
        $data = $this->create($request, $user->id);

        // Update user information
        $type = $request->get('type');
        if ($data) {
            switch ($type) {
                case 1:
                    $user->update(['has_motor' => true]);
                    break;
                case 2:
                    $user->update(['has_car' => true]);
                    break;

                default:
                    break;
            }
        }

        return $this->responseSuccess(trans('messages.vehicle.success.create'), $data);
    }

    /**
     * Delete a new vehicle from the database.
     */
    public function deleteVehicle($user, $id)
    {
        if ($this->isUsedCreateTrip($id)) {
            return $this->responseError(trans('messages.vehicle.error.delete'));
        }

        DB::transaction(function () use ($user, $id) {
            $vehicle = $user->vehicles()->findOrFail($id);
            $vehicle->delete();
        });

        return $this->responseSuccess(trans('messages.vehicle.success.delete'), true);
    }

    /**
     * Get vehicle of user.
     */
    public function getVehicles($user)
    {
        $data = $user->vehicles()->withCount('trips')->get();

        $data->map(function ($vehicle) {
            $vehicle->can_deletable = $vehicle->trips_count == 0;

            return $vehicle;
        });

        return $this->responseSuccess(trans('messages.vehicle.success.get'), $data);
    }

    /**
     * Check the vehicle that was used to create the trip.
     */
    private function isUsedCreateTrip($id)
    {
        return Trip::where('vehicle_id', $id)->exists();
    }
}
