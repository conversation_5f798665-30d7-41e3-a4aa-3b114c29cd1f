<?php

namespace App\Services;

use App\Helpers\Format;
use App\Models\User;
use App\Models\UserTemp;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;

class AuthService extends BaseService
{
    protected $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Create a new User record and store it in the database.
     */
    public function create($request)
    {
        $firstName = $request->first_name;
        $middleName = $request->middle_name;
        $lastName = $request->last_name;
        $emailOrPhoneKey = $request->has('email') ? 'email' : 'phone';
        $emailOrPhoneValue = $request->{$emailOrPhoneKey};
        $statusReceiveNoti = $request->is_receive_noti ? User::RECEIVE_ALL_NOTI : User::NOT_RECEIVE_NOTI;

        $user = $this->user->create([
            'first_name' => $firstName,
            'middle_name' => $middleName,
            'last_name' => $lastName,
            'full_name' => $this->generateFullName($lastName, $middleName, $firstName),
            $emailOrPhoneKey => $emailOrPhoneValue,
            'birthday' => $request->birthday,
            'register_from' => $request->register_from,
            'gender' => $request->gender,
            'is_noti_promo_email' => $statusReceiveNoti,
            'is_noti_message_email' => $statusReceiveNoti,
            'password' => Hash::make($request->password),
            $emailOrPhoneKey.'_verified_at' => Carbon::now(),
        ]);

        return $user->refresh();
    }

    /**
     * Get the authenticated user's information.
     */
    public function getAuthUserInfo()
    {
        $user = Auth::user();

        return $this->responseSuccess(trans('messages.auth.success.user'), $user);
    }

    /**
     * Login.
     */
    public function login($request)
    {
        $username = $request->username;
        $password = $request->password;

        $user = $this->user
            ->where('email', $username)
            ->orWhere('phone', Format::formatPhone($username))
            ->first();

        if (! $user || ! Hash::check($password, $user->password)) {
            return $this->responseError(trans('messages.auth.error.login'), Response::HTTP_BAD_REQUEST);
        }

        $data = $this->createAccessToken($user);
        // $data = $this->createAccessToken($request);
        if (! $data) {
            return $this->responseError(trans('messages.auth.error.create_token'));
        }

        // $data['user'] = $user;

        return $this->responseSuccess(trans('messages.auth.success.login'), $data);
    }

    /**
     * Logout.
     */
    public function logout()
    {
        $data = auth()->user()->token()->revoke();

        return $this->responseSuccess(trans('messages.auth.success.logout'), $data);
    }

    /**
     * Refresh token.
     */
    public function refreshToken()
    {
        $refreshToken = str_replace('Bearer ', '', request()->header('Authorization', ''));

        request()->request->add([
            'grant_type' => 'refresh_token',
            'client_id' => env('PASSWORD_GRANT_CLIENT_ID'),
            'client_secret' => env('PASSWORD_GRANT_CLIENT_SECRET'),
            'refresh_token' => $refreshToken,
            'scope' => '',
        ]);
        $request = Request::create(config('app.url').'/oauth/token', 'POST');
        $response = Route::dispatch($request);

        if ($response->getStatusCode() !== Response::HTTP_OK) {
            return $this->responseError(trans('messages.auth.error.refresh_token'));
        }

        return $this->responseSuccess(
            trans('messages.auth.success.refresh_token'),
            json_decode($response->getContent(), true)
        );
    }

    /**
     * Register.
     */
    public function register($request)
    {
        $emailOrPhoneKey = $request->has('email') ? 'email' : 'phone';
        $emailOrPhoneValue = $request->{$emailOrPhoneKey};

        // Get the temporary user in DB
        $userTemp = UserTemp::where($emailOrPhoneKey, $emailOrPhoneValue)
            ->where('is_verified', true)
            ->first();

        // Check if there is a verified temporary user with the provided email or phone number
        if (! $userTemp) {
            return $this->responseError(trans('messages.auth.error.register.'.$emailOrPhoneKey));
        }

        // Create a new user
        $user = $this->create($request);

        // Delete the temporary user
        $userTemp->delete();

        // Create parameters to create access token
        $params = [
            'username' => $emailOrPhoneValue,
            'password' => $request->password,
        ];
        $data = $this->createAccessToken($params);
        if (! $data) {
            return $this->responseError(trans('messages.auth.error.create_token'));
        }

        $data['user'] = $user;

        return $this->responseSuccess(trans('messages.auth.success.register'), $data);
    }

    /**
     * Generate full name.
     */
    private function generateFullName(string $lastName, ?string $middleName, string $firstName)
    {
        $name_parts = [$lastName, $middleName ? $middleName : null, $firstName];

        return implode(' ', array_filter($name_parts));
    }

    /**
     * Create access token.
     */
    private function createAccessToken($user)
    {
        $tokenResult = $user->createToken('Auth login');
        $tokenExpiration = Carbon::now()->addDays(15);
        $token = $tokenResult->token;
        $token->expires_at = $tokenExpiration;
        $token->save();

        return [
            'user' => $user,
            'access_token' => $tokenResult->accessToken,
            'token_type' => 'Bearer',
            'expires_at' => $tokenExpiration,
        ];
    }
    // private function createAccessToken($user, $params)
    // {
    //     request()->request->add([
    //         'grant_type' => 'password',
    //         'client_id' => env('PASSWORD_GRANT_CLIENT_ID'),
    //         'client_secret' => env('PASSWORD_GRANT_CLIENT_SECRET'),
    //         'username' => $params['username'],
    //         'password' => $params['password'],
    //         'scope' => '',
    //     ]);
    //     $request = Request::create(config('app.url') . '/oauth/token', 'POST');
    //     $response = Route::dispatch($request);

    //     if ($response->status() !== Response::HTTP_OK) {
    //         return [];
    //     }

    //     return json_decode($response->getContent(), true);
    // }
}
