<?php
return [
    /*
    |--------------------------------------------------------------------------
    | Custom Validation Messages
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */
    'attributes' => [
        // User
        'avatar_url'                => 'ảnh đại diện',
        'bio'                       => 'giới thiệu',
        'birthday'                  => 'ngày sinh',
        'current_password'          => 'mật khẩu hiện tại',
        'email'                     => 'email',
        'feedback'                  => 'phản hồi của khách hàng',
        'first_name'                => 'tên',
        'gender'                    => 'giới tính',
        'identity_card'             => 'CCCD',
        'is_recommend_app'          => 'giới thiệu ứng dụng',
        'last_name'                 => 'họ',
        'middle_name'               => 'tên đệm',
        'otp'                       => 'mã OTP',
        'password'                  => 'mật khẩu',
        'phone'                     => 'số điện thoại',
        'preference'                => 'sở thích',
        'preference.*'              => 'sở thích',
        'reason_close_account'      => 'lý do đóng tài khoản',
        'username'                  => 'tên đăng nhập',

        // Vehicle
        'brand'                     => 'hãng xe',
        'color'                     => 'màu xe',
        'license_plate'             => 'biển số xe',
        'model'                     => 'mẫu xe',
        'number_of_seats'           => 'số chỗ ngồi',
        'type'                      => 'loại xe',

        // Trip
        'completion_times'          => 'thời gian hoàn thành chuyến đi dự kiến',
        'completion_times.*'        => 'thời gian hoàn thành chuyến đi dự kiến',
        'date'                      => 'ngày đi',
        'distances'                 => 'khoảng cách chuyến đi',
        'distances.*'               => 'khoảng cách chuyến đi',
        'max_seat'                  => 'số chỗ ngồi',
        'overview_polyline'         => 'thông tin lộ trình',
        'overview_polyline.*'       => 'thông tin lộ trình',
        'points'                    => 'danh sách các địa điểm',
        'points.*.address_name'     => 'tên địa điểm',
        'points.*.city_name'        => 'chi tiết địa điểm',
        'points.*.latitude'         => 'tọa độ latitude của điểm đi(đến)',
        'points.*.longitude'        => 'tọa độ longitude của điểm đi(đến)',
        'prices'                    => 'giá chuyến đi',
        'prices.*'                  => 'giá chuyến đi',
        'time'                      => 'giờ đi',

        'cond' => [
            'departure_city'        => 'tỉnh/thành phố',
            'departure_date'        => 'ngày xuất phát',
            'departure_latitude'    => 'tọa độ latitude của điểm đi',
            'departure_longitude'   => 'tọa độ longitude của điểm đi',
            'destination_city'      => 'tỉnh/thành phố',
            'destination_latitude'  => 'tọa độ latitude của điểm đến',
            'destination_longitude' => 'tọa độ latitude của điểm đến',
            'max_seat'              => 'số chỗ ngồi',
        ],

        // Related trips
        'departure_city'            => 'tỉnh/thành phố khởi hành',
        'departure_date'            => 'ngày xuất phát',
        'destination_city'          => 'tỉnh/thành phố đến',
        'vehicle_type'              => 'loại phương tiện',

        // Reviews
        'comment'                   => 'nội dung đánh giá',
        'rating'                    => 'điểm đánh giá',
        'reservation_id'            => 'booking ID',
        'reviewee_id'               => 'ID của tài xế',
    ],


    'custom' => [
        // Users
        'avatar_url' => [
            'mimes'                 => 'Trường :attribute phải thuộc loại jpeg, png, jpg.',
            'max'                   => 'Kích thước :attribute tối đa là :max MB.',
        ],
        'bio' => [
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'birthday' => [
            'before'                => 'Độ tuổi giới hạn yêu cầu là 16 tuổi',
            'date'                  => 'Trường :attribute phải là ngày hợp lệ.',
            'date_format'           => 'Trường :attribute phải có định dạng Y-m-d.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'brand' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'color' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'comment' => [
            'required'              => 'Trường :attribute là bắt buộc',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'completion_times' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'array'                 => 'Trường :attribute phải là mảng.',
        ],
        'completion_times.*' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'integer'               => 'Trường :attribute phải là số nguyên.',
        ],
        'cond' => [
            'departure_city' => [
                'max'               => 'Trường :attribute tối đa là :max ký tự.',
                'required'          => 'Trường :attribute là bắt buộc',
                'string'            => 'Trường :attribute phải là chuỗi ký tự.',
            ],
            'departure_date' => [
                'after_or_equal'    => 'Trường :attribute phải lớn hơn hoặc bằng ngày hiện tại.',
                'date'              => 'Trường :attribute phải là ngày hợp lệ.',
                'date_format'       => 'Trường :attribute phải có định dạng Y-m-d.',
                'required'          => 'Trường :attribute là bắt buộc',
            ],
            'departure_latitude' => [
                'required'          => 'Trường :attribute là bắt buộc',
                'string'            => 'Trường :attribute phải là chuỗi ký tự.',
            ],
            'departure_longitude' => [
                'required'          => 'Trường :attribute là bắt buộc',
                'string'            => 'Trường :attribute phải là chuỗi ký tự.',
            ],
            'destination_city' => [
                'max'               => 'Trường :attribute tối đa là :max ký tự.',
                'required'          => 'Trường :attribute là bắt buộc',
                'string'            => 'Trường :attribute phải là chuỗi ký tự.',
            ],
            'destination_latitude' => [
                'required'          => 'Trường :attribute là bắt buộc',
                'string'            => 'Trường :attribute phải là chuỗi ký tự.',
            ],
            'destination_longitude' => [
                'required'          => 'Trường :attribute là bắt buộc',
                'string'            => 'Trường :attribute phải là chuỗi ký tự.',
            ],
            'max_seat' => [
                'integer'           => 'Trường :attribute phải là số nguyên.',
                'required'          => 'Trường :attribute là bắt buộc',
            ],
            'sort_by' => [
                'required'          => 'Trường :attribute là bắt buộc',
                'string'            => 'Trường :attribute phải là chuỗi ký tự.',
            ],
        ],
        'current_password' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'date' => [
            'date'                  => 'Trường :attribute phải là ngày hợp lệ.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'distances' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'array'                 => 'Trường :attribute phải là mảng.',
        ],
        'distances.*' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'integer'               => 'Trường :attribute phải là số nguyên.',
        ],
        'email' => [
            'email'                 => 'Trường :attribute phải là địa chỉ email hợp lệ.',
            'exists'                => 'Trường :attribute này chưa được đăng ký tài khoản.',
            'max'                   => 'Độ dài :attribute không được vượt quá 255 ký tự.',
            'required'              => 'Trường :attribute là bắt buộc.',
            'required_without'      => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
            'unique'                => 'Trường :attribute này đã được sử dụng. Vui lòng đăng nhập.',
        ],
        'feedback' => [
            'required'              => 'Trường :attribute là bắt buộc',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'first_name' => [
            'max'                   => 'Độ dài :attribute không được vượt quá 255 ký tự.',
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'gender' => [
            'in'                    => 'Trường :attribute phải là male hoặc female.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'identity_card' => [
            'array'                 => 'Trường :attribute phải là mảng.',
            'size'                  => 'Trường :attribute phải nộp đủ 2 hình gồm mặt trước và mặt sau.',
        ],
        'identity_card.*' => [
            'mimes'                 => 'Trường :attribute phải thuộc loại jpeg, png, jpg.',
            'max'                   => 'Kích thước :attribute tối đa là :max MB.',
        ],
        'is_receive_noti' => [
            'boolean'               => 'Trường :attribute phải là true hoặc false.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'last_name' => [
            'max'                   => 'Độ dài :attribute không được vượt quá 255 ký tự.',
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'license_plate' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
            'unique'                => 'Biển số xe này đã được đăng ký.',
        ],
        'max_seat' => [
            'between'               => 'Trường :attribute phải nằm trong khoảng từ 1 đến 4.',
            'integer'               => 'Trường :attribute phải là số nguyên.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'middle_name' => [
            'max'                   => 'Độ dài :attribute không được vượt quá 255 ký tự.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'model' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'otp' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'size'                  => 'Trường :attribute bao gồm 6 số.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'password' => [
            'confirmed'             => 'Trường :attribute xác nhận không khớp.',
            'max'                   => 'Trường :attribute tối đa có :max ký tự.',
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
            'strong_password'       => 'Trường :attribute phải ít nhất 6 ký tự, bao gồm chữ hoa, số, và ký tự đặc biệt.'
        ],
        'phone' => [
            'exists'                => 'Trường :attribute này chưa được đăng ký tài khoản.',
            'phone_number'          => 'Định dạng :attribute không đúng.',
            'required'              => 'Trường :attribute là bắt buộc.',
            'required_without'      => 'Trường :attribute là bắt buộc.',
            'unique'                => 'Trường :attribute này đã được sử dụng. Vui lòng đăng nhập.',
        ],
        'points' => [
            'array'                 => 'Trường :attribute phải là mảng.',
            'min'                   => 'Trường :attribute phải có độ dài nhỏ nhất là :min.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'points.*.address_name' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'points.*.city_name' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'points.*.latitude' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'points.*.longitude' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'preference' => [
            'regex'                 => 'Trường :attribute có giá trị không hợp lệ!.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'prices' => [
            'array'                 => 'Trường :attribute phải là mảng.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'prices.*' => [
            'integer'               => 'Trường :attribute phải là số nguyên.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'rating' => [
            'integer'               => 'Trường :attribute phải là số nguyên.',
            'max'                   => 'Trường :attribute tối đa là :max.',
            'required'              => 'Trường :attribute là bắt buộc',
        ],
        'reason_close_account' => [
            'required'              => 'Trường :attribute là bắt buộc',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'refresh_token' => [
            'required'              => 'Trường :attribute là bắt buộc',
        ],
        'register_from' => [
            'in'                    => 'Trường :attribute phải là web hoặc app.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'reviewee_id' => [
            'required'              => 'Trường :attribute là bắt buộc',
        ],
        'status' => [
            'integer'               => 'Trường :attribute phải là số.',
        ],
        'time' => [
            'after'                 => 'Trường :attribute phải bắt đầu sau 15 phút.',
            'date_format'           => 'Trường :attribute phải có định dạng H:i.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'type' => [
            'in'                    => 'Trường :attribute phải là 1 hoặc 2.',
            'required'              => 'Trường :attribute là bắt buộc.',
        ],
        'username' => [
            'required'              => 'Trường :attribute là bắt buộc.',
            'string'                => 'Trường :attribute phải là chuỗi ký tự.',
        ],
        'vehicle_type' => [
            'in'                => 'Trường :attribute phải là 1 hoặc 2.',
            'integer'           => 'Trường :attribute phải là số nguyên.',
            'required'          => 'Trường :attribute là bắt buộc',
        ],
    ],
];
